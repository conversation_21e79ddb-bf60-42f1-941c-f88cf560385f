/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "(ssr)/./src/hooks/utils.ts":
/*!****************************!*\
  !*** ./src/hooks/utils.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _langchain_langgraph_sdk__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @langchain/langgraph-sdk */ \"(ssr)/../../node_modules/@langchain/langgraph-sdk/index.js\");\n\nconst createClient = ()=>{\n    const apiUrl = process.env.NEXT_PUBLIC_API_URL ?? \"http://localhost:3000/api\";\n    return new _langchain_langgraph_sdk__WEBPACK_IMPORTED_MODULE_0__.Client({\n        apiUrl\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBa0Q7QUFFM0MsTUFBTUMsZUFBZTtJQUMxQixNQUFNQyxTQUFTQyxRQUFRQyxHQUFHLENBQUNDLG1CQUFtQixJQUFJO0lBQ2xELE9BQU8sSUFBSUwsNERBQU1BLENBQUM7UUFDaEJFO0lBQ0Y7QUFDRixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4vc3JjL2hvb2tzL3V0aWxzLnRzPzkwNTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ2xpZW50IH0gZnJvbSBcIkBsYW5nY2hhaW4vbGFuZ2dyYXBoLXNka1wiO1xyXG5cclxuZXhwb3J0IGNvbnN0IGNyZWF0ZUNsaWVudCA9ICgpID0+IHtcclxuICBjb25zdCBhcGlVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMID8/IFwiaHR0cDovL2xvY2FsaG9zdDozMDAwL2FwaVwiO1xyXG4gIHJldHVybiBuZXcgQ2xpZW50KHtcclxuICAgIGFwaVVybCxcclxuICB9KTtcclxufTtcclxuIl0sIm5hbWVzIjpbIkNsaWVudCIsImNyZWF0ZUNsaWVudCIsImFwaVVybCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUElfVVJMIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/workers/graph-stream/stream.worker.ts":
/*!***************************************************!*\
  !*** ./src/workers/graph-stream/stream.worker.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _hooks_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/hooks/utils */ \"(ssr)/./src/hooks/utils.ts\");\n\n// Since workers can't directly access the client SDK, you'll need to recreate/import necessary parts\nconst ctx = self;\nctx.addEventListener(\"message\", async (event)=>{\n    try {\n        const { threadId, assistantId, input, modelName, modelConfigs } = event.data;\n        const client = (0,_hooks_utils__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const stream = client.runs.stream(threadId, assistantId, {\n            input: input,\n            streamMode: \"events\",\n            config: {\n                configurable: {\n                    customModelName: modelName,\n                    modelConfig: modelConfigs[modelName]\n                }\n            }\n        });\n        for await (const chunk of stream){\n            // Serialize the chunk and post it back to the main thread\n            ctx.postMessage({\n                type: \"chunk\",\n                data: JSON.stringify(chunk)\n            });\n        }\n        ctx.postMessage({\n            type: \"done\"\n        });\n    } catch (error) {\n        ctx.postMessage({\n            type: \"error\",\n            error: error.message\n        });\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/workers/graph-stream/stream.worker.ts\n");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			id: moduleId,
/******/ 			loaded: false,
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		var threw = true;
/******/ 		try {
/******/ 			__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 			threw = false;
/******/ 		} finally {
/******/ 			if(threw) delete __webpack_module_cache__[moduleId];
/******/ 		}
/******/ 	
/******/ 		// Flag the module as loaded
/******/ 		module.loaded = true;
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = __webpack_modules__;
/******/ 	
/******/ 	// the startup function
/******/ 	__webpack_require__.x = () => {
/******/ 		// Load entry module and return exports
/******/ 		// This entry module depends on other loaded chunks and execution need to be delayed
/******/ 		var __webpack_exports__ = __webpack_require__.O(undefined, ["vendor-chunks/@langchain","vendor-chunks/p-queue","vendor-chunks/eventemitter3","vendor-chunks/retry","vendor-chunks/p-retry","vendor-chunks/p-timeout","vendor-chunks/p-finally"], () => (__webpack_require__("(ssr)/./src/workers/graph-stream/stream.worker.ts")))
/******/ 		__webpack_exports__ = __webpack_require__.O(__webpack_exports__);
/******/ 		return __webpack_exports__;
/******/ 	};
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/chunk loaded */
/******/ 	(() => {
/******/ 		var deferred = [];
/******/ 		__webpack_require__.O = (result, chunkIds, fn, priority) => {
/******/ 			if(chunkIds) {
/******/ 				priority = priority || 0;
/******/ 				for(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];
/******/ 				deferred[i] = [chunkIds, fn, priority];
/******/ 				return;
/******/ 			}
/******/ 			var notFulfilled = Infinity;
/******/ 			for (var i = 0; i < deferred.length; i++) {
/******/ 				var [chunkIds, fn, priority] = deferred[i];
/******/ 				var fulfilled = true;
/******/ 				for (var j = 0; j < chunkIds.length; j++) {
/******/ 					if ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {
/******/ 						chunkIds.splice(j--, 1);
/******/ 					} else {
/******/ 						fulfilled = false;
/******/ 						if(priority < notFulfilled) notFulfilled = priority;
/******/ 					}
/******/ 				}
/******/ 				if(fulfilled) {
/******/ 					deferred.splice(i--, 1)
/******/ 					var r = fn();
/******/ 					if (r !== undefined) result = r;
/******/ 				}
/******/ 			}
/******/ 			return result;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/ensure chunk */
/******/ 	(() => {
/******/ 		__webpack_require__.f = {};
/******/ 		// This file contains only the entry chunk.
/******/ 		// The chunk loading function for additional chunks
/******/ 		__webpack_require__.e = (chunkId) => {
/******/ 			return Promise.all(Object.keys(__webpack_require__.f).reduce((promises, key) => {
/******/ 				__webpack_require__.f[key](chunkId, promises);
/******/ 				return promises;
/******/ 			}, []));
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/get javascript chunk filename */
/******/ 	(() => {
/******/ 		// This function allow to reference async chunks and sibling chunks for the entrypoint
/******/ 		__webpack_require__.u = (chunkId) => {
/******/ 			// return url for filenames based on template
/******/ 			return "" + chunkId + ".js";
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/node module decorator */
/******/ 	(() => {
/******/ 		__webpack_require__.nmd = (module) => {
/******/ 			module.paths = [];
/******/ 			if (!module.children) module.children = [];
/******/ 			return module;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/require chunk loading */
/******/ 	(() => {
/******/ 		// no baseURI
/******/ 		
/******/ 		// object to store loaded chunks
/******/ 		// "1" means "loaded", otherwise not loaded yet
/******/ 		var installedChunks = {
/******/ 			"_ssr_src_workers_graph-stream_stream_worker_ts": 1
/******/ 		};
/******/ 		
/******/ 		__webpack_require__.O.require = (chunkId) => (installedChunks[chunkId]);
/******/ 		
/******/ 		var installChunk = (chunk) => {
/******/ 			var moreModules = chunk.modules, chunkIds = chunk.ids, runtime = chunk.runtime;
/******/ 			for(var moduleId in moreModules) {
/******/ 				if(__webpack_require__.o(moreModules, moduleId)) {
/******/ 					__webpack_require__.m[moduleId] = moreModules[moduleId];
/******/ 				}
/******/ 			}
/******/ 			if(runtime) runtime(__webpack_require__);
/******/ 			for(var i = 0; i < chunkIds.length; i++)
/******/ 				installedChunks[chunkIds[i]] = 1;
/******/ 			__webpack_require__.O();
/******/ 		};
/******/ 		
/******/ 		// require() chunk loading for javascript
/******/ 		__webpack_require__.f.require = (chunkId, promises) => {
/******/ 			// "1" is the signal for "already loaded"
/******/ 			if(!installedChunks[chunkId]) {
/******/ 				if(true) { // all chunks have JS
/******/ 					installChunk(require("./" + __webpack_require__.u(chunkId)));
/******/ 				} else installedChunks[chunkId] = 1;
/******/ 			}
/******/ 		};
/******/ 		
/******/ 		// no external install chunk
/******/ 		
/******/ 		// no HMR
/******/ 		
/******/ 		// no HMR manifest
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/startup chunk dependencies */
/******/ 	(() => {
/******/ 		var next = __webpack_require__.x;
/******/ 		__webpack_require__.x = () => {
/******/ 			__webpack_require__.e("vendor-chunks/@langchain");
/******/ 			__webpack_require__.e("vendor-chunks/p-queue");
/******/ 			__webpack_require__.e("vendor-chunks/eventemitter3");
/******/ 			__webpack_require__.e("vendor-chunks/retry");
/******/ 			__webpack_require__.e("vendor-chunks/p-retry");
/******/ 			__webpack_require__.e("vendor-chunks/p-timeout");
/******/ 			__webpack_require__.e("vendor-chunks/p-finally");
/******/ 			return next();
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// run startup
/******/ 	var __webpack_exports__ = __webpack_require__.x();
/******/ 	module.exports = __webpack_exports__;
/******/ 	
/******/ })()
;