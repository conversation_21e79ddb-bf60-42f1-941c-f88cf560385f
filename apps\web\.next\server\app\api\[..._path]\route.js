"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/[..._path]/route";
exports.ids = ["app/api/[..._path]/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2F%5B..._path%5D%2Froute&page=%2Fapi%2F%5B..._path%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2F%5B..._path%5D%2Froute.ts&appDir=D%3A%5CStudy%5Ccreat%5Copen-canvas%5C%E7%AC%AC%E4%B8%80%5Copen-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CStudy%5Ccreat%5Copen-canvas%5C%E7%AC%AC%E4%B8%80%5Copen-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2F%5B..._path%5D%2Froute&page=%2Fapi%2F%5B..._path%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2F%5B..._path%5D%2Froute.ts&appDir=D%3A%5CStudy%5Ccreat%5Copen-canvas%5C%E7%AC%AC%E4%B8%80%5Copen-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CStudy%5Ccreat%5Copen-canvas%5C%E7%AC%AC%E4%B8%80%5Copen-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Study_creat_open_canvas_open_canvas_apps_web_src_app_api_path_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/[..._path]/route.ts */ \"(rsc)/./src/app/api/[..._path]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/[..._path]/route\",\n        pathname: \"/api/[..._path]\",\n        filename: \"route\",\n        bundlePath: \"app/api/[..._path]/route\"\n    },\n    resolvedPagePath: \"D:\\\\Study\\\\creat\\\\open-canvas\\\\第一\\\\open-canvas\\\\apps\\\\web\\\\src\\\\app\\\\api\\\\[..._path]\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Study_creat_open_canvas_open_canvas_apps_web_src_app_api_path_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/[..._path]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2F%5B..._path%5D%2Froute&page=%2Fapi%2F%5B..._path%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2F%5B..._path%5D%2Froute.ts&appDir=D%3A%5CStudy%5Ccreat%5Copen-canvas%5C%E7%AC%AC%E4%B8%80%5Copen-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CStudy%5Ccreat%5Copen-canvas%5C%E7%AC%AC%E4%B8%80%5Copen-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/[..._path]/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/[..._path]/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../constants */ \"(rsc)/./src/constants.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/../../node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_verify_user_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/supabase/verify_user_server */ \"(rsc)/./src/lib/supabase/verify_user_server.ts\");\n\n\n\nfunction getCorsHeaders() {\n    return {\n        \"Access-Control-Allow-Origin\": \"*\",\n        \"Access-Control-Allow-Methods\": \"GET, POST, PUT, PATCH, DELETE, OPTIONS\",\n        \"Access-Control-Allow-Headers\": \"*\"\n    };\n}\nasync function handleRequest(req, method) {\n    let session;\n    let user;\n    try {\n        const authRes = await (0,_lib_supabase_verify_user_server__WEBPACK_IMPORTED_MODULE_2__.verifyUserAuthenticated)();\n        session = authRes?.session;\n        user = authRes?.user;\n        if (!session || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n    } catch (e) {\n        console.error(\"Failed to fetch user\", e);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            error: \"Unauthorized\"\n        }, {\n            status: 401\n        });\n    }\n    try {\n        const path = req.nextUrl.pathname.replace(/^\\/?api\\//, \"\");\n        const url = new URL(req.url);\n        const searchParams = new URLSearchParams(url.search);\n        searchParams.delete(\"_path\");\n        searchParams.delete(\"nxtP_path\");\n        const queryString = searchParams.toString() ? `?${searchParams.toString()}` : \"\";\n        const options = {\n            method,\n            headers: {\n                \"x-api-key\": process.env.LANGCHAIN_API_KEY || \"\"\n            }\n        };\n        if ([\n            \"POST\",\n            \"PUT\",\n            \"PATCH\"\n        ].includes(method)) {\n            options.headers = {\n                ...options.headers,\n                \"Content-Type\": \"application/json\"\n            };\n            const bodyText = await req.text();\n            if (typeof bodyText === \"string\" && bodyText.length > 0) {\n                const parsedBody = JSON.parse(bodyText);\n                parsedBody.config = parsedBody.config || {};\n                parsedBody.config.configurable = {\n                    ...parsedBody.config.configurable,\n                    supabase_session: session,\n                    supabase_user_id: user.id\n                };\n                options.body = JSON.stringify(parsedBody);\n            } else {\n                options.body = bodyText;\n            }\n        }\n        const res = await fetch(`${_constants__WEBPACK_IMPORTED_MODULE_0__.LANGGRAPH_API_URL}/${path}${queryString}`, options);\n        if (res.status >= 400) {\n            console.error(\"ERROR IN PROXY\", `${_constants__WEBPACK_IMPORTED_MODULE_0__.LANGGRAPH_API_URL}/${path}${queryString}`, res.status, res.statusText);\n            return new Response(res.body, {\n                status: res.status,\n                statusText: res.statusText\n            });\n        }\n        const headers = new Headers({\n            ...getCorsHeaders()\n        });\n        // Safely add headers from the original response\n        res.headers.forEach((value, key)=>{\n            try {\n                headers.set(key, value);\n            } catch (error) {\n                console.warn(`Failed to set header: ${key}`, error);\n            }\n        });\n        return new Response(res.body, {\n            status: res.status,\n            statusText: res.statusText,\n            headers\n        });\n    } catch (e) {\n        console.error(\"Error in proxy\");\n        console.error(e);\n        console.error(\"\\n\\n\\nEND ERROR\\n\\n\");\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            error: e.message\n        }, {\n            status: e.status ?? 500\n        });\n    }\n}\nconst GET = (req)=>handleRequest(req, \"GET\");\nconst POST = (req)=>handleRequest(req, \"POST\");\nconst PUT = (req)=>handleRequest(req, \"PUT\");\nconst PATCH = (req)=>handleRequest(req, \"PATCH\");\nconst DELETE = (req)=>handleRequest(req, \"DELETE\");\n// Add a new OPTIONS handler\nconst OPTIONS = ()=>{\n    return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(null, {\n        status: 204,\n        headers: {\n            ...getCorsHeaders()\n        }\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/[..._path]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/constants.ts":
/*!**************************!*\
  !*** ./src/constants.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALLOWED_AUDIO_TYPES: () => (/* binding */ ALLOWED_AUDIO_TYPES),\n/* harmony export */   ALLOWED_AUDIO_TYPE_ENDINGS: () => (/* binding */ ALLOWED_AUDIO_TYPE_ENDINGS),\n/* harmony export */   ALLOWED_VIDEO_TYPES: () => (/* binding */ ALLOWED_VIDEO_TYPES),\n/* harmony export */   ALLOWED_VIDEO_TYPE_ENDINGS: () => (/* binding */ ALLOWED_VIDEO_TYPE_ENDINGS),\n/* harmony export */   ASSISTANT_ID_COOKIE: () => (/* binding */ ASSISTANT_ID_COOKIE),\n/* harmony export */   CHAT_COLLAPSED_QUERY_PARAM: () => (/* binding */ CHAT_COLLAPSED_QUERY_PARAM),\n/* harmony export */   HAS_ASSISTANT_COOKIE_BEEN_SET: () => (/* binding */ HAS_ASSISTANT_COOKIE_BEEN_SET),\n/* harmony export */   LANGGRAPH_API_URL: () => (/* binding */ LANGGRAPH_API_URL),\n/* harmony export */   OC_HAS_SEEN_CUSTOM_ASSISTANTS_ALERT: () => (/* binding */ OC_HAS_SEEN_CUSTOM_ASSISTANTS_ALERT),\n/* harmony export */   WEB_SEARCH_RESULTS_QUERY_PARAM: () => (/* binding */ WEB_SEARCH_RESULTS_QUERY_PARAM)\n/* harmony export */ });\nconst LANGGRAPH_API_URL = process.env.LANGGRAPH_API_URL ?? \"http://localhost:54367\";\n// v2 is tied to the 'open-canvas-prod' deployment.\nconst ASSISTANT_ID_COOKIE = \"oc_assistant_id_v2\";\n// export const ASSISTANT_ID_COOKIE = \"oc_assistant_id\";\nconst HAS_ASSISTANT_COOKIE_BEEN_SET = \"has_oc_assistant_id_been_set\";\nconst OC_HAS_SEEN_CUSTOM_ASSISTANTS_ALERT = \"oc_has_seen_custom_assistants_alert\";\nconst WEB_SEARCH_RESULTS_QUERY_PARAM = \"webSearchResults\";\nconst ALLOWED_AUDIO_TYPES = new Set([\n    \"audio/mp3\",\n    \"audio/mp4\",\n    \"audio/mpeg\",\n    \"audio/mpga\",\n    \"audio/m4a\",\n    \"audio/wav\",\n    \"audio/webm\"\n]);\nconst ALLOWED_AUDIO_TYPE_ENDINGS = [\n    \".mp3\",\n    \".mpga\",\n    \".m4a\",\n    \".wav\",\n    \".webm\"\n];\nconst ALLOWED_VIDEO_TYPES = new Set([\n    \"video/mp4\",\n    \"video/mpeg\",\n    \"video/webm\"\n]);\nconst ALLOWED_VIDEO_TYPE_ENDINGS = [\n    \".mp4\",\n    \".mpeg\",\n    \".webm\"\n];\nconst CHAT_COLLAPSED_QUERY_PARAM = \"chatCollapsed\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/constants.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/../../node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/../../node_modules/next/dist/api/headers.js\");\n\n\nfunction createClient() {\n    if (false) {}\n    if (false) {}\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://dkzqtcimgmrnlnsuomiu.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRrenF0Y2ltZ21ybmxuc3VvbWl1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTU0ODY4NDMsImV4cCI6MjA3MTA2Mjg0M30.m2iAmcSa-l4Qo2C8YdS00J7FtjaeIwzuappMUdQ_n4I\", {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/verify_user_server.ts":
/*!************************************************!*\
  !*** ./src/lib/supabase/verify_user_server.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   verifyUserAuthenticated: () => (/* binding */ verifyUserAuthenticated)\n/* harmony export */ });\n/* harmony import */ var _server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\nasync function verifyUserAuthenticated() {\n    const supabase = (0,_server__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n    const { data: { user } } = await supabase.auth.getUser();\n    const { data: { session } } = await supabase.auth.getSession();\n    if (!user || !session) {\n        return undefined;\n    }\n    return {\n        user,\n        session\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL3ZlcmlmeV91c2VyX3NlcnZlci50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUN3QztBQUVqQyxlQUFlQztJQUdwQixNQUFNQyxXQUFXRixxREFBWUE7SUFDN0IsTUFBTSxFQUNKRyxNQUFNLEVBQUVDLElBQUksRUFBRSxFQUNmLEdBQUcsTUFBTUYsU0FBU0csSUFBSSxDQUFDQyxPQUFPO0lBQy9CLE1BQU0sRUFDSkgsTUFBTSxFQUFFSSxPQUFPLEVBQUUsRUFDbEIsR0FBRyxNQUFNTCxTQUFTRyxJQUFJLENBQUNHLFVBQVU7SUFDbEMsSUFBSSxDQUFDSixRQUFRLENBQUNHLFNBQVM7UUFDckIsT0FBT0U7SUFDVDtJQUNBLE9BQU87UUFBRUw7UUFBTUc7SUFBUTtBQUN6QiIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uL3NyYy9saWIvc3VwYWJhc2UvdmVyaWZ5X3VzZXJfc2VydmVyLnRzP2JiNDciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgU2Vzc2lvbiwgVXNlciB9IGZyb20gXCJAc3VwYWJhc2Uvc3VwYWJhc2UtanNcIjtcclxuaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSBcIi4vc2VydmVyXCI7XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdmVyaWZ5VXNlckF1dGhlbnRpY2F0ZWQoKTogUHJvbWlzZTxcclxuICB7IHVzZXI6IFVzZXI7IHNlc3Npb246IFNlc3Npb24gfSB8IHVuZGVmaW5lZFxyXG4+IHtcclxuICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudCgpO1xyXG4gIGNvbnN0IHtcclxuICAgIGRhdGE6IHsgdXNlciB9LFxyXG4gIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKTtcclxuICBjb25zdCB7XHJcbiAgICBkYXRhOiB7IHNlc3Npb24gfSxcclxuICB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRTZXNzaW9uKCk7XHJcbiAgaWYgKCF1c2VyIHx8ICFzZXNzaW9uKSB7XHJcbiAgICByZXR1cm4gdW5kZWZpbmVkO1xyXG4gIH1cclxuICByZXR1cm4geyB1c2VyLCBzZXNzaW9uIH07XHJcbn1cclxuIl0sIm5hbWVzIjpbImNyZWF0ZUNsaWVudCIsInZlcmlmeVVzZXJBdXRoZW50aWNhdGVkIiwic3VwYWJhc2UiLCJkYXRhIiwidXNlciIsImF1dGgiLCJnZXRVc2VyIiwic2Vzc2lvbiIsImdldFNlc3Npb24iLCJ1bmRlZmluZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/verify_user_server.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2F%5B..._path%5D%2Froute&page=%2Fapi%2F%5B..._path%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2F%5B..._path%5D%2Froute.ts&appDir=D%3A%5CStudy%5Ccreat%5Copen-canvas%5C%E7%AC%AC%E4%B8%80%5Copen-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CStudy%5Ccreat%5Copen-canvas%5C%E7%AC%AC%E4%B8%80%5Copen-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();