"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opencanvas";
exports.ids = ["vendor-chunks/@opencanvas"];
exports.modules = {

/***/ "(ssr)/../../node_modules/@opencanvas/shared/dist/constants.js":
/*!***************************************************************!*\
  !*** ../../node_modules/@opencanvas/shared/dist/constants.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PROGRAMMING_LANGUAGES = exports.DEFAULT_INPUTS = exports.CONTEXT_DOCUMENTS_NAMESPACE = exports.OC_WEB_SEARCH_RESULTS_MESSAGE_KEY = exports.OC_HIDE_FROM_UI_KEY = exports.OC_SUMMARIZED_MESSAGE_KEY = void 0;\nexports.OC_SUMMARIZED_MESSAGE_KEY = \"__oc_summarized_message\";\nexports.OC_HIDE_FROM_UI_KEY = \"__oc_hide_from_ui\";\nexports.OC_WEB_SEARCH_RESULTS_MESSAGE_KEY = \"__oc_web_search_results_message\";\nexports.CONTEXT_DOCUMENTS_NAMESPACE = [\"context_documents\"];\nexports.DEFAULT_INPUTS = {\n    highlightedCode: undefined,\n    highlightedText: undefined,\n    next: undefined,\n    language: undefined,\n    artifactLength: undefined,\n    regenerateWithEmojis: undefined,\n    readingLevel: undefined,\n    addComments: undefined,\n    addLogs: undefined,\n    fixBugs: undefined,\n    portLanguage: undefined,\n    customQuickActionId: undefined,\n    webSearchEnabled: undefined,\n    webSearchResults: undefined,\n};\nexports.PROGRAMMING_LANGUAGES = [\n    {\n        language: \"typescript\",\n        label: \"TypeScript\",\n    },\n    {\n        language: \"javascript\",\n        label: \"JavaScript\",\n    },\n    {\n        language: \"cpp\",\n        label: \"C++\",\n    },\n    {\n        language: \"java\",\n        label: \"Java\",\n    },\n    {\n        language: \"php\",\n        label: \"PHP\",\n    },\n    {\n        language: \"python\",\n        label: \"Python\",\n    },\n    {\n        language: \"html\",\n        label: \"HTML\",\n    },\n    {\n        language: \"sql\",\n        label: \"SQL\",\n    },\n    {\n        language: \"json\",\n        label: \"JSON\",\n    },\n    {\n        language: \"rust\",\n        label: \"Rust\",\n    },\n    {\n        language: \"xml\",\n        label: \"XML\",\n    },\n    {\n        language: \"clojure\",\n        label: \"Clojure\",\n    },\n    {\n        language: \"csharp\",\n        label: \"C#\",\n    },\n    {\n        language: \"other\",\n        label: \"Other\",\n    },\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0BvcGVuY2FudmFzL3NoYXJlZC9kaXN0L2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCw2QkFBNkIsR0FBRyxzQkFBc0IsR0FBRyxtQ0FBbUMsR0FBRyx5Q0FBeUMsR0FBRywyQkFBMkIsR0FBRyxpQ0FBaUM7QUFDMU0saUNBQWlDO0FBQ2pDLDJCQUEyQjtBQUMzQix5Q0FBeUM7QUFDekMsbUNBQW1DO0FBQ25DLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvQG9wZW5jYW52YXMvc2hhcmVkL2Rpc3QvY29uc3RhbnRzLmpzP2Y0NTMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLlBST0dSQU1NSU5HX0xBTkdVQUdFUyA9IGV4cG9ydHMuREVGQVVMVF9JTlBVVFMgPSBleHBvcnRzLkNPTlRFWFRfRE9DVU1FTlRTX05BTUVTUEFDRSA9IGV4cG9ydHMuT0NfV0VCX1NFQVJDSF9SRVNVTFRTX01FU1NBR0VfS0VZID0gZXhwb3J0cy5PQ19ISURFX0ZST01fVUlfS0VZID0gZXhwb3J0cy5PQ19TVU1NQVJJWkVEX01FU1NBR0VfS0VZID0gdm9pZCAwO1xuZXhwb3J0cy5PQ19TVU1NQVJJWkVEX01FU1NBR0VfS0VZID0gXCJfX29jX3N1bW1hcml6ZWRfbWVzc2FnZVwiO1xuZXhwb3J0cy5PQ19ISURFX0ZST01fVUlfS0VZID0gXCJfX29jX2hpZGVfZnJvbV91aVwiO1xuZXhwb3J0cy5PQ19XRUJfU0VBUkNIX1JFU1VMVFNfTUVTU0FHRV9LRVkgPSBcIl9fb2Nfd2ViX3NlYXJjaF9yZXN1bHRzX21lc3NhZ2VcIjtcbmV4cG9ydHMuQ09OVEVYVF9ET0NVTUVOVFNfTkFNRVNQQUNFID0gW1wiY29udGV4dF9kb2N1bWVudHNcIl07XG5leHBvcnRzLkRFRkFVTFRfSU5QVVRTID0ge1xuICAgIGhpZ2hsaWdodGVkQ29kZTogdW5kZWZpbmVkLFxuICAgIGhpZ2hsaWdodGVkVGV4dDogdW5kZWZpbmVkLFxuICAgIG5leHQ6IHVuZGVmaW5lZCxcbiAgICBsYW5ndWFnZTogdW5kZWZpbmVkLFxuICAgIGFydGlmYWN0TGVuZ3RoOiB1bmRlZmluZWQsXG4gICAgcmVnZW5lcmF0ZVdpdGhFbW9qaXM6IHVuZGVmaW5lZCxcbiAgICByZWFkaW5nTGV2ZWw6IHVuZGVmaW5lZCxcbiAgICBhZGRDb21tZW50czogdW5kZWZpbmVkLFxuICAgIGFkZExvZ3M6IHVuZGVmaW5lZCxcbiAgICBmaXhCdWdzOiB1bmRlZmluZWQsXG4gICAgcG9ydExhbmd1YWdlOiB1bmRlZmluZWQsXG4gICAgY3VzdG9tUXVpY2tBY3Rpb25JZDogdW5kZWZpbmVkLFxuICAgIHdlYlNlYXJjaEVuYWJsZWQ6IHVuZGVmaW5lZCxcbiAgICB3ZWJTZWFyY2hSZXN1bHRzOiB1bmRlZmluZWQsXG59O1xuZXhwb3J0cy5QUk9HUkFNTUlOR19MQU5HVUFHRVMgPSBbXG4gICAge1xuICAgICAgICBsYW5ndWFnZTogXCJ0eXBlc2NyaXB0XCIsXG4gICAgICAgIGxhYmVsOiBcIlR5cGVTY3JpcHRcIixcbiAgICB9LFxuICAgIHtcbiAgICAgICAgbGFuZ3VhZ2U6IFwiamF2YXNjcmlwdFwiLFxuICAgICAgICBsYWJlbDogXCJKYXZhU2NyaXB0XCIsXG4gICAgfSxcbiAgICB7XG4gICAgICAgIGxhbmd1YWdlOiBcImNwcFwiLFxuICAgICAgICBsYWJlbDogXCJDKytcIixcbiAgICB9LFxuICAgIHtcbiAgICAgICAgbGFuZ3VhZ2U6IFwiamF2YVwiLFxuICAgICAgICBsYWJlbDogXCJKYXZhXCIsXG4gICAgfSxcbiAgICB7XG4gICAgICAgIGxhbmd1YWdlOiBcInBocFwiLFxuICAgICAgICBsYWJlbDogXCJQSFBcIixcbiAgICB9LFxuICAgIHtcbiAgICAgICAgbGFuZ3VhZ2U6IFwicHl0aG9uXCIsXG4gICAgICAgIGxhYmVsOiBcIlB5dGhvblwiLFxuICAgIH0sXG4gICAge1xuICAgICAgICBsYW5ndWFnZTogXCJodG1sXCIsXG4gICAgICAgIGxhYmVsOiBcIkhUTUxcIixcbiAgICB9LFxuICAgIHtcbiAgICAgICAgbGFuZ3VhZ2U6IFwic3FsXCIsXG4gICAgICAgIGxhYmVsOiBcIlNRTFwiLFxuICAgIH0sXG4gICAge1xuICAgICAgICBsYW5ndWFnZTogXCJqc29uXCIsXG4gICAgICAgIGxhYmVsOiBcIkpTT05cIixcbiAgICB9LFxuICAgIHtcbiAgICAgICAgbGFuZ3VhZ2U6IFwicnVzdFwiLFxuICAgICAgICBsYWJlbDogXCJSdXN0XCIsXG4gICAgfSxcbiAgICB7XG4gICAgICAgIGxhbmd1YWdlOiBcInhtbFwiLFxuICAgICAgICBsYWJlbDogXCJYTUxcIixcbiAgICB9LFxuICAgIHtcbiAgICAgICAgbGFuZ3VhZ2U6IFwiY2xvanVyZVwiLFxuICAgICAgICBsYWJlbDogXCJDbG9qdXJlXCIsXG4gICAgfSxcbiAgICB7XG4gICAgICAgIGxhbmd1YWdlOiBcImNzaGFycFwiLFxuICAgICAgICBsYWJlbDogXCJDI1wiLFxuICAgIH0sXG4gICAge1xuICAgICAgICBsYW5ndWFnZTogXCJvdGhlclwiLFxuICAgICAgICBsYWJlbDogXCJPdGhlclwiLFxuICAgIH0sXG5dO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@opencanvas/shared/dist/constants.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@opencanvas/shared/dist/models.js":
/*!************************************************************!*\
  !*** ../../node_modules/@opencanvas/shared/dist/models.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DEFAULT_MODEL_CONFIG = exports.DEFAULT_MODEL_NAME = exports.ALL_MODELS = exports.THINKING_MODELS = exports.NON_STREAMING_TEXT_MODELS = exports.NON_STREAMING_TOOL_CALLING_MODELS = exports.TEMPERATURE_EXCLUDED_MODELS = exports.LANGCHAIN_USER_ONLY_MODELS = void 0;\nconst AZURE_MODELS = [\n    {\n        name: \"azure/gpt-4o-mini\",\n        label: \"GPT-4o mini (Azure)\",\n        isNew: false,\n        config: {\n            provider: \"azure_openai\",\n            temperatureRange: {\n                min: 0,\n                max: 1,\n                default: 0.5,\n                current: 0.5,\n            },\n            maxTokens: {\n                min: 1,\n                max: 4_096,\n                default: 4_096,\n                current: 4_096,\n            },\n        },\n    },\n];\nconst OPENAI_MODELS = [\n    {\n        name: \"gpt-4.1\",\n        label: \"GPT 4.1\",\n        config: {\n            provider: \"openai\",\n            temperatureRange: {\n                min: 0,\n                max: 1,\n                default: 0.5,\n                current: 0.5,\n            },\n            maxTokens: {\n                min: 1,\n                max: 32_768,\n                default: 4_096,\n                current: 4_096,\n            },\n        },\n        isNew: true,\n    },\n    {\n        name: \"gpt-4o\",\n        label: \"GPT 4o\",\n        config: {\n            provider: \"openai\",\n            temperatureRange: {\n                min: 0,\n                max: 1,\n                default: 0.5,\n                current: 0.5,\n            },\n            maxTokens: {\n                min: 1,\n                max: 16_384,\n                default: 4_096,\n                current: 4_096,\n            },\n        },\n        isNew: false,\n    },\n    {\n        name: \"glm-4.5\",\n        label: \"GLM 4.5\",\n        config: {\n            provider: \"openai\",\n            temperatureRange: {\n                min: 0,\n                max: 1,\n                default: 0.7,\n                current: 0.7,\n            },\n            maxTokens: {\n                min: 1,\n                max: 32_768,\n                default: 4_096,\n                current: 4_096,\n            },\n        },\n        isNew: true,\n    },\n];\n/**\n * Ollama model names _MUST_ be prefixed with `\"ollama-\"`\n */\nconst OLLAMA_MODELS = [\n    {\n        name: \"ollama-llama3.3\",\n        label: \"Llama 3.3 70B (local)\",\n        config: {\n            provider: \"ollama\",\n            temperatureRange: {\n                min: 0,\n                max: 1,\n                default: 0.5,\n                current: 0.5,\n            },\n            maxTokens: {\n                min: 1,\n                max: 2_048,\n                default: 2_048,\n                current: 2_048,\n            },\n        },\n        isNew: false,\n    },\n];\nconst ANTHROPIC_MODELS = [\n    {\n        name: \"claude-sonnet-4-0\",\n        label: \"Claude Sonnet 4\",\n        config: {\n            provider: \"anthropic\",\n            temperatureRange: {\n                min: 0,\n                max: 1,\n                default: 0.5,\n                current: 0.5,\n            },\n            maxTokens: {\n                min: 1,\n                max: 64_000,\n                default: 4_096,\n                current: 4_096,\n            },\n        },\n        isNew: true,\n    },\n    {\n        name: \"claude-opus-4-0\",\n        label: \"Claude Opus 4\",\n        config: {\n            provider: \"anthropic\",\n            temperatureRange: {\n                min: 0,\n                max: 1,\n                default: 0.5,\n                current: 0.5,\n            },\n            maxTokens: {\n                min: 1,\n                max: 32_000,\n                default: 4_096,\n                current: 4_096,\n            },\n        },\n        isNew: true,\n    },\n    {\n        name: \"claude-3-7-sonnet-latest\",\n        label: \"Claude 3.7 Sonnet\",\n        config: {\n            provider: \"anthropic\",\n            temperatureRange: {\n                min: 0,\n                max: 1,\n                default: 0.5,\n                current: 0.5,\n            },\n            maxTokens: {\n                min: 1,\n                max: 8_192,\n                default: 4_096,\n                current: 4_096,\n            },\n        },\n        isNew: false,\n    },\n    {\n        name: \"claude-3-5-sonnet-latest\",\n        label: \"Claude 3.5 Sonnet\",\n        config: {\n            provider: \"anthropic\",\n            temperatureRange: {\n                min: 0,\n                max: 1,\n                default: 0.5,\n                current: 0.5,\n            },\n            maxTokens: {\n                min: 1,\n                max: 8_192,\n                default: 4_096,\n                current: 4_096,\n            },\n        },\n        isNew: false,\n    },\n    {\n        name: \"claude-3-5-haiku-20241022\",\n        label: \"Claude 3.5 Haiku\",\n        config: {\n            provider: \"anthropic\",\n            temperatureRange: {\n                min: 0,\n                max: 1,\n                default: 0.5,\n                current: 0.5,\n            },\n            maxTokens: {\n                min: 1,\n                max: 8_192,\n                default: 4_096,\n                current: 4_096,\n            },\n        },\n        isNew: false,\n    },\n    {\n        name: \"claude-3-haiku-20240307\",\n        label: \"Claude 3 Haiku (old)\",\n        config: {\n            provider: \"anthropic\",\n            temperatureRange: {\n                min: 0,\n                max: 1,\n                default: 0.5,\n                current: 0.5,\n            },\n            maxTokens: {\n                min: 1,\n                max: 4_096,\n                default: 4_096,\n                current: 4_096,\n            },\n        },\n        isNew: false,\n    },\n];\nconst FIREWORKS_MODELS = [\n    {\n        name: \"accounts/fireworks/models/llama-v3p3-70b-instruct\",\n        label: \"Llama 3.3 70B\",\n        config: {\n            provider: \"fireworks\",\n            temperatureRange: {\n                min: 0,\n                max: 1,\n                default: 0.5,\n                current: 0.5,\n            },\n            maxTokens: {\n                min: 1,\n                max: 16_384,\n                default: 4_096,\n                current: 4_096,\n            },\n        },\n        isNew: false,\n    },\n    {\n        name: \"accounts/fireworks/models/llama-v3p1-70b-instruct\",\n        label: \"Llama 70B (old)\",\n        config: {\n            provider: \"fireworks\",\n            temperatureRange: {\n                min: 0,\n                max: 1,\n                default: 0.5,\n                current: 0.5,\n            },\n            maxTokens: {\n                min: 1,\n                max: 16_384,\n                default: 4_096,\n                current: 4_096,\n            },\n        },\n        isNew: false,\n    },\n    {\n        name: \"accounts/fireworks/models/deepseek-v3\",\n        label: \"DeepSeek V3\",\n        config: {\n            provider: \"fireworks\",\n            temperatureRange: {\n                min: 0,\n                max: 1,\n                default: 0.5,\n                current: 0.5,\n            },\n            maxTokens: {\n                min: 1,\n                max: 8_000,\n                default: 4_096,\n                current: 4_096,\n            },\n        },\n        isNew: false,\n    },\n    {\n        name: \"accounts/fireworks/models/deepseek-r1\",\n        label: \"DeepSeek R1\",\n        config: {\n            provider: \"fireworks\",\n            temperatureRange: {\n                min: 0,\n                max: 1,\n                default: 0.5,\n                current: 0.5,\n            },\n            maxTokens: {\n                min: 1,\n                max: 8_000,\n                default: 4_096,\n                current: 4_096,\n            },\n        },\n        isNew: false,\n    },\n];\nconst GROQ_MODELS = [\n    {\n        name: \"groq/deepseek-r1-distill-llama-70b\",\n        label: \"DeepSeek R1 Llama 70b Distill\",\n        config: {\n            provider: \"groq\",\n            temperatureRange: {\n                min: 0,\n                max: 1,\n                default: 0.5,\n                current: 0.5,\n            },\n            maxTokens: {\n                min: 1,\n                max: 8_000,\n                default: 4_096,\n                current: 4_096,\n            },\n        },\n        isNew: false,\n    },\n];\nconst GEMINI_MODELS = [\n    {\n        name: \"gemini-2.5-flash-preview-05-20\",\n        label: \"Gemini 2.5 Flash (Preview)\",\n        config: {\n            provider: \"google-genai\",\n            temperatureRange: {\n                min: 0,\n                max: 1,\n                default: 0.5,\n                current: 0.5,\n            },\n            maxTokens: {\n                min: 1,\n                max: 100_000,\n                default: 4_096,\n                current: 4_096,\n            },\n        },\n        isNew: true,\n    },\n    {\n        name: \"gemini-2.5-pro\",\n        label: \"Gemini 2.5 Pro\",\n        config: {\n            provider: \"google-genai\",\n            temperatureRange: {\n                min: 0,\n                max: 1,\n                default: 0.7,\n                current: 0.7,\n            },\n            maxTokens: {\n                min: 1,\n                max: 100_000,\n                default: 4_096,\n                current: 4_096,\n            },\n        },\n        isNew: true,\n    },\n];\nexports.LANGCHAIN_USER_ONLY_MODELS = [\n    \"o1\",\n    \"gpt-4.5-preview\",\n    \"claude-3-5-sonnet-latest\",\n    \"claude-3-7-sonnet-latest\",\n    \"gemini-2.0-flash-thinking-exp-01-21\",\n    \"gemini-2.5-pro-preview-05-06\",\n    \"claude-sonnet-4-0\",\n    \"claude-opus-4-0\",\n];\n// Models which do NOT support the temperature parameter.\nexports.TEMPERATURE_EXCLUDED_MODELS = [\n    \"o1-mini\",\n    \"o3-mini\",\n    \"o1\",\n    \"o4-mini\",\n];\n// Models which do NOT stream back tool calls.\nexports.NON_STREAMING_TOOL_CALLING_MODELS = [\n    \"gemini-2.0-flash-exp\",\n    \"gemini-1.5-flash\",\n    \"gemini-2.5-pro-preview-05-06\",\n    \"gemini-2.5-flash-preview-05-20\",\n];\n// Models which do NOT stream back text.\nexports.NON_STREAMING_TEXT_MODELS = [\n    \"o1\",\n    \"gemini-2.0-flash-thinking-exp-01-21\",\n];\n// Models which preform CoT before generating a final response.\nexports.THINKING_MODELS = [\n    \"accounts/fireworks/models/deepseek-r1\",\n    \"groq/deepseek-r1-distill-llama-70b\",\n];\nexports.ALL_MODELS = [\n    ...OPENAI_MODELS,\n    ...ANTHROPIC_MODELS,\n    ...FIREWORKS_MODELS,\n    ...GEMINI_MODELS,\n    ...AZURE_MODELS,\n    ...OLLAMA_MODELS,\n    ...GROQ_MODELS,\n];\nexports.DEFAULT_MODEL_NAME = OPENAI_MODELS[2].name;\nexports.DEFAULT_MODEL_CONFIG = {\n    ...OPENAI_MODELS[2].config,\n    temperatureRange: { ...OPENAI_MODELS[2].config.temperatureRange },\n    maxTokens: { ...OPENAI_MODELS[2].config.maxTokens },\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@opencanvas/shared/dist/models.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@opencanvas/shared/dist/prompts/quick-actions.js":
/*!***************************************************************************!*\
  !*** ../../node_modules/@opencanvas/shared/dist/prompts/quick-actions.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.REFLECTIONS_QUICK_ACTION_PROMPT = exports.CUSTOM_QUICK_ACTION_CONVERSATION_CONTEXT = exports.CUSTOM_QUICK_ACTION_ARTIFACT_PROMPT_PREFIX = exports.CUSTOM_QUICK_ACTION_ARTIFACT_CONTENT_PROMPT = void 0;\nexports.CUSTOM_QUICK_ACTION_ARTIFACT_CONTENT_PROMPT = `Here is the full artifact content the user has generated, and is requesting you rewrite according to their custom instructions:\r\n<artifact>\r\n{artifactContent}\r\n</artifact>`;\nexports.CUSTOM_QUICK_ACTION_ARTIFACT_PROMPT_PREFIX = `You are an AI assistant tasked with rewriting a users generated artifact.\r\nThey have provided custom instructions on how you should manage rewriting the artifact. The custom instructions are wrapped inside the <custom-instructions> tags.\r\n\r\nUse this context about the application the user is interacting with when generating your response:\r\n<app-context>\r\nThe name of the application is \"Open Canvas\". Open Canvas is a web application where users have a chat window and a canvas to display an artifact.\r\nArtifacts can be any sort of writing content, emails, code, or other creative writing work. Think of artifacts as content, or writing you might find on you might find on a blog, Google doc, or other writing platform.\r\nUsers only have a single artifact per conversation, however they have the ability to go back and fourth between artifact edits/revisions.\r\n</app-context>`;\nexports.CUSTOM_QUICK_ACTION_CONVERSATION_CONTEXT = `Here is the last 5 (or less) messages in the chat history between you and the user:\r\n<conversation>\r\n{conversation}\r\n</conversation>`;\nexports.REFLECTIONS_QUICK_ACTION_PROMPT = `The following are reflections on the user's style guidelines and general memories/facts about the user.\r\nUse these reflections as context when generating your response.\r\n<reflections>\r\n{reflections}\r\n</reflections>`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@opencanvas/shared/dist/prompts/quick-actions.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@opencanvas/shared/dist/utils/artifacts.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/@opencanvas/shared/dist/utils/artifacts.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getArtifactContent = exports.isDeprecatedArtifactType = exports.isArtifactMarkdownContent = exports.isArtifactCodeContent = void 0;\nconst isArtifactCodeContent = (content) => {\n    return !!(typeof content === \"object\" &&\n        content &&\n        \"type\" in content &&\n        content.type === \"code\");\n};\nexports.isArtifactCodeContent = isArtifactCodeContent;\nconst isArtifactMarkdownContent = (content) => {\n    return !!(typeof content === \"object\" &&\n        content &&\n        \"type\" in content &&\n        content.type === \"text\");\n};\nexports.isArtifactMarkdownContent = isArtifactMarkdownContent;\nconst isDeprecatedArtifactType = (artifact) => {\n    return !!(typeof artifact === \"object\" &&\n        artifact &&\n        \"currentContentIndex\" in artifact &&\n        typeof artifact.currentContentIndex === \"number\");\n};\nexports.isDeprecatedArtifactType = isDeprecatedArtifactType;\nconst getArtifactContent = (artifact) => {\n    if (!artifact) {\n        throw new Error(\"No artifact found.\");\n    }\n    const currentContent = artifact.contents.find((a) => a.index === artifact.currentIndex);\n    if (!currentContent) {\n        return artifact.contents[artifact.contents.length - 1];\n    }\n    return currentContent;\n};\nexports.getArtifactContent = getArtifactContent;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@opencanvas/shared/dist/utils/artifacts.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@opencanvas/shared/dist/utils/thinking.js":
/*!********************************************************************!*\
  !*** ../../node_modules/@opencanvas/shared/dist/utils/thinking.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.extractThinkingAndResponseTokens = extractThinkingAndResponseTokens;\nexports.handleRewriteArtifactThinkingModel = handleRewriteArtifactThinkingModel;\nexports.isThinkingModel = isThinkingModel;\nconst messages_1 = __webpack_require__(/*! @langchain/core/messages */ \"(ssr)/../../node_modules/@langchain/core/messages.cjs\");\nconst models_js_1 = __webpack_require__(/*! ../models.js */ \"(ssr)/../../node_modules/@opencanvas/shared/dist/models.js\");\n/**\n * Extracts thinking and response content from a text string containing XML-style think tags.\n * Designed to handle streaming text where tags might be incomplete.\n *\n * @param text - The input text that may contain <think> tags\n * @returns An object containing:\n *   - thinking: Content between <think> tags, or all content after <think> if no closing tag\n *   - response: All text outside of think tags\n *\n * @example\n * // Complete tags\n * extractThinkingAndResponseTokens('Hello <think>processing...</think>world')\n * // Returns: { thinking: 'processing...', response: 'Hello world' }\n *\n * // Streaming/incomplete tags\n * extractThinkingAndResponseTokens('Hello <think>processing...')\n * // Returns: { thinking: 'processing...', response: 'Hello ' }\n *\n * // No tags\n * extractThinkingAndResponseTokens('Hello world')\n * // Returns: { thinking: '', response: 'Hello world' }\n */\nfunction extractThinkingAndResponseTokens(text) {\n    const thinkStartTag = \"<think>\";\n    const thinkEndTag = \"</think>\";\n    const startIndex = text.indexOf(thinkStartTag);\n    // No thinking tag found\n    if (startIndex === -1) {\n        return {\n            thinking: \"\",\n            response: text.trim(),\n        };\n    }\n    const afterStartTag = text.substring(startIndex + thinkStartTag.length);\n    const endIndex = afterStartTag.indexOf(thinkEndTag);\n    // If no closing tag, all remaining text is thinking\n    if (endIndex === -1) {\n        return {\n            thinking: afterStartTag.trim(),\n            response: text.substring(0, startIndex).trim(),\n        };\n    }\n    // We have both opening and closing tags\n    const thinking = afterStartTag.substring(0, endIndex).trim();\n    const response = (text.substring(0, startIndex) +\n        afterStartTag.substring(endIndex + thinkEndTag.length)).trim();\n    return {\n        thinking,\n        response,\n    };\n}\n/**\n * Handles the rewriting of artifact content by processing thinking tokens and updating messages state.\n * This function extracts thinking and response tokens from the new artifact content, updates the message\n * state with thinking tokens if present, and returns the response content.\n *\n * @param {Object} params - The parameters for handling artifact rewriting\n * @param {string} params.newArtifactContent - The new content to process for the artifact\n * @param {Dispatch<SetStateAction<BaseMessage[]>>} params.setMessages - State setter function for updating messages\n * @param {string} params.thinkingMessageId - Unique identifier for the thinking message to update or create\n * @returns {string} The extracted response content from the artifact\n */\nfunction handleRewriteArtifactThinkingModel({ newArtifactContent, setMessages, thinkingMessageId, }) {\n    const { thinking, response } = extractThinkingAndResponseTokens(newArtifactContent);\n    if (thinking.length > 0) {\n        setMessages((prevMessages) => {\n            if (!thinkingMessageId) {\n                console.error(\"Thinking message not found\");\n                return prevMessages;\n            }\n            const prevHasThinkingMsg = prevMessages.some((msg) => msg.id === thinkingMessageId);\n            const thinkingMessage = new messages_1.AIMessage({\n                id: thinkingMessageId,\n                content: thinking,\n            });\n            if (prevHasThinkingMsg) {\n                // The message exists, so replace it\n                const newMsgs = prevMessages.map((msg) => {\n                    if (msg.id !== thinkingMessageId) {\n                        return msg;\n                    }\n                    return thinkingMessage;\n                });\n                return newMsgs;\n            }\n            // The message does not yet exist, so create it:\n            return [...prevMessages, thinkingMessage];\n        });\n    }\n    return response;\n}\nfunction isThinkingModel(model) {\n    return models_js_1.THINKING_MODELS.some((m) => m === model);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@opencanvas/shared/dist/utils/thinking.js\n");

/***/ })

};
;