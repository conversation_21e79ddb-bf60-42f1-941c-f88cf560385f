"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@uiw";
exports.ids = ["vendor-chunks/@uiw"];
exports.modules = {

/***/ "(ssr)/../../node_modules/@uiw/codemirror-extensions-basic-setup/esm/index.js":
/*!******************************************************************************!*\
  !*** ../../node_modules/@uiw/codemirror-extensions-basic-setup/esm/index.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   basicSetup: () => (/* binding */ basicSetup),\n/* harmony export */   minimalSetup: () => (/* binding */ minimalSetup)\n/* harmony export */ });\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/../../node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/../../node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _codemirror_commands__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @codemirror/commands */ \"(ssr)/../../node_modules/@codemirror/commands/dist/index.js\");\n/* harmony import */ var _codemirror_search__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/search */ \"(ssr)/../../node_modules/@codemirror/search/dist/index.js\");\n/* harmony import */ var _codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @codemirror/autocomplete */ \"(ssr)/../../node_modules/@codemirror/autocomplete/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/../../node_modules/@codemirror/language/dist/index.js\");\n/* harmony import */ var _codemirror_lint__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @codemirror/lint */ \"(ssr)/../../node_modules/@codemirror/lint/dist/index.js\");\n\n\n\n\n\n\n\n/**\nThis is an extension value that just pulls together a number of\nextensions that you might want in a basic editor. It is meant as a\nconvenient helper to quickly set up CodeMirror without installing\nand importing a lot of separate packages.\n\nSpecifically, it includes...\n\n - [the default command bindings](https://codemirror.net/6/docs/ref/#commands.defaultKeymap)\n - [line numbers](https://codemirror.net/6/docs/ref/#view.lineNumbers)\n - [special character highlighting](https://codemirror.net/6/docs/ref/#view.highlightSpecialChars)\n - [the undo history](https://codemirror.net/6/docs/ref/#commands.history)\n - [a fold gutter](https://codemirror.net/6/docs/ref/#language.foldGutter)\n - [custom selection drawing](https://codemirror.net/6/docs/ref/#view.drawSelection)\n - [drop cursor](https://codemirror.net/6/docs/ref/#view.dropCursor)\n - [multiple selections](https://codemirror.net/6/docs/ref/#state.EditorState^allowMultipleSelections)\n - [reindentation on input](https://codemirror.net/6/docs/ref/#language.indentOnInput)\n - [the default highlight style](https://codemirror.net/6/docs/ref/#language.defaultHighlightStyle) (as fallback)\n - [bracket matching](https://codemirror.net/6/docs/ref/#language.bracketMatching)\n - [bracket closing](https://codemirror.net/6/docs/ref/#autocomplete.closeBrackets)\n - [autocompletion](https://codemirror.net/6/docs/ref/#autocomplete.autocompletion)\n - [rectangular selection](https://codemirror.net/6/docs/ref/#view.rectangularSelection) and [crosshair cursor](https://codemirror.net/6/docs/ref/#view.crosshairCursor)\n - [active line highlighting](https://codemirror.net/6/docs/ref/#view.highlightActiveLine)\n - [active line gutter highlighting](https://codemirror.net/6/docs/ref/#view.highlightActiveLineGutter)\n - [selection match highlighting](https://codemirror.net/6/docs/ref/#search.highlightSelectionMatches)\n - [search](https://codemirror.net/6/docs/ref/#search.searchKeymap)\n - [linting](https://codemirror.net/6/docs/ref/#lint.lintKeymap)\n\n(You'll probably want to add some language package to your setup\ntoo.)\n\nThis extension does not allow customization. The idea is that,\nonce you decide you want to configure your editor more precisely,\nyou take this package's source (which is just a bunch of imports\nand an array literal), copy it into your own code, and adjust it\nas desired.\n*/\nvar basicSetup = function basicSetup(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var {\n    crosshairCursor: initCrosshairCursor = false\n  } = options;\n  var keymaps = [];\n  if (options.closeBracketsKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_0__.closeBracketsKeymap);\n  }\n  if (options.defaultKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.defaultKeymap);\n  }\n  if (options.searchKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_search__WEBPACK_IMPORTED_MODULE_2__.searchKeymap);\n  }\n  if (options.historyKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.historyKeymap);\n  }\n  if (options.foldKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.foldKeymap);\n  }\n  if (options.completionKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_0__.completionKeymap);\n  }\n  if (options.lintKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_lint__WEBPACK_IMPORTED_MODULE_4__.lintKeymap);\n  }\n  var extensions = [];\n  if (options.lineNumbers !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.lineNumbers)());\n  if (options.highlightActiveLineGutter !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightActiveLineGutter)());\n  if (options.highlightSpecialChars !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightSpecialChars)());\n  if (options.history !== false) extensions.push((0,_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.history)());\n  if (options.foldGutter !== false) extensions.push((0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.foldGutter)());\n  if (options.drawSelection !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.drawSelection)());\n  if (options.dropCursor !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.dropCursor)());\n  if (options.allowMultipleSelections !== false) extensions.push(_codemirror_state__WEBPACK_IMPORTED_MODULE_6__.EditorState.allowMultipleSelections.of(true));\n  if (options.indentOnInput !== false) extensions.push((0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.indentOnInput)());\n  if (options.syntaxHighlighting !== false) extensions.push((0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.syntaxHighlighting)(_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.defaultHighlightStyle, {\n    fallback: true\n  }));\n  if (options.bracketMatching !== false) extensions.push((0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.bracketMatching)());\n  if (options.closeBrackets !== false) extensions.push((0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_0__.closeBrackets)());\n  if (options.autocompletion !== false) extensions.push((0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_0__.autocompletion)());\n  if (options.rectangularSelection !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.rectangularSelection)());\n  if (initCrosshairCursor !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.crosshairCursor)());\n  if (options.highlightActiveLine !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightActiveLine)());\n  if (options.highlightSelectionMatches !== false) extensions.push((0,_codemirror_search__WEBPACK_IMPORTED_MODULE_2__.highlightSelectionMatches)());\n  if (options.tabSize && typeof options.tabSize === 'number') extensions.push(_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.indentUnit.of(' '.repeat(options.tabSize)));\n  return extensions.concat([_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.keymap.of(keymaps.flat())]).filter(Boolean);\n};\n/**\nA minimal set of extensions to create a functional editor. Only\nincludes [the default keymap](https://codemirror.net/6/docs/ref/#commands.defaultKeymap), [undo\nhistory](https://codemirror.net/6/docs/ref/#commands.history), [special character\nhighlighting](https://codemirror.net/6/docs/ref/#view.highlightSpecialChars), [custom selection\ndrawing](https://codemirror.net/6/docs/ref/#view.drawSelection), and [default highlight\nstyle](https://codemirror.net/6/docs/ref/#language.defaultHighlightStyle).\n*/\nvar minimalSetup = function minimalSetup(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var keymaps = [];\n  if (options.defaultKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.defaultKeymap);\n  }\n  if (options.historyKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.historyKeymap);\n  }\n  var extensions = [];\n  if (options.highlightSpecialChars !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightSpecialChars)());\n  if (options.history !== false) extensions.push((0,_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.history)());\n  if (options.drawSelection !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.drawSelection)());\n  if (options.syntaxHighlighting !== false) extensions.push((0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.syntaxHighlighting)(_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.defaultHighlightStyle, {\n    fallback: true\n  }));\n  return extensions.concat([_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.keymap.of(keymaps.flat())]).filter(Boolean);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@uiw/codemirror-extensions-basic-setup/esm/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@uiw/react-codemirror/esm/getDefaultExtensions.js":
/*!****************************************************************************!*\
  !*** ../../node_modules/@uiw/react-codemirror/esm/getDefaultExtensions.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   color: () => (/* reexport safe */ _codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__.color),\n/* harmony export */   defaultLightThemeOption: () => (/* reexport safe */ _theme_light__WEBPACK_IMPORTED_MODULE_1__.defaultLightThemeOption),\n/* harmony export */   getDefaultExtensions: () => (/* binding */ getDefaultExtensions),\n/* harmony export */   oneDark: () => (/* reexport safe */ _codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__.oneDark),\n/* harmony export */   oneDarkHighlightStyle: () => (/* reexport safe */ _codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__.oneDarkHighlightStyle),\n/* harmony export */   oneDarkTheme: () => (/* reexport safe */ _codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__.oneDarkTheme)\n/* harmony export */ });\n/* harmony import */ var _codemirror_commands__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @codemirror/commands */ \"(ssr)/../../node_modules/@codemirror/commands/dist/index.js\");\n/* harmony import */ var _uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @uiw/codemirror-extensions-basic-setup */ \"(ssr)/../../node_modules/@uiw/codemirror-extensions-basic-setup/esm/index.js\");\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/../../node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/theme-one-dark */ \"(ssr)/../../node_modules/@codemirror/theme-one-dark/dist/index.js\");\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/../../node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _theme_light__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./theme/light */ \"(ssr)/../../node_modules/@uiw/react-codemirror/esm/theme/light.js\");\n\n\n\n\n\n\n\n\nvar getDefaultExtensions = function getDefaultExtensions(optios) {\n  if (optios === void 0) {\n    optios = {};\n  }\n  var {\n    indentWithTab: defaultIndentWithTab = true,\n    editable = true,\n    readOnly = false,\n    theme = 'light',\n    placeholder: placeholderStr = '',\n    basicSetup: defaultBasicSetup = true\n  } = optios;\n  var getExtensions = [];\n  if (defaultIndentWithTab) {\n    getExtensions.unshift(_codemirror_view__WEBPACK_IMPORTED_MODULE_3__.keymap.of([_codemirror_commands__WEBPACK_IMPORTED_MODULE_4__.indentWithTab]));\n  }\n  if (defaultBasicSetup) {\n    if (typeof defaultBasicSetup === 'boolean') {\n      getExtensions.unshift((0,_uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_0__.basicSetup)());\n    } else {\n      getExtensions.unshift((0,_uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_0__.basicSetup)(defaultBasicSetup));\n    }\n  }\n  if (placeholderStr) {\n    getExtensions.unshift((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_3__.placeholder)(placeholderStr));\n  }\n  switch (theme) {\n    case 'light':\n      getExtensions.push(_theme_light__WEBPACK_IMPORTED_MODULE_1__.defaultLightThemeOption);\n      break;\n    case 'dark':\n      getExtensions.push(_codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__.oneDark);\n      break;\n    case 'none':\n      break;\n    default:\n      getExtensions.push(theme);\n      break;\n  }\n  if (editable === false) {\n    getExtensions.push(_codemirror_view__WEBPACK_IMPORTED_MODULE_3__.EditorView.editable.of(false));\n  }\n  if (readOnly) {\n    getExtensions.push(_codemirror_state__WEBPACK_IMPORTED_MODULE_5__.EditorState.readOnly.of(true));\n  }\n  return [...getExtensions];\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@uiw/react-codemirror/esm/getDefaultExtensions.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@uiw/react-codemirror/esm/index.js":
/*!*************************************************************!*\
  !*** ../../node_modules/@uiw/react-codemirror/esm/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Annotation: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Annotation),\n/* harmony export */   AnnotationType: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.AnnotationType),\n/* harmony export */   BidiSpan: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.BidiSpan),\n/* harmony export */   BlockInfo: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.BlockInfo),\n/* harmony export */   BlockType: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.BlockType),\n/* harmony export */   ChangeDesc: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.ChangeDesc),\n/* harmony export */   ChangeSet: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.ChangeSet),\n/* harmony export */   CharCategory: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.CharCategory),\n/* harmony export */   Compartment: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Compartment),\n/* harmony export */   Decoration: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.Decoration),\n/* harmony export */   Direction: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.Direction),\n/* harmony export */   EditorSelection: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.EditorSelection),\n/* harmony export */   EditorState: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.EditorState),\n/* harmony export */   EditorView: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.EditorView),\n/* harmony export */   Facet: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Facet),\n/* harmony export */   GutterMarker: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.GutterMarker),\n/* harmony export */   Line: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Line),\n/* harmony export */   MapMode: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.MapMode),\n/* harmony export */   MatchDecorator: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.MatchDecorator),\n/* harmony export */   Prec: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Prec),\n/* harmony export */   Range: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Range),\n/* harmony export */   RangeSet: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.RangeSet),\n/* harmony export */   RangeSetBuilder: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.RangeSetBuilder),\n/* harmony export */   RangeValue: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.RangeValue),\n/* harmony export */   RectangleMarker: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.RectangleMarker),\n/* harmony export */   SelectionRange: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.SelectionRange),\n/* harmony export */   StateEffect: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.StateEffect),\n/* harmony export */   StateEffectType: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.StateEffectType),\n/* harmony export */   StateField: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.StateField),\n/* harmony export */   Text: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Text),\n/* harmony export */   Transaction: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Transaction),\n/* harmony export */   ViewPlugin: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.ViewPlugin),\n/* harmony export */   ViewUpdate: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.ViewUpdate),\n/* harmony export */   WidgetType: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.WidgetType),\n/* harmony export */   __test: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.__test),\n/* harmony export */   basicSetup: () => (/* reexport safe */ _uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_7__.basicSetup),\n/* harmony export */   closeHoverTooltips: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.closeHoverTooltips),\n/* harmony export */   codePointAt: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.codePointAt),\n/* harmony export */   codePointSize: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.codePointSize),\n/* harmony export */   color: () => (/* reexport safe */ _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_8__.color),\n/* harmony export */   combineConfig: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.combineConfig),\n/* harmony export */   countColumn: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.countColumn),\n/* harmony export */   crosshairCursor: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.crosshairCursor),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defaultLightThemeOption: () => (/* reexport safe */ _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_8__.defaultLightThemeOption),\n/* harmony export */   drawSelection: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.drawSelection),\n/* harmony export */   dropCursor: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.dropCursor),\n/* harmony export */   findClusterBreak: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.findClusterBreak),\n/* harmony export */   findColumn: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.findColumn),\n/* harmony export */   fromCodePoint: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.fromCodePoint),\n/* harmony export */   getDefaultExtensions: () => (/* reexport safe */ _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_8__.getDefaultExtensions),\n/* harmony export */   getDrawSelectionConfig: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.getDrawSelectionConfig),\n/* harmony export */   getPanel: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.getPanel),\n/* harmony export */   getStatistics: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_9__.getStatistics),\n/* harmony export */   getTooltip: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.getTooltip),\n/* harmony export */   gutter: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.gutter),\n/* harmony export */   gutterLineClass: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.gutterLineClass),\n/* harmony export */   gutterWidgetClass: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.gutterWidgetClass),\n/* harmony export */   gutters: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.gutters),\n/* harmony export */   hasHoverTooltips: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.hasHoverTooltips),\n/* harmony export */   highlightActiveLine: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightActiveLine),\n/* harmony export */   highlightActiveLineGutter: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightActiveLineGutter),\n/* harmony export */   highlightSpecialChars: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightSpecialChars),\n/* harmony export */   highlightTrailingWhitespace: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightTrailingWhitespace),\n/* harmony export */   highlightWhitespace: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightWhitespace),\n/* harmony export */   hoverTooltip: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.hoverTooltip),\n/* harmony export */   keymap: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.keymap),\n/* harmony export */   layer: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.layer),\n/* harmony export */   lineNumberMarkers: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.lineNumberMarkers),\n/* harmony export */   lineNumberWidgetMarker: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.lineNumberWidgetMarker),\n/* harmony export */   lineNumbers: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.lineNumbers),\n/* harmony export */   logException: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.logException),\n/* harmony export */   minimalSetup: () => (/* reexport safe */ _uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_7__.minimalSetup),\n/* harmony export */   oneDark: () => (/* reexport safe */ _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_8__.oneDark),\n/* harmony export */   oneDarkHighlightStyle: () => (/* reexport safe */ _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_8__.oneDarkHighlightStyle),\n/* harmony export */   oneDarkTheme: () => (/* reexport safe */ _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_8__.oneDarkTheme),\n/* harmony export */   panels: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.panels),\n/* harmony export */   placeholder: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.placeholder),\n/* harmony export */   rectangularSelection: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.rectangularSelection),\n/* harmony export */   repositionTooltips: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.repositionTooltips),\n/* harmony export */   runScopeHandlers: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.runScopeHandlers),\n/* harmony export */   scrollPastEnd: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.scrollPastEnd),\n/* harmony export */   showPanel: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.showPanel),\n/* harmony export */   showTooltip: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.showTooltip),\n/* harmony export */   tooltips: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.tooltips),\n/* harmony export */   useCodeMirror: () => (/* reexport safe */ _useCodeMirror__WEBPACK_IMPORTED_MODULE_3__.useCodeMirror)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/../../node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/objectWithoutPropertiesLoose */ \"(ssr)/../../node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _useCodeMirror__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useCodeMirror */ \"(ssr)/../../node_modules/@uiw/react-codemirror/esm/useCodeMirror.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/../../node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/../../node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @uiw/codemirror-extensions-basic-setup */ \"(ssr)/../../node_modules/@uiw/codemirror-extensions-basic-setup/esm/index.js\");\n/* harmony import */ var _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./getDefaultExtensions */ \"(ssr)/../../node_modules/@uiw/react-codemirror/esm/getDefaultExtensions.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/@uiw/react-codemirror/esm/utils.js\");\n\n\nvar _excluded = [\"className\", \"value\", \"selection\", \"extensions\", \"onChange\", \"onStatistics\", \"onCreateEditor\", \"onUpdate\", \"autoFocus\", \"theme\", \"height\", \"minHeight\", \"maxHeight\", \"width\", \"minWidth\", \"maxWidth\", \"basicSetup\", \"placeholder\", \"indentWithTab\", \"editable\", \"readOnly\", \"root\", \"initialState\"];\n\n\n\n\n\n\n\n\n\nvar ReactCodeMirror = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)((props, ref) => {\n  var {\n      className,\n      value = '',\n      selection,\n      extensions = [],\n      onChange,\n      onStatistics,\n      onCreateEditor,\n      onUpdate,\n      autoFocus,\n      theme = 'light',\n      height,\n      minHeight,\n      maxHeight,\n      width,\n      minWidth,\n      maxWidth,\n      basicSetup,\n      placeholder,\n      indentWithTab,\n      editable,\n      readOnly,\n      root,\n      initialState\n    } = props,\n    other = _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1___default()(props, _excluded);\n  var editor = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n  var {\n    state,\n    view,\n    container\n  } = (0,_useCodeMirror__WEBPACK_IMPORTED_MODULE_3__.useCodeMirror)({\n    container: editor.current,\n    root,\n    value,\n    autoFocus,\n    theme,\n    height,\n    minHeight,\n    maxHeight,\n    width,\n    minWidth,\n    maxWidth,\n    basicSetup,\n    placeholder,\n    indentWithTab,\n    editable,\n    readOnly,\n    selection,\n    onChange,\n    onStatistics,\n    onCreateEditor,\n    onUpdate,\n    extensions,\n    initialState\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, () => ({\n    editor: editor.current,\n    state: state,\n    view: view\n  }), [editor, container, state, view]);\n\n  // check type of value\n  if (typeof value !== 'string') {\n    throw new Error(\"value must be typeof string but got \" + typeof value);\n  }\n  var defaultClassNames = typeof theme === 'string' ? \"cm-theme-\" + theme : 'cm-theme';\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"div\", _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({\n    ref: editor,\n    className: \"\" + defaultClassNames + (className ? \" \" + className : '')\n  }, other));\n});\nReactCodeMirror.displayName = 'CodeMirror';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReactCodeMirror);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@uiw/react-codemirror/esm/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@uiw/react-codemirror/esm/theme/light.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/@uiw/react-codemirror/esm/theme/light.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLightThemeOption: () => (/* binding */ defaultLightThemeOption)\n/* harmony export */ });\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/../../node_modules/@codemirror/view/dist/index.js\");\n\nvar defaultLightThemeOption = _codemirror_view__WEBPACK_IMPORTED_MODULE_0__.EditorView.theme({\n  '&': {\n    backgroundColor: '#fff'\n  }\n}, {\n  dark: false\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0B1aXcvcmVhY3QtY29kZW1pcnJvci9lc20vdGhlbWUvbGlnaHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEM7QUFDdkMsOEJBQThCLHdEQUFVO0FBQy9DO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL0B1aXcvcmVhY3QtY29kZW1pcnJvci9lc20vdGhlbWUvbGlnaHQuanM/Y2U0MCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBFZGl0b3JWaWV3IH0gZnJvbSAnQGNvZGVtaXJyb3Ivdmlldyc7XG5leHBvcnQgdmFyIGRlZmF1bHRMaWdodFRoZW1lT3B0aW9uID0gRWRpdG9yVmlldy50aGVtZSh7XG4gICcmJzoge1xuICAgIGJhY2tncm91bmRDb2xvcjogJyNmZmYnXG4gIH1cbn0sIHtcbiAgZGFyazogZmFsc2Vcbn0pOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@uiw/react-codemirror/esm/theme/light.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@uiw/react-codemirror/esm/useCodeMirror.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/@uiw/react-codemirror/esm/useCodeMirror.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCodeMirror: () => (/* binding */ useCodeMirror)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/../../node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/../../node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getDefaultExtensions */ \"(ssr)/../../node_modules/@uiw/react-codemirror/esm/getDefaultExtensions.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/@uiw/react-codemirror/esm/utils.js\");\n\n\n\n\n\nvar External = _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.Annotation.define();\nvar emptyExtensions = [];\nfunction useCodeMirror(props) {\n  var {\n    value,\n    selection,\n    onChange,\n    onStatistics,\n    onCreateEditor,\n    onUpdate,\n    extensions = emptyExtensions,\n    autoFocus,\n    theme = 'light',\n    height = null,\n    minHeight = null,\n    maxHeight = null,\n    width = null,\n    minWidth = null,\n    maxWidth = null,\n    placeholder: placeholderStr = '',\n    editable = true,\n    readOnly = false,\n    indentWithTab: defaultIndentWithTab = true,\n    basicSetup: defaultBasicSetup = true,\n    root,\n    initialState\n  } = props;\n  var [container, setContainer] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  var [view, setView] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  var [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  var defaultThemeOption = _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.theme({\n    '&': {\n      height,\n      minHeight,\n      maxHeight,\n      width,\n      minWidth,\n      maxWidth\n    },\n    '& .cm-scroller': {\n      height: '100% !important'\n    }\n  });\n  var updateListener = _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.updateListener.of(vu => {\n    if (vu.docChanged && typeof onChange === 'function' &&\n    // Fix echoing of the remote changes:\n    // If transaction is market as remote we don't have to call `onChange` handler again\n    !vu.transactions.some(tr => tr.annotation(External))) {\n      var doc = vu.state.doc;\n      var _value = doc.toString();\n      onChange(_value, vu);\n    }\n    onStatistics && onStatistics((0,_utils__WEBPACK_IMPORTED_MODULE_2__.getStatistics)(vu));\n  });\n  var defaultExtensions = (0,_getDefaultExtensions__WEBPACK_IMPORTED_MODULE_1__.getDefaultExtensions)({\n    theme,\n    editable,\n    readOnly,\n    placeholder: placeholderStr,\n    indentWithTab: defaultIndentWithTab,\n    basicSetup: defaultBasicSetup\n  });\n  var getExtensions = [updateListener, defaultThemeOption, ...defaultExtensions];\n  if (onUpdate && typeof onUpdate === 'function') {\n    getExtensions.push(_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.updateListener.of(onUpdate));\n  }\n  getExtensions = getExtensions.concat(extensions);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (container && !state) {\n      var config = {\n        doc: value,\n        selection,\n        extensions: getExtensions\n      };\n      var stateCurrent = initialState ? _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.EditorState.fromJSON(initialState.json, config, initialState.fields) : _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.EditorState.create(config);\n      setState(stateCurrent);\n      if (!view) {\n        var viewCurrent = new _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView({\n          state: stateCurrent,\n          parent: container,\n          root\n        });\n        setView(viewCurrent);\n        onCreateEditor && onCreateEditor(viewCurrent, stateCurrent);\n      }\n    }\n    return () => {\n      if (view) {\n        setState(undefined);\n        setView(undefined);\n      }\n    };\n  }, [container, state]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => setContainer(props.container), [props.container]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => () => {\n    if (view) {\n      view.destroy();\n      setView(undefined);\n    }\n  }, [view]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (autoFocus && view) {\n      view.focus();\n    }\n  }, [autoFocus, view]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (view) {\n      view.dispatch({\n        effects: _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.StateEffect.reconfigure.of(getExtensions)\n      });\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [theme, extensions, height, minHeight, maxHeight, width, minWidth, maxWidth, placeholderStr, editable, readOnly, defaultIndentWithTab, defaultBasicSetup, onChange, onUpdate]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (value === undefined) {\n      return;\n    }\n    var currentValue = view ? view.state.doc.toString() : '';\n    if (view && value !== currentValue) {\n      view.dispatch({\n        changes: {\n          from: 0,\n          to: currentValue.length,\n          insert: value || ''\n        },\n        annotations: [External.of(true)]\n      });\n    }\n  }, [value, view]);\n  return {\n    state,\n    setState,\n    view,\n    setView,\n    container,\n    setContainer\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@uiw/react-codemirror/esm/useCodeMirror.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@uiw/react-codemirror/esm/utils.js":
/*!*************************************************************!*\
  !*** ../../node_modules/@uiw/react-codemirror/esm/utils.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getStatistics: () => (/* binding */ getStatistics)\n/* harmony export */ });\nvar getStatistics = view => {\n  return {\n    line: view.state.doc.lineAt(view.state.selection.main.from),\n    lineCount: view.state.doc.lines,\n    lineBreak: view.state.lineBreak,\n    length: view.state.doc.length,\n    readOnly: view.state.readOnly,\n    tabSize: view.state.tabSize,\n    selection: view.state.selection,\n    selectionAsSingle: view.state.selection.asSingle().main,\n    ranges: view.state.selection.ranges,\n    selectionCode: view.state.sliceDoc(view.state.selection.main.from, view.state.selection.main.to),\n    selections: view.state.selection.ranges.map(r => view.state.sliceDoc(r.from, r.to)),\n    selectedText: view.state.selection.ranges.some(r => !r.empty)\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0B1aXcvcmVhY3QtY29kZW1pcnJvci9lc20vdXRpbHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9AdWl3L3JlYWN0LWNvZGVtaXJyb3IvZXNtL3V0aWxzLmpzPzZlN2QiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBnZXRTdGF0aXN0aWNzID0gdmlldyA9PiB7XG4gIHJldHVybiB7XG4gICAgbGluZTogdmlldy5zdGF0ZS5kb2MubGluZUF0KHZpZXcuc3RhdGUuc2VsZWN0aW9uLm1haW4uZnJvbSksXG4gICAgbGluZUNvdW50OiB2aWV3LnN0YXRlLmRvYy5saW5lcyxcbiAgICBsaW5lQnJlYWs6IHZpZXcuc3RhdGUubGluZUJyZWFrLFxuICAgIGxlbmd0aDogdmlldy5zdGF0ZS5kb2MubGVuZ3RoLFxuICAgIHJlYWRPbmx5OiB2aWV3LnN0YXRlLnJlYWRPbmx5LFxuICAgIHRhYlNpemU6IHZpZXcuc3RhdGUudGFiU2l6ZSxcbiAgICBzZWxlY3Rpb246IHZpZXcuc3RhdGUuc2VsZWN0aW9uLFxuICAgIHNlbGVjdGlvbkFzU2luZ2xlOiB2aWV3LnN0YXRlLnNlbGVjdGlvbi5hc1NpbmdsZSgpLm1haW4sXG4gICAgcmFuZ2VzOiB2aWV3LnN0YXRlLnNlbGVjdGlvbi5yYW5nZXMsXG4gICAgc2VsZWN0aW9uQ29kZTogdmlldy5zdGF0ZS5zbGljZURvYyh2aWV3LnN0YXRlLnNlbGVjdGlvbi5tYWluLmZyb20sIHZpZXcuc3RhdGUuc2VsZWN0aW9uLm1haW4udG8pLFxuICAgIHNlbGVjdGlvbnM6IHZpZXcuc3RhdGUuc2VsZWN0aW9uLnJhbmdlcy5tYXAociA9PiB2aWV3LnN0YXRlLnNsaWNlRG9jKHIuZnJvbSwgci50bykpLFxuICAgIHNlbGVjdGVkVGV4dDogdmlldy5zdGF0ZS5zZWxlY3Rpb24ucmFuZ2VzLnNvbWUociA9PiAhci5lbXB0eSlcbiAgfTtcbn07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@uiw/react-codemirror/esm/utils.js\n");

/***/ })

};
;