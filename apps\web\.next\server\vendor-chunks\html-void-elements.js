"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/html-void-elements";
exports.ids = ["vendor-chunks/html-void-elements"];
exports.modules = {

/***/ "(ssr)/../../node_modules/html-void-elements/index.js":
/*!******************************************************!*\
  !*** ../../node_modules/html-void-elements/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   htmlVoidElements: () => (/* binding */ htmlVoidElements)\n/* harmony export */ });\n/**\n * List of HTML void tag names.\n *\n * @type {Array<string>}\n */\nconst htmlVoidElements = [\n  'area',\n  'base',\n  'basefont',\n  'bgsound',\n  'br',\n  'col',\n  'command',\n  'embed',\n  'frame',\n  'hr',\n  'image',\n  'img',\n  'input',\n  'isindex',\n  'keygen',\n  'link',\n  'menuitem',\n  'meta',\n  'nextid',\n  'param',\n  'source',\n  'track',\n  'wbr'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2h0bWwtdm9pZC1lbGVtZW50cy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL2h0bWwtdm9pZC1lbGVtZW50cy9pbmRleC5qcz80NGMyIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogTGlzdCBvZiBIVE1MIHZvaWQgdGFnIG5hbWVzLlxuICpcbiAqIEB0eXBlIHtBcnJheTxzdHJpbmc+fVxuICovXG5leHBvcnQgY29uc3QgaHRtbFZvaWRFbGVtZW50cyA9IFtcbiAgJ2FyZWEnLFxuICAnYmFzZScsXG4gICdiYXNlZm9udCcsXG4gICdiZ3NvdW5kJyxcbiAgJ2JyJyxcbiAgJ2NvbCcsXG4gICdjb21tYW5kJyxcbiAgJ2VtYmVkJyxcbiAgJ2ZyYW1lJyxcbiAgJ2hyJyxcbiAgJ2ltYWdlJyxcbiAgJ2ltZycsXG4gICdpbnB1dCcsXG4gICdpc2luZGV4JyxcbiAgJ2tleWdlbicsXG4gICdsaW5rJyxcbiAgJ21lbnVpdGVtJyxcbiAgJ21ldGEnLFxuICAnbmV4dGlkJyxcbiAgJ3BhcmFtJyxcbiAgJ3NvdXJjZScsXG4gICd0cmFjaycsXG4gICd3YnInXG5dXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/html-void-elements/index.js\n");

/***/ })

};
;