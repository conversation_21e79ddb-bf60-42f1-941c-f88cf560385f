"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/html-whitespace-sensitive-tag-names";
exports.ids = ["vendor-chunks/html-whitespace-sensitive-tag-names"];
exports.modules = {

/***/ "(ssr)/../../node_modules/html-whitespace-sensitive-tag-names/lib/index.js":
/*!***************************************************************************!*\
  !*** ../../node_modules/html-whitespace-sensitive-tag-names/lib/index.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   whitespaceSensitiveTagNames: () => (/* binding */ whitespaceSensitiveTagNames)\n/* harmony export */ });\n/**\n * List of HTML tag names that are whitespace sensitive.\n */\nconst whitespaceSensitiveTagNames = [\n  'pre',\n  'script',\n  'style',\n  'textarea'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2h0bWwtd2hpdGVzcGFjZS1zZW5zaXRpdmUtdGFnLW5hbWVzL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9odG1sLXdoaXRlc3BhY2Utc2Vuc2l0aXZlLXRhZy1uYW1lcy9saWIvaW5kZXguanM/NGJmMCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIExpc3Qgb2YgSFRNTCB0YWcgbmFtZXMgdGhhdCBhcmUgd2hpdGVzcGFjZSBzZW5zaXRpdmUuXG4gKi9cbmV4cG9ydCBjb25zdCB3aGl0ZXNwYWNlU2Vuc2l0aXZlVGFnTmFtZXMgPSBbXG4gICdwcmUnLFxuICAnc2NyaXB0JyxcbiAgJ3N0eWxlJyxcbiAgJ3RleHRhcmVhJ1xuXVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/html-whitespace-sensitive-tag-names/lib/index.js\n");

/***/ })

};
;