"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-definitions";
exports.ids = ["vendor-chunks/mdast-util-definitions"];
exports.modules = {

/***/ "(ssr)/../../node_modules/mdast-util-definitions/lib/index.js":
/*!**************************************************************!*\
  !*** ../../node_modules/mdast-util-definitions/lib/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   definitions: () => (/* binding */ definitions)\n/* harmony export */ });\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/../../node_modules/mdast-util-definitions/node_modules/unist-util-visit/lib/index.js\");\n/**\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').Definition} Definition\n */\n\n/**\n * @typedef {Root | Content} Node\n *\n * @callback GetDefinition\n *   Get a definition by identifier.\n * @param {string | null | undefined} [identifier]\n *   Identifier of definition.\n * @returns {Definition | null}\n *   Definition corresponding to `identifier` or `null`.\n */\n\n\n\nconst own = {}.hasOwnProperty\n\n/**\n * Find definitions in `tree`.\n *\n * Uses CommonMark precedence, which means that earlier definitions are\n * preferred over duplicate later definitions.\n *\n * @param {Node} tree\n *   Tree to check.\n * @returns {GetDefinition}\n *   Getter.\n */\nfunction definitions(tree) {\n  /** @type {Record<string, Definition>} */\n  const cache = Object.create(null)\n\n  if (!tree || !tree.type) {\n    throw new Error('mdast-util-definitions expected node')\n  }\n\n  (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_0__.visit)(tree, 'definition', (definition) => {\n    const id = clean(definition.identifier)\n    if (id && !own.call(cache, id)) {\n      cache[id] = definition\n    }\n  })\n\n  return definition\n\n  /** @type {GetDefinition} */\n  function definition(identifier) {\n    const id = clean(identifier)\n    // To do: next major: return `undefined` when not found.\n    return id && own.call(cache, id) ? cache[id] : null\n  }\n}\n\n/**\n * @param {string | null | undefined} [value]\n * @returns {string}\n */\nfunction clean(value) {\n  return String(value || '').toUpperCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/mdast-util-definitions/lib/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/mdast-util-definitions/node_modules/unist-util-visit-parents/lib/color.js":
/*!****************************************************************************************************!*\
  !*** ../../node_modules/mdast-util-definitions/node_modules/unist-util-visit-parents/lib/color.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   color: () => (/* binding */ color)\n/* harmony export */ });\n/**\n * @param {string} d\n * @returns {string}\n */\nfunction color(d) {\n  return '\\u001B[33m' + d + '\\u001B[39m'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtZGVmaW5pdGlvbnMvbm9kZV9tb2R1bGVzL3VuaXN0LXV0aWwtdmlzaXQtcGFyZW50cy9saWIvY29sb3IuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC1kZWZpbml0aW9ucy9ub2RlX21vZHVsZXMvdW5pc3QtdXRpbC12aXNpdC1wYXJlbnRzL2xpYi9jb2xvci5qcz84NGYzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHBhcmFtIHtzdHJpbmd9IGRcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjb2xvcihkKSB7XG4gIHJldHVybiAnXFx1MDAxQlszM20nICsgZCArICdcXHUwMDFCWzM5bSdcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/mdast-util-definitions/node_modules/unist-util-visit-parents/lib/color.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/mdast-util-definitions/node_modules/unist-util-visit-parents/lib/index.js":
/*!****************************************************************************************************!*\
  !*** ../../node_modules/mdast-util-definitions/node_modules/unist-util-visit-parents/lib/index.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CONTINUE: () => (/* binding */ CONTINUE),\n/* harmony export */   EXIT: () => (/* binding */ EXIT),\n/* harmony export */   SKIP: () => (/* binding */ SKIP),\n/* harmony export */   visitParents: () => (/* binding */ visitParents)\n/* harmony export */ });\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-is */ \"(ssr)/../../node_modules/unist-util-is/lib/index.js\");\n/* harmony import */ var _color_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./color.js */ \"(ssr)/../../node_modules/mdast-util-definitions/node_modules/unist-util-visit-parents/lib/color.js\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n * @typedef {import('unist-util-is').Test} Test\n */\n\n/**\n * @typedef {boolean | 'skip'} Action\n *   Union of the action types.\n *\n * @typedef {number} Index\n *   Move to the sibling at `index` next (after node itself is completely\n *   traversed).\n *\n *   Useful if mutating the tree, such as removing the node the visitor is\n *   currently on, or any of its previous siblings.\n *   Results less than 0 or greater than or equal to `children.length` stop\n *   traversing the parent.\n *\n * @typedef {[(Action | null | undefined | void)?, (Index | null | undefined)?]} ActionTuple\n *   List with one or two values, the first an action, the second an index.\n *\n * @typedef {Action | ActionTuple | Index | null | undefined | void} VisitorResult\n *   Any value that can be returned from a visitor.\n */\n\n/**\n * @template {Node} [Visited=Node]\n *   Visited node type.\n * @template {Parent} [Ancestor=Parent]\n *   Ancestor type.\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform the parent of node (the last of `ancestors`).\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of an ancestor still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Array<Ancestor>} ancestors\n *   Ancestors of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   Tree type.\n * @template {Test} [Check=string]\n *   Test type.\n * @typedef {Visitor<import('./complex-types.js').Matches<import('./complex-types.js').InclusiveDescendant<Tree>, Check>, Extract<import('./complex-types.js').InclusiveDescendant<Tree>, Parent>>} BuildVisitor\n *   Build a typed `Visitor` function from a tree and a test.\n *\n *   It will infer which values are passed as `node` and which as `parents`.\n */\n\n\n\n\n/**\n * Continue traversing as normal.\n */\nconst CONTINUE = true\n\n/**\n * Stop traversing immediately.\n */\nconst EXIT = false\n\n/**\n * Do not traverse this node’s children.\n */\nconst SKIP = 'skip'\n\n/**\n * Visit nodes, with ancestral information.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @param tree\n *   Tree to traverse.\n * @param test\n *   `unist-util-is`-compatible test\n * @param visitor\n *   Handle each node.\n * @param reverse\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns\n *   Nothing.\n */\nconst visitParents =\n  /**\n   * @type {(\n   *   (<Tree extends Node, Check extends Test>(tree: Tree, test: Check, visitor: BuildVisitor<Tree, Check>, reverse?: boolean | null | undefined) => void) &\n   *   (<Tree extends Node>(tree: Tree, visitor: BuildVisitor<Tree>, reverse?: boolean | null | undefined) => void)\n   * )}\n   */\n  (\n    /**\n     * @param {Node} tree\n     * @param {Test} test\n     * @param {Visitor<Node>} visitor\n     * @param {boolean | null | undefined} [reverse]\n     * @returns {void}\n     */\n    function (tree, test, visitor, reverse) {\n      if (typeof test === 'function' && typeof visitor !== 'function') {\n        reverse = visitor\n        // @ts-expect-error no visitor given, so `visitor` is test.\n        visitor = test\n        test = null\n      }\n\n      const is = (0,unist_util_is__WEBPACK_IMPORTED_MODULE_0__.convert)(test)\n      const step = reverse ? -1 : 1\n\n      factory(tree, undefined, [])()\n\n      /**\n       * @param {Node} node\n       * @param {number | undefined} index\n       * @param {Array<Parent>} parents\n       */\n      function factory(node, index, parents) {\n        /** @type {Record<string, unknown>} */\n        // @ts-expect-error: hush\n        const value = node && typeof node === 'object' ? node : {}\n\n        if (typeof value.type === 'string') {\n          const name =\n            // `hast`\n            typeof value.tagName === 'string'\n              ? value.tagName\n              : // `xast`\n              typeof value.name === 'string'\n              ? value.name\n              : undefined\n\n          Object.defineProperty(visit, 'name', {\n            value:\n              'node (' + (0,_color_js__WEBPACK_IMPORTED_MODULE_1__.color)(node.type + (name ? '<' + name + '>' : '')) + ')'\n          })\n        }\n\n        return visit\n\n        function visit() {\n          /** @type {ActionTuple} */\n          let result = []\n          /** @type {ActionTuple} */\n          let subresult\n          /** @type {number} */\n          let offset\n          /** @type {Array<Parent>} */\n          let grandparents\n\n          if (!test || is(node, index, parents[parents.length - 1] || null)) {\n            result = toResult(visitor(node, parents))\n\n            if (result[0] === EXIT) {\n              return result\n            }\n          }\n\n          // @ts-expect-error looks like a parent.\n          if (node.children && result[0] !== SKIP) {\n            // @ts-expect-error looks like a parent.\n            offset = (reverse ? node.children.length : -1) + step\n            // @ts-expect-error looks like a parent.\n            grandparents = parents.concat(node)\n\n            // @ts-expect-error looks like a parent.\n            while (offset > -1 && offset < node.children.length) {\n              // @ts-expect-error looks like a parent.\n              subresult = factory(node.children[offset], offset, grandparents)()\n\n              if (subresult[0] === EXIT) {\n                return subresult\n              }\n\n              offset =\n                typeof subresult[1] === 'number' ? subresult[1] : offset + step\n            }\n          }\n\n          return result\n        }\n      }\n    }\n  )\n\n/**\n * Turn a return value into a clean result.\n *\n * @param {VisitorResult} value\n *   Valid return values from visitors.\n * @returns {ActionTuple}\n *   Clean result.\n */\nfunction toResult(value) {\n  if (Array.isArray(value)) {\n    return value\n  }\n\n  if (typeof value === 'number') {\n    return [CONTINUE, value]\n  }\n\n  return [value]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/mdast-util-definitions/node_modules/unist-util-visit-parents/lib/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/mdast-util-definitions/node_modules/unist-util-visit/lib/index.js":
/*!********************************************************************************************!*\
  !*** ../../node_modules/mdast-util-definitions/node_modules/unist-util-visit/lib/index.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CONTINUE: () => (/* reexport safe */ unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_0__.CONTINUE),\n/* harmony export */   EXIT: () => (/* reexport safe */ unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_0__.EXIT),\n/* harmony export */   SKIP: () => (/* reexport safe */ unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_0__.SKIP),\n/* harmony export */   visit: () => (/* binding */ visit)\n/* harmony export */ });\n/* harmony import */ var unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit-parents */ \"(ssr)/../../node_modules/mdast-util-definitions/node_modules/unist-util-visit-parents/lib/index.js\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n * @typedef {import('unist-util-is').Test} Test\n * @typedef {import('unist-util-visit-parents').VisitorResult} VisitorResult\n */\n\n/**\n * Check if `Child` can be a child of `Ancestor`.\n *\n * Returns the ancestor when `Child` can be a child of `Ancestor`, or returns\n * `never`.\n *\n * @template {Node} Ancestor\n *   Node type.\n * @template {Node} Child\n *   Node type.\n * @typedef {(\n *   Ancestor extends Parent\n *     ? Child extends Ancestor['children'][number]\n *       ? Ancestor\n *       : never\n *     : never\n * )} ParentsOf\n */\n\n/**\n * @template {Node} [Visited=Node]\n *   Visited node type.\n * @template {Parent} [Ancestor=Parent]\n *   Ancestor type.\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform `parent`.\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of `parent` still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Visited extends Node ? number | null : never} index\n *   Index of `node` in `parent`.\n * @param {Ancestor extends Node ? Ancestor | null : never} parent\n *   Parent of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n */\n\n/**\n * Build a typed `Visitor` function from a node and all possible parents.\n *\n * It will infer which values are passed as `node` and which as `parent`.\n *\n * @template {Node} Visited\n *   Node type.\n * @template {Parent} Ancestor\n *   Parent type.\n * @typedef {Visitor<Visited, ParentsOf<Ancestor, Visited>>} BuildVisitorFromMatch\n */\n\n/**\n * Build a typed `Visitor` function from a list of descendants and a test.\n *\n * It will infer which values are passed as `node` and which as `parent`.\n *\n * @template {Node} Descendant\n *   Node type.\n * @template {Test} Check\n *   Test type.\n * @typedef {(\n *   BuildVisitorFromMatch<\n *     import('unist-util-visit-parents/complex-types.js').Matches<Descendant, Check>,\n *     Extract<Descendant, Parent>\n *   >\n * )} BuildVisitorFromDescendants\n */\n\n/**\n * Build a typed `Visitor` function from a tree and a test.\n *\n * It will infer which values are passed as `node` and which as `parent`.\n *\n * @template {Node} [Tree=Node]\n *   Node type.\n * @template {Test} [Check=string]\n *   Test type.\n * @typedef {(\n *   BuildVisitorFromDescendants<\n *     import('unist-util-visit-parents/complex-types.js').InclusiveDescendant<Tree>,\n *     Check\n *   >\n * )} BuildVisitor\n */\n\n\n\n/**\n * Visit nodes.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @param tree\n *   Tree to traverse.\n * @param test\n *   `unist-util-is`-compatible test\n * @param visitor\n *   Handle each node.\n * @param reverse\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns\n *   Nothing.\n */\nconst visit =\n  /**\n   * @type {(\n   *   (<Tree extends Node, Check extends Test>(tree: Tree, test: Check, visitor: BuildVisitor<Tree, Check>, reverse?: boolean | null | undefined) => void) &\n   *   (<Tree extends Node>(tree: Tree, visitor: BuildVisitor<Tree>, reverse?: boolean | null | undefined) => void)\n   * )}\n   */\n  (\n    /**\n     * @param {Node} tree\n     * @param {Test} test\n     * @param {Visitor} visitor\n     * @param {boolean | null | undefined} [reverse]\n     * @returns {void}\n     */\n    function (tree, test, visitor, reverse) {\n      if (typeof test === 'function' && typeof visitor !== 'function') {\n        reverse = visitor\n        visitor = test\n        test = null\n      }\n\n      (0,unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_0__.visitParents)(tree, test, overload, reverse)\n\n      /**\n       * @param {Node} node\n       * @param {Array<Parent>} parents\n       */\n      function overload(node, parents) {\n        const parent = parents[parents.length - 1]\n        return visitor(\n          node,\n          parent ? parent.children.indexOf(node) : null,\n          parent\n        )\n      }\n    }\n  )\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/mdast-util-definitions/node_modules/unist-util-visit/lib/index.js\n");

/***/ })

};
;