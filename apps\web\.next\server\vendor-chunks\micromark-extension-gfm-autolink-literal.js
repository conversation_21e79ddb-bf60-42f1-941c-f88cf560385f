"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-autolink-literal";
exports.ids = ["vendor-chunks/micromark-extension-gfm-autolink-literal"];
exports.modules = {

/***/ "(ssr)/../../node_modules/micromark-extension-gfm-autolink-literal/dev/lib/html.js":
/*!***********************************************************************************!*\
  !*** ../../node_modules/micromark-extension-gfm-autolink-literal/dev/lib/html.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmAutolinkLiteralHtml: () => (/* binding */ gfmAutolinkLiteralHtml)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/../../node_modules/micromark-extension-gfm-autolink-literal/node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @import {CompileContext, Handle, HtmlExtension, Token} from 'micromark-util-types'\n */\n\n\n\n/**\n * Create an HTML extension for `micromark` to support GitHub autolink literal\n * when serializing to HTML.\n *\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GitHub autolink literal when serializing to HTML.\n */\nfunction gfmAutolinkLiteralHtml() {\n  return {\n    exit: {literalAutolinkEmail, literalAutolinkHttp, literalAutolinkWww}\n  }\n}\n\n/**\n * @this {CompileContext}\n * @type {Handle}\n */\nfunction literalAutolinkWww(token) {\n  anchorFromToken.call(this, token, 'http://')\n}\n\n/**\n * @this {CompileContext}\n * @type {Handle}\n */\nfunction literalAutolinkEmail(token) {\n  anchorFromToken.call(this, token, 'mailto:')\n}\n\n/**\n * @this {CompileContext}\n * @type {Handle}\n */\nfunction literalAutolinkHttp(token) {\n  anchorFromToken.call(this, token)\n}\n\n/**\n * @this CompileContext\n * @param {Token} token\n * @param {string | null | undefined} [protocol]\n * @returns {undefined}\n */\nfunction anchorFromToken(token, protocol) {\n  const url = this.sliceSerialize(token)\n  this.tag('<a href=\"' + (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.sanitizeUri)((protocol || '') + url) + '\">')\n  this.raw(this.encode(url))\n  this.tag('</a>')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/micromark-extension-gfm-autolink-literal/dev/lib/html.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js":
/*!*************************************************************************************!*\
  !*** ../../node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmAutolinkLiteral: () => (/* binding */ gfmAutolinkLiteral)\n/* harmony export */ });\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/../../node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/../../node_modules/micromark-util-symbol/lib/codes.js\");\n/**\n * @import {Code, ConstructRecord, Event, Extension, Previous, State, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\n\n\n\nconst wwwPrefix = {tokenize: tokenizeWwwPrefix, partial: true}\nconst domain = {tokenize: tokenizeDomain, partial: true}\nconst path = {tokenize: tokenizePath, partial: true}\nconst trail = {tokenize: tokenizeTrail, partial: true}\nconst emailDomainDotTrail = {\n  tokenize: tokenizeEmailDomainDotTrail,\n  partial: true\n}\n\nconst wwwAutolink = {\n  name: 'wwwAutolink',\n  tokenize: tokenizeWwwAutolink,\n  previous: previousWww\n}\n\nconst protocolAutolink = {\n  name: 'protocolAutolink',\n  tokenize: tokenizeProtocolAutolink,\n  previous: previousProtocol\n}\n\nconst emailAutolink = {\n  name: 'emailAutolink',\n  tokenize: tokenizeEmailAutolink,\n  previous: previousEmail\n}\n\n/** @type {ConstructRecord} */\nconst text = {}\n\n/**\n * Create an extension for `micromark` to support GitHub autolink literal\n * syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to enable GFM\n *   autolink literal syntax.\n */\nfunction gfmAutolinkLiteral() {\n  return {text}\n}\n\n/** @type {Code} */\nlet code = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit0\n\n// Add alphanumerics.\nwhile (code < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftCurlyBrace) {\n  text[code] = emailAutolink\n  code++\n  if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.colon) code = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseA\n  else if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket) code = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseA\n}\n\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.plusSign] = emailAutolink\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash] = emailAutolink\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot] = emailAutolink\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore] = emailAutolink\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseH] = [emailAutolink, protocolAutolink]\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseH] = [emailAutolink, protocolAutolink]\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseW] = [emailAutolink, wwwAutolink]\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseW] = [emailAutolink, wwwAutolink]\n\n// To do: perform email autolink literals on events, afterwards.\n// That’s where `markdown-rs` and `cmark-gfm` perform it.\n// It should look for `@`, then for atext backwards, and then for a label\n// forwards.\n// To do: `mailto:`, `xmpp:` protocol as prefix.\n\n/**\n * Email autolink literal.\n *\n * ```markdown\n * > | a <EMAIL> b\n *       ^^^^^^^^^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeEmailAutolink(effects, ok, nok) {\n  const self = this\n  /** @type {boolean | undefined} */\n  let dot\n  /** @type {boolean} */\n  let data\n\n  return start\n\n  /**\n   * Start of email autolink literal.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (\n      !gfmAtext(code) ||\n      !previousEmail.call(self, self.previous) ||\n      previousUnbalanced(self.events)\n    ) {\n      return nok(code)\n    }\n\n    effects.enter('literalAutolink')\n    effects.enter('literalAutolinkEmail')\n    return atext(code)\n  }\n\n  /**\n   * In email atext.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atext(code) {\n    if (gfmAtext(code)) {\n      effects.consume(code)\n      return atext\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.atSign) {\n      effects.consume(code)\n      return emailDomain\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In email domain.\n   *\n   * The reference code is a bit overly complex as it handles the `@`, of which\n   * there may be just one.\n   * Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L318>\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailDomain(code) {\n    // Dot followed by alphanumerical (not `-` or `_`).\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot) {\n      return effects.check(\n        emailDomainDotTrail,\n        emailDomainAfter,\n        emailDomainDot\n      )(code)\n    }\n\n    // Alphanumerical, `-`, and `_`.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlphanumeric)(code)\n    ) {\n      data = true\n      effects.consume(code)\n      return emailDomain\n    }\n\n    // To do: `/` if xmpp.\n\n    // Note: normally we’d truncate trailing punctuation from the link.\n    // However, email autolink literals cannot contain any of those markers,\n    // except for `.`, but that can only occur if it isn’t trailing.\n    // So we can ignore truncating!\n    return emailDomainAfter(code)\n  }\n\n  /**\n   * In email domain, on dot that is not a trail.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *                      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailDomainDot(code) {\n    effects.consume(code)\n    dot = true\n    return emailDomain\n  }\n\n  /**\n   * After email domain.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *                          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailDomainAfter(code) {\n    // Domain must not be empty, must include a dot, and must end in alphabetical.\n    // Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L332>.\n    if (data && dot && (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(self.previous)) {\n      effects.exit('literalAutolinkEmail')\n      effects.exit('literalAutolink')\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n\n/**\n * `www` autolink literal.\n *\n * ```markdown\n * > | a www.example.org b\n *       ^^^^^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeWwwAutolink(effects, ok, nok) {\n  const self = this\n\n  return wwwStart\n\n  /**\n   * Start of www autolink literal.\n   *\n   * ```markdown\n   * > | www.example.com/a?b#c\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function wwwStart(code) {\n    if (\n      (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseW && code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseW) ||\n      !previousWww.call(self, self.previous) ||\n      previousUnbalanced(self.events)\n    ) {\n      return nok(code)\n    }\n\n    effects.enter('literalAutolink')\n    effects.enter('literalAutolinkWww')\n    // Note: we *check*, so we can discard the `www.` we parsed.\n    // If it worked, we consider it as a part of the domain.\n    return effects.check(\n      wwwPrefix,\n      effects.attempt(domain, effects.attempt(path, wwwAfter), nok),\n      nok\n    )(code)\n  }\n\n  /**\n   * After a www autolink literal.\n   *\n   * ```markdown\n   * > | www.example.com/a?b#c\n   *                          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function wwwAfter(code) {\n    effects.exit('literalAutolinkWww')\n    effects.exit('literalAutolink')\n    return ok(code)\n  }\n}\n\n/**\n * Protocol autolink literal.\n *\n * ```markdown\n * > | a https://example.org b\n *       ^^^^^^^^^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeProtocolAutolink(effects, ok, nok) {\n  const self = this\n  let buffer = ''\n  let seen = false\n\n  return protocolStart\n\n  /**\n   * Start of protocol autolink literal.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function protocolStart(code) {\n    if (\n      (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseH || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseH) &&\n      previousProtocol.call(self, self.previous) &&\n      !previousUnbalanced(self.events)\n    ) {\n      effects.enter('literalAutolink')\n      effects.enter('literalAutolinkHttp')\n      buffer += String.fromCodePoint(code)\n      effects.consume(code)\n      return protocolPrefixInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In protocol.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *     ^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function protocolPrefixInside(code) {\n    // `5` is size of `https`\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(code) && buffer.length < 5) {\n      // @ts-expect-error: definitely number.\n      buffer += String.fromCodePoint(code)\n      effects.consume(code)\n      return protocolPrefixInside\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.colon) {\n      const protocol = buffer.toLowerCase()\n\n      if (protocol === 'http' || protocol === 'https') {\n        effects.consume(code)\n        return protocolSlashesInside\n      }\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In slashes.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *           ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function protocolSlashesInside(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.slash) {\n      effects.consume(code)\n\n      if (seen) {\n        return afterProtocol\n      }\n\n      seen = true\n      return protocolSlashesInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After protocol, before domain.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterProtocol(code) {\n    // To do: this is different from `markdown-rs`:\n    // https://github.com/wooorm/markdown-rs/blob/b3a921c761309ae00a51fe348d8a43adbc54b518/src/construct/gfm_autolink_literal.rs#L172-L182\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiControl)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodePunctuation)(code)\n      ? nok(code)\n      : effects.attempt(domain, effects.attempt(path, protocolAfter), nok)(code)\n  }\n\n  /**\n   * After a protocol autolink literal.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *                              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function protocolAfter(code) {\n    effects.exit('literalAutolinkHttp')\n    effects.exit('literalAutolink')\n    return ok(code)\n  }\n}\n\n/**\n * `www` prefix.\n *\n * ```markdown\n * > | a www.example.org b\n *       ^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeWwwPrefix(effects, ok, nok) {\n  let size = 0\n\n  return wwwPrefixInside\n\n  /**\n   * In www prefix.\n   *\n   * ```markdown\n   * > | www.example.com\n   *     ^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function wwwPrefixInside(code) {\n    if ((code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseW || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseW) && size < 3) {\n      size++\n      effects.consume(code)\n      return wwwPrefixInside\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot && size === 3) {\n      effects.consume(code)\n      return wwwPrefixAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After www prefix.\n   *\n   * ```markdown\n   * > | www.example.com\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function wwwPrefixAfter(code) {\n    // If there is *anything*, we can link.\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ? nok(code) : ok(code)\n  }\n}\n\n/**\n * Domain.\n *\n * ```markdown\n * > | a https://example.org b\n *               ^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeDomain(effects, ok, nok) {\n  /** @type {boolean | undefined} */\n  let underscoreInLastSegment\n  /** @type {boolean | undefined} */\n  let underscoreInLastLastSegment\n  /** @type {boolean | undefined} */\n  let seen\n\n  return domainInside\n\n  /**\n   * In domain.\n   *\n   * ```markdown\n   * > | https://example.com/a\n   *             ^^^^^^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function domainInside(code) {\n    // Check whether this marker, which is a trailing punctuation\n    // marker, optionally followed by more trailing markers, and then\n    // followed by an end.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore) {\n      return effects.check(trail, domainAfter, domainAtPunctuation)(code)\n    }\n\n    // GH documents that only alphanumerics (other than `-`, `.`, and `_`) can\n    // occur, which sounds like ASCII only, but they also support `www.點看.com`,\n    // so that’s Unicode.\n    // Instead of some new production for Unicode alphanumerics, markdown\n    // already has that for Unicode punctuation and whitespace, so use those.\n    // Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L12>.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code) ||\n      (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash && (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodePunctuation)(code))\n    ) {\n      return domainAfter(code)\n    }\n\n    seen = true\n    effects.consume(code)\n    return domainInside\n  }\n\n  /**\n   * In domain, at potential trailing punctuation, that was not trailing.\n   *\n   * ```markdown\n   * > | https://example.com\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */\n  function domainAtPunctuation(code) {\n    // There is an underscore in the last segment of the domain\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore) {\n      underscoreInLastSegment = true\n    }\n    // Otherwise, it’s a `.`: save the last segment underscore in the\n    // penultimate segment slot.\n    else {\n      underscoreInLastLastSegment = underscoreInLastSegment\n      underscoreInLastSegment = undefined\n    }\n\n    effects.consume(code)\n    return domainInside\n  }\n\n  /**\n   * After domain.\n   *\n   * ```markdown\n   * > | https://example.com/a\n   *                        ^\n   * ```\n   *\n   * @type {State} */\n  function domainAfter(code) {\n    // Note: that’s GH says a dot is needed, but it’s not true:\n    // <https://github.com/github/cmark-gfm/issues/279>\n    if (underscoreInLastLastSegment || underscoreInLastSegment || !seen) {\n      return nok(code)\n    }\n\n    return ok(code)\n  }\n}\n\n/**\n * Path.\n *\n * ```markdown\n * > | a https://example.org/stuff b\n *                          ^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizePath(effects, ok) {\n  let sizeOpen = 0\n  let sizeClose = 0\n\n  return pathInside\n\n  /**\n   * In path.\n   *\n   * ```markdown\n   * > | https://example.com/a\n   *                        ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function pathInside(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftParenthesis) {\n      sizeOpen++\n      effects.consume(code)\n      return pathInside\n    }\n\n    // To do: `markdown-rs` also needs this.\n    // If this is a paren, and there are less closings than openings,\n    // we don’t check for a trail.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightParenthesis && sizeClose < sizeOpen) {\n      return pathAtPunctuation(code)\n    }\n\n    // Check whether this trailing punctuation marker is optionally\n    // followed by more trailing markers, and then followed\n    // by an end.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.exclamationMark ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.quotationMark ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.apostrophe ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightParenthesis ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.comma ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.colon ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.semicolon ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.questionMark ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde\n    ) {\n      return effects.check(trail, ok, pathAtPunctuation)(code)\n    }\n\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code)\n    ) {\n      return ok(code)\n    }\n\n    effects.consume(code)\n    return pathInside\n  }\n\n  /**\n   * In path, at potential trailing punctuation, that was not trailing.\n   *\n   * ```markdown\n   * > | https://example.com/a\"b\n   *                          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function pathAtPunctuation(code) {\n    // Count closing parens.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightParenthesis) {\n      sizeClose++\n    }\n\n    effects.consume(code)\n    return pathInside\n  }\n}\n\n/**\n * Trail.\n *\n * This calls `ok` if this *is* the trail, followed by an end, which means\n * the entire trail is not part of the link.\n * It calls `nok` if this *is* part of the link.\n *\n * ```markdown\n * > | https://example.com\").\n *                        ^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeTrail(effects, ok, nok) {\n  return trail\n\n  /**\n   * In trail of domain or path.\n   *\n   * ```markdown\n   * > | https://example.com\").\n   *                        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function trail(code) {\n    // Regular trailing punctuation.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.exclamationMark ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.quotationMark ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.apostrophe ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightParenthesis ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.comma ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.colon ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.semicolon ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.questionMark ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde\n    ) {\n      effects.consume(code)\n      return trail\n    }\n\n    // `&` followed by one or more alphabeticals and then a `;`, is\n    // as a whole considered as trailing punctuation.\n    // In all other cases, it is considered as continuation of the URL.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand) {\n      effects.consume(code)\n      return trailCharacterReferenceStart\n    }\n\n    // Needed because we allow literals after `[`, as we fix:\n    // <https://github.com/github/cmark-gfm/issues/278>.\n    // Check that it is not followed by `(` or `[`.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket) {\n      effects.consume(code)\n      return trailBracketAfter\n    }\n\n    if (\n      // `<` is an end.\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan ||\n      // So is whitespace.\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code)\n    ) {\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In trail, after `]`.\n   *\n   * > 👉 **Note**: this deviates from `cmark-gfm` to fix a bug.\n   * > See end of <https://github.com/github/cmark-gfm/issues/278> for more.\n   *\n   * ```markdown\n   * > | https://example.com](\n   *                         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function trailBracketAfter(code) {\n    // Whitespace or something that could start a resource or reference is the end.\n    // Switch back to trail otherwise.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftParenthesis ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code)\n    ) {\n      return ok(code)\n    }\n\n    return trail(code)\n  }\n\n  /**\n   * In character-reference like trail, after `&`.\n   *\n   * ```markdown\n   * > | https://example.com&amp;).\n   *                         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function trailCharacterReferenceStart(code) {\n    // When non-alpha, it’s not a trail.\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(code) ? trailCharacterReferenceInside(code) : nok(code)\n  }\n\n  /**\n   * In character-reference like trail.\n   *\n   * ```markdown\n   * > | https://example.com&amp;).\n   *                         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function trailCharacterReferenceInside(code) {\n    // Switch back to trail if this is well-formed.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.semicolon) {\n      effects.consume(code)\n      return trail\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(code)) {\n      effects.consume(code)\n      return trailCharacterReferenceInside\n    }\n\n    // It’s not a trail.\n    return nok(code)\n  }\n}\n\n/**\n * Dot in email domain trail.\n *\n * This calls `ok` if this *is* the trail, followed by an end, which means\n * the trail is not part of the link.\n * It calls `nok` if this *is* part of the link.\n *\n * ```markdown\n * > | <EMAIL>.\n *                        ^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeEmailDomainDotTrail(effects, ok, nok) {\n  return start\n\n  /**\n   * Dot.\n   *\n   * ```markdown\n   * > | <EMAIL>.\n   *                    ^   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // Must be dot.\n    effects.consume(code)\n    return after\n  }\n\n  /**\n   * After dot.\n   *\n   * ```markdown\n   * > | <EMAIL>.\n   *                     ^   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // Not a trail if alphanumeric.\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlphanumeric)(code) ? nok(code) : ok(code)\n  }\n}\n\n/**\n * See:\n * <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L156>.\n *\n * @type {Previous}\n */\nfunction previousWww(code) {\n  return (\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftParenthesis ||\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk ||\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore ||\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket ||\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket ||\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde ||\n    (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code)\n  )\n}\n\n/**\n * See:\n * <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L214>.\n *\n * @type {Previous}\n */\nfunction previousProtocol(code) {\n  return !(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(code)\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Previous}\n */\nfunction previousEmail(code) {\n  // Do not allow a slash “inside” atext.\n  // The reference code is a bit weird, but that’s what it results in.\n  // Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L307>.\n  // Other than slash, every preceding character is allowed.\n  return !(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.slash || gfmAtext(code))\n}\n\n/**\n * @param {Code} code\n * @returns {boolean}\n */\nfunction gfmAtext(code) {\n  return (\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.plusSign ||\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash ||\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot ||\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore ||\n    (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlphanumeric)(code)\n  )\n}\n\n/**\n * @param {Array<Event>} events\n * @returns {boolean}\n */\nfunction previousUnbalanced(events) {\n  let index = events.length\n  let result = false\n\n  while (index--) {\n    const token = events[index][1]\n\n    if (\n      (token.type === 'labelLink' || token.type === 'labelImage') &&\n      !token._balanced\n    ) {\n      result = true\n      break\n    }\n\n    // If we’ve seen this token, and it was marked as not having any unbalanced\n    // bracket before it, we can exit.\n    if (token._gfmAutolinkLiteralWalkedInto) {\n      result = false\n      break\n    }\n  }\n\n  if (events.length > 0 && !result) {\n    // Mark the last token as “walked into” w/o finding\n    // anything.\n    events[events.length - 1][1]._gfmAutolinkLiteralWalkedInto = true\n  }\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/micromark-extension-gfm-autolink-literal/node_modules/micromark-util-encode/index.js":
/*!***************************************************************************************************************!*\
  !*** ../../node_modules/micromark-extension-gfm-autolink-literal/node_modules/micromark-util-encode/index.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encode: () => (/* binding */ encode)\n/* harmony export */ });\nconst characterReferences = {'\"': 'quot', '&': 'amp', '<': 'lt', '>': 'gt'}\n\n/**\n * Encode only the dangerous HTML characters.\n *\n * This ensures that certain characters which have special meaning in HTML are\n * dealt with.\n * Technically, we can skip `>` and `\"` in many cases, but CM includes them.\n *\n * @param {string} value\n *   Value to encode.\n * @returns {string}\n *   Encoded value.\n */\nfunction encode(value) {\n  return value.replace(/[\"&<>]/g, replace)\n\n  /**\n   * @param {string} value\n   *   Value to replace.\n   * @returns {string}\n   *   Encoded value.\n   */\n  function replace(value) {\n    return (\n      '&' +\n      characterReferences[\n        /** @type {keyof typeof characterReferences} */ (value)\n      ] +\n      ';'\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay1leHRlbnNpb24tZ2ZtLWF1dG9saW5rLWxpdGVyYWwvbm9kZV9tb2R1bGVzL21pY3JvbWFyay11dGlsLWVuY29kZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQTZCOztBQUU3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDs7QUFFQTtBQUNBLGFBQWEsUUFBUTtBQUNyQjtBQUNBLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsa0NBQWtDO0FBQ3JEO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWV4dGVuc2lvbi1nZm0tYXV0b2xpbmstbGl0ZXJhbC9ub2RlX21vZHVsZXMvbWljcm9tYXJrLXV0aWwtZW5jb2RlL2luZGV4LmpzP2QwMmMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgY2hhcmFjdGVyUmVmZXJlbmNlcyA9IHsnXCInOiAncXVvdCcsICcmJzogJ2FtcCcsICc8JzogJ2x0JywgJz4nOiAnZ3QnfVxuXG4vKipcbiAqIEVuY29kZSBvbmx5IHRoZSBkYW5nZXJvdXMgSFRNTCBjaGFyYWN0ZXJzLlxuICpcbiAqIFRoaXMgZW5zdXJlcyB0aGF0IGNlcnRhaW4gY2hhcmFjdGVycyB3aGljaCBoYXZlIHNwZWNpYWwgbWVhbmluZyBpbiBIVE1MIGFyZVxuICogZGVhbHQgd2l0aC5cbiAqIFRlY2huaWNhbGx5LCB3ZSBjYW4gc2tpcCBgPmAgYW5kIGBcImAgaW4gbWFueSBjYXNlcywgYnV0IENNIGluY2x1ZGVzIHRoZW0uXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IHZhbHVlXG4gKiAgIFZhbHVlIHRvIGVuY29kZS5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIEVuY29kZWQgdmFsdWUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBlbmNvZGUodmFsdWUpIHtcbiAgcmV0dXJuIHZhbHVlLnJlcGxhY2UoL1tcIiY8Pl0vZywgcmVwbGFjZSlcblxuICAvKipcbiAgICogQHBhcmFtIHtzdHJpbmd9IHZhbHVlXG4gICAqICAgVmFsdWUgdG8gcmVwbGFjZS5cbiAgICogQHJldHVybnMge3N0cmluZ31cbiAgICogICBFbmNvZGVkIHZhbHVlLlxuICAgKi9cbiAgZnVuY3Rpb24gcmVwbGFjZSh2YWx1ZSkge1xuICAgIHJldHVybiAoXG4gICAgICAnJicgK1xuICAgICAgY2hhcmFjdGVyUmVmZXJlbmNlc1tcbiAgICAgICAgLyoqIEB0eXBlIHtrZXlvZiB0eXBlb2YgY2hhcmFjdGVyUmVmZXJlbmNlc30gKi8gKHZhbHVlKVxuICAgICAgXSArXG4gICAgICAnOydcbiAgICApXG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/micromark-extension-gfm-autolink-literal/node_modules/micromark-util-encode/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/micromark-extension-gfm-autolink-literal/node_modules/micromark-util-sanitize-uri/dev/index.js":
/*!*************************************************************************************************************************!*\
  !*** ../../node_modules/micromark-extension-gfm-autolink-literal/node_modules/micromark-util-sanitize-uri/dev/index.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   normalizeUri: () => (/* binding */ normalizeUri),\n/* harmony export */   sanitizeUri: () => (/* binding */ sanitizeUri)\n/* harmony export */ });\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/../../node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_encode__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-encode */ \"(ssr)/../../node_modules/micromark-extension-gfm-autolink-literal/node_modules/micromark-util-encode/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/../../node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/../../node_modules/micromark-util-symbol/lib/values.js\");\n\n\n\n\n/**\n * Make a value safe for injection as a URL.\n *\n * This encodes unsafe characters with percent-encoding and skips already\n * encoded sequences (see `normalizeUri`).\n * Further unsafe characters are encoded as character references (see\n * `micromark-util-encode`).\n *\n * A regex of allowed protocols can be given, in which case the URL is\n * sanitized.\n * For example, `/^(https?|ircs?|mailto|xmpp)$/i` can be used for `a[href]`, or\n * `/^https?$/i` for `img[src]` (this is what `github.com` allows).\n * If the URL includes an unknown protocol (one not matched by `protocol`, such\n * as a dangerous example, `javascript:`), the value is ignored.\n *\n * @param {string | null | undefined} url\n *   URI to sanitize.\n * @param {RegExp | null | undefined} [protocol]\n *   Allowed protocols.\n * @returns {string}\n *   Sanitized URI.\n */\nfunction sanitizeUri(url, protocol) {\n  const value = (0,micromark_util_encode__WEBPACK_IMPORTED_MODULE_0__.encode)(normalizeUri(url || ''))\n\n  if (!protocol) {\n    return value\n  }\n\n  const colon = value.indexOf(':')\n  const questionMark = value.indexOf('?')\n  const numberSign = value.indexOf('#')\n  const slash = value.indexOf('/')\n\n  if (\n    // If there is no protocol, it’s relative.\n    colon < 0 ||\n    // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    (slash > -1 && colon > slash) ||\n    (questionMark > -1 && colon > questionMark) ||\n    (numberSign > -1 && colon > numberSign) ||\n    // It is a protocol, it should be allowed.\n    protocol.test(value.slice(0, colon))\n  ) {\n    return value\n  }\n\n  return ''\n}\n\n/**\n * Normalize a URL.\n *\n * Encode unsafe characters with percent-encoding, skipping already encoded\n * sequences.\n *\n * @param {string} value\n *   URI to normalize.\n * @returns {string}\n *   Normalized URI.\n */\nfunction normalizeUri(value) {\n  /** @type {Array<string>} */\n  const result = []\n  let index = -1\n  let start = 0\n  let skip = 0\n\n  while (++index < value.length) {\n    const code = value.charCodeAt(index)\n    /** @type {string} */\n    let replace = ''\n\n    // A correct percent encoded value.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.percentSign &&\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.asciiAlphanumeric)(value.charCodeAt(index + 1)) &&\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.asciiAlphanumeric)(value.charCodeAt(index + 2))\n    ) {\n      skip = 2\n    }\n    // ASCII.\n    else if (code < 128) {\n      if (!/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(code))) {\n        replace = String.fromCharCode(code)\n      }\n    }\n    // Astral.\n    else if (code > 55_295 && code < 57_344) {\n      const next = value.charCodeAt(index + 1)\n\n      // A correct surrogate pair.\n      if (code < 56_320 && next > 56_319 && next < 57_344) {\n        replace = String.fromCharCode(code, next)\n        skip = 1\n      }\n      // Lone surrogate.\n      else {\n        replace = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.values.replacementCharacter\n      }\n    }\n    // Unicode.\n    else {\n      replace = String.fromCharCode(code)\n    }\n\n    if (replace) {\n      result.push(value.slice(start, index), encodeURIComponent(replace))\n      start = index + skip + 1\n      replace = ''\n    }\n\n    if (skip) {\n      index += skip\n      skip = 0\n    }\n  }\n\n  return result.join('') + value.slice(start)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/micromark-extension-gfm-autolink-literal/node_modules/micromark-util-sanitize-uri/dev/index.js\n");

/***/ })

};
;