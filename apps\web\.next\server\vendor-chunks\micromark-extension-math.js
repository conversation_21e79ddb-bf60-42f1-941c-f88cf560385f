"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-math";
exports.ids = ["vendor-chunks/micromark-extension-math"];
exports.modules = {

/***/ "(ssr)/../../node_modules/micromark-extension-math/dev/lib/math-flow.js":
/*!************************************************************************!*\
  !*** ../../node_modules/micromark-extension-math/dev/lib/math-flow.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mathFlow: () => (/* binding */ mathFlow)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(ssr)/../../node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/../../node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/../../node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/../../node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/../../node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/../../node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {Construct, State, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\n\n\n\n\n\n/** @type {Construct} */\nconst mathFlow = {\n  tokenize: tokenizeMathFenced,\n  concrete: true,\n  name: 'mathFlow'\n}\n\n/** @type {Construct} */\nconst nonLazyContinuation = {\n  tokenize: tokenizeNonLazyContinuation,\n  partial: true\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeMathFenced(effects, ok, nok) {\n  const self = this\n  const tail = self.events[self.events.length - 1]\n  const initialSize =\n    tail && tail[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix\n      ? tail[2].sliceSerialize(tail[1], true).length\n      : 0\n  let sizeOpen = 0\n\n  return start\n\n  /**\n   * Start of math.\n   *\n   * ```markdown\n   * > | $$\n   *     ^\n   *   | \\frac{1}{2}\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dollarSign, 'expected `$`')\n    effects.enter('mathFlow')\n    effects.enter('mathFlowFence')\n    effects.enter('mathFlowFenceSequence')\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening fence sequence.\n   *\n   * ```markdown\n   * > | $$\n   *      ^\n   *   | \\frac{1}{2}\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dollarSign) {\n      effects.consume(code)\n      sizeOpen++\n      return sequenceOpen\n    }\n\n    if (sizeOpen < 2) {\n      return nok(code)\n    }\n\n    effects.exit('mathFlowFenceSequence')\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, metaBefore, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code)\n  }\n\n  /**\n   * In opening fence, before meta.\n   *\n   * ```markdown\n   * > | $$asciimath\n   *       ^\n   *   | x < y\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n\n  function metaBefore(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      return metaAfter(code)\n    }\n\n    effects.enter('mathFlowFenceMeta')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.chunkString, {contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeString})\n    return meta(code)\n  }\n\n  /**\n   * In meta.\n   *\n   * ```markdown\n   * > | $$asciimath\n   *        ^\n   *   | x < y\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function meta(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.chunkString)\n      effects.exit('mathFlowFenceMeta')\n      return metaAfter(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dollarSign) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return meta\n  }\n\n  /**\n   * After meta.\n   *\n   * ```markdown\n   * > | $$\n   *       ^\n   *   | \\frac{1}{2}\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function metaAfter(code) {\n    // Guaranteed to be eol/eof.\n    effects.exit('mathFlowFence')\n\n    if (self.interrupt) {\n      return ok(code)\n    }\n\n    return effects.attempt(\n      nonLazyContinuation,\n      beforeNonLazyContinuation,\n      after\n    )(code)\n  }\n\n  /**\n   * After eol/eof in math, at a non-lazy closing fence or content.\n   *\n   * ```markdown\n   *   | $$\n   * > | \\frac{1}{2}\n   *     ^\n   * > | $$\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeNonLazyContinuation(code) {\n    return effects.attempt(\n      {tokenize: tokenizeClosingFence, partial: true},\n      after,\n      contentStart\n    )(code)\n  }\n\n  /**\n   * Before math content, definitely not before a closing fence.\n   *\n   * ```markdown\n   *   | $$\n   * > | \\frac{1}{2}\n   *     ^\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function contentStart(code) {\n    return (\n      initialSize\n        ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(\n            effects,\n            beforeContentChunk,\n            micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix,\n            initialSize + 1\n          )\n        : beforeContentChunk\n    )(code)\n  }\n\n  /**\n   * Before math content, after optional prefix.\n   *\n   * ```markdown\n   *   | $$\n   * > | \\frac{1}{2}\n   *     ^\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeContentChunk(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof) {\n      return after(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      return effects.attempt(\n        nonLazyContinuation,\n        beforeNonLazyContinuation,\n        after\n      )(code)\n    }\n\n    effects.enter('mathFlowValue')\n    return contentChunk(code)\n  }\n\n  /**\n   * In math content.\n   *\n   * ```markdown\n   *   | $$\n   * > | \\frac{1}{2}\n   *      ^\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function contentChunk(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      effects.exit('mathFlowValue')\n      return beforeContentChunk(code)\n    }\n\n    effects.consume(code)\n    return contentChunk\n  }\n\n  /**\n   * After math (ha!).\n   *\n   * ```markdown\n   *   | $$\n   *   | \\frac{1}{2}\n   * > | $$\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    effects.exit('mathFlow')\n    return ok(code)\n  }\n\n  /** @type {Tokenizer} */\n  function tokenizeClosingFence(effects, ok, nok) {\n    let size = 0\n\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.parser.constructs.disable.null, 'expected `disable.null`')\n    /**\n     * Before closing fence, at optional whitespace.\n     *\n     * ```markdown\n     *   | $$\n     *   | \\frac{1}{2}\n     * > | $$\n     *     ^\n     * ```\n     */\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(\n      effects,\n      beforeSequenceClose,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix,\n      self.parser.constructs.disable.null.includes('codeIndented')\n        ? undefined\n        : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize\n    )\n\n    /**\n     * In closing fence, after optional whitespace, at sequence.\n     *\n     * ```markdown\n     *   | $$\n     *   | \\frac{1}{2}\n     * > | $$\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function beforeSequenceClose(code) {\n      effects.enter('mathFlowFence')\n      effects.enter('mathFlowFenceSequence')\n      return sequenceClose(code)\n    }\n\n    /**\n     * In closing fence sequence.\n     *\n     * ```markdown\n     *   | $$\n     *   | \\frac{1}{2}\n     * > | $$\n     *      ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceClose(code) {\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dollarSign) {\n        size++\n        effects.consume(code)\n        return sequenceClose\n      }\n\n      if (size < sizeOpen) {\n        return nok(code)\n      }\n\n      effects.exit('mathFlowFenceSequence')\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, afterSequenceClose, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code)\n    }\n\n    /**\n     * After closing fence sequence, after optional whitespace.\n     *\n     * ```markdown\n     *   | $$\n     *   | \\frac{1}{2}\n     * > | $$\n     *       ^\n     * ```\n     *\n     * @type {State}\n     */\n    function afterSequenceClose(code) {\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n        effects.exit('mathFlowFence')\n        return ok(code)\n      }\n\n      return nok(code)\n    }\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeNonLazyContinuation(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    if (code === null) {\n      return ok(code)\n    }\n\n    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code), 'expected eol')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)\n    return lineStart\n  }\n\n  /** @type {State} */\n  function lineStart(code) {\n    return self.parser.lazy[self.now().line] ? nok(code) : ok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/micromark-extension-math/dev/lib/math-flow.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/micromark-extension-math/dev/lib/math-text.js":
/*!************************************************************************!*\
  !*** ../../node_modules/micromark-extension-math/dev/lib/math-text.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mathText: () => (/* binding */ mathText)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/../../node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/../../node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/../../node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/../../node_modules/micromark-util-symbol/lib/types.js\");\n/**\n * @import {Options} from 'micromark-extension-math'\n * @import {Construct, Previous, Resolver, State, Token, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\n// To do: next major: clean spaces in HTML compiler.\n// This has to be coordinated together with `mdast-util-math`.\n\n\n\n\n\n/**\n * @param {Options | null | undefined} [options={}]\n *   Configuration (default: `{}`).\n * @returns {Construct}\n *   Construct.\n */\nfunction mathText(options) {\n  const options_ = options || {}\n  let single = options_.singleDollarTextMath\n\n  if (single === null || single === undefined) {\n    single = true\n  }\n\n  return {\n    tokenize: tokenizeMathText,\n    resolve: resolveMathText,\n    previous,\n    name: 'mathText'\n  }\n\n  /**\n   * @this {TokenizeContext}\n   * @type {Tokenizer}\n   */\n  function tokenizeMathText(effects, ok, nok) {\n    const self = this\n    let sizeOpen = 0\n    /** @type {number} */\n    let size\n    /** @type {Token} */\n    let token\n\n    return start\n\n    /**\n     * Start of math (text).\n     *\n     * ```markdown\n     * > | $a$\n     *     ^\n     * > | \\$a$\n     *      ^\n     * ```\n     *\n     * @type {State}\n     */\n    function start(code) {\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign, 'expected `$`')\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(previous.call(self, self.previous), 'expected correct previous')\n      effects.enter('mathText')\n      effects.enter('mathTextSequence')\n      return sequenceOpen(code)\n    }\n\n    /**\n     * In opening sequence.\n     *\n     * ```markdown\n     * > | $a$\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n\n    function sequenceOpen(code) {\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign) {\n        effects.consume(code)\n        sizeOpen++\n        return sequenceOpen\n      }\n\n      // Not enough markers in the sequence.\n      if (sizeOpen < 2 && !single) {\n        return nok(code)\n      }\n\n      effects.exit('mathTextSequence')\n      return between(code)\n    }\n\n    /**\n     * Between something and something else.\n     *\n     * ```markdown\n     * > | $a$\n     *      ^^\n     * ```\n     *\n     * @type {State}\n     */\n    function between(code) {\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n        return nok(code)\n      }\n\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign) {\n        token = effects.enter('mathTextSequence')\n        size = 0\n        return sequenceClose(code)\n      }\n\n      // Tabs don’t work, and virtual spaces don’t make sense.\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.space) {\n        effects.enter('space')\n        effects.consume(code)\n        effects.exit('space')\n        return between\n      }\n\n      if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding)\n        effects.consume(code)\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding)\n        return between\n      }\n\n      // Data.\n      effects.enter('mathTextData')\n      return data(code)\n    }\n\n    /**\n     * In data.\n     *\n     * ```markdown\n     * > | $a$\n     *      ^\n     * ```\n     *\n     * @type {State}\n     */\n    function data(code) {\n      if (\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof ||\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.space ||\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign ||\n        (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)\n      ) {\n        effects.exit('mathTextData')\n        return between(code)\n      }\n\n      effects.consume(code)\n      return data\n    }\n\n    /**\n     * In closing sequence.\n     *\n     * ```markdown\n     * > | `a`\n     *       ^\n     * ```\n     *\n     * @type {State}\n     */\n\n    function sequenceClose(code) {\n      // More.\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign) {\n        effects.consume(code)\n        size++\n        return sequenceClose\n      }\n\n      // Done!\n      if (size === sizeOpen) {\n        effects.exit('mathTextSequence')\n        effects.exit('mathText')\n        return ok(code)\n      }\n\n      // More or less accents: mark as data.\n      token.type = 'mathTextData'\n      return data(code)\n    }\n  }\n}\n\n/** @type {Resolver} */\nfunction resolveMathText(events) {\n  let tailExitIndex = events.length - 4\n  let headEnterIndex = 3\n  /** @type {number} */\n  let index\n  /** @type {number | undefined} */\n  let enter\n\n  // If we start and end with an EOL or a space.\n  if (\n    (events[headEnterIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding ||\n      events[headEnterIndex][1].type === 'space') &&\n    (events[tailExitIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding ||\n      events[tailExitIndex][1].type === 'space')\n  ) {\n    index = headEnterIndex\n\n    // And we have data.\n    while (++index < tailExitIndex) {\n      if (events[index][1].type === 'mathTextData') {\n        // Then we have padding.\n        events[tailExitIndex][1].type = 'mathTextPadding'\n        events[headEnterIndex][1].type = 'mathTextPadding'\n        headEnterIndex += 2\n        tailExitIndex -= 2\n        break\n      }\n    }\n  }\n\n  // Merge adjacent spaces and data.\n  index = headEnterIndex - 1\n  tailExitIndex++\n\n  while (++index <= tailExitIndex) {\n    if (enter === undefined) {\n      if (\n        index !== tailExitIndex &&\n        events[index][1].type !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding\n      ) {\n        enter = index\n      }\n    } else if (\n      index === tailExitIndex ||\n      events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding\n    ) {\n      events[enter][1].type = 'mathTextData'\n\n      if (index !== enter + 2) {\n        events[enter][1].end = events[index - 1][1].end\n        events.splice(enter + 2, index - enter - 2)\n        tailExitIndex -= index - enter - 2\n        index = enter + 2\n      }\n\n      enter = undefined\n    }\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Previous}\n */\nfunction previous(code) {\n  // If there is a previous code, there will always be a tail.\n  return (\n    code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign ||\n    this.events[this.events.length - 1][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.characterEscape\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/micromark-extension-math/dev/lib/math-text.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/micromark-extension-math/dev/lib/syntax.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/micromark-extension-math/dev/lib/syntax.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   math: () => (/* binding */ math)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/../../node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var _math_flow_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./math-flow.js */ \"(ssr)/../../node_modules/micromark-extension-math/dev/lib/math-flow.js\");\n/* harmony import */ var _math_text_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./math-text.js */ \"(ssr)/../../node_modules/micromark-extension-math/dev/lib/math-text.js\");\n/**\n * @import {Options} from 'micromark-extension-math'\n * @import {Extension} from 'micromark-util-types'\n */\n\n\n\n\n\n/**\n * Create an extension for `micromark` to enable math syntax.\n *\n * @param {Options | null | undefined} [options={}]\n *   Configuration (default: `{}`).\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions`, to\n *   enable math syntax.\n */\nfunction math(options) {\n  return {\n    flow: {[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dollarSign]: _math_flow_js__WEBPACK_IMPORTED_MODULE_1__.mathFlow},\n    text: {[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dollarSign]: (0,_math_text_js__WEBPACK_IMPORTED_MODULE_2__.mathText)(options)}\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay1leHRlbnNpb24tbWF0aC9kZXYvbGliL3N5bnRheC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7QUFDQSxZQUFZLFNBQVM7QUFDckIsWUFBWSxXQUFXO0FBQ3ZCOztBQUUyQztBQUNKO0FBQ0E7O0FBRXZDO0FBQ0E7QUFDQTtBQUNBLFdBQVcsNEJBQTRCLFdBQVc7QUFDbEQsZ0NBQWdDO0FBQ2hDLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0EsV0FBVyxDQUFDLHdEQUFLLGNBQWMsbURBQVEsQ0FBQztBQUN4QyxXQUFXLENBQUMsd0RBQUssY0FBYyx1REFBUTtBQUN2QztBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9taWNyb21hcmstZXh0ZW5zaW9uLW1hdGgvZGV2L2xpYi9zeW50YXguanM/NGY4YiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge09wdGlvbnN9IGZyb20gJ21pY3JvbWFyay1leHRlbnNpb24tbWF0aCdcbiAqIEBpbXBvcnQge0V4dGVuc2lvbn0gZnJvbSAnbWljcm9tYXJrLXV0aWwtdHlwZXMnXG4gKi9cblxuaW1wb3J0IHtjb2Rlc30gZnJvbSAnbWljcm9tYXJrLXV0aWwtc3ltYm9sJ1xuaW1wb3J0IHttYXRoRmxvd30gZnJvbSAnLi9tYXRoLWZsb3cuanMnXG5pbXBvcnQge21hdGhUZXh0fSBmcm9tICcuL21hdGgtdGV4dC5qcydcblxuLyoqXG4gKiBDcmVhdGUgYW4gZXh0ZW5zaW9uIGZvciBgbWljcm9tYXJrYCB0byBlbmFibGUgbWF0aCBzeW50YXguXG4gKlxuICogQHBhcmFtIHtPcHRpb25zIHwgbnVsbCB8IHVuZGVmaW5lZH0gW29wdGlvbnM9e31dXG4gKiAgIENvbmZpZ3VyYXRpb24gKGRlZmF1bHQ6IGB7fWApLlxuICogQHJldHVybnMge0V4dGVuc2lvbn1cbiAqICAgRXh0ZW5zaW9uIGZvciBgbWljcm9tYXJrYCB0aGF0IGNhbiBiZSBwYXNzZWQgaW4gYGV4dGVuc2lvbnNgLCB0b1xuICogICBlbmFibGUgbWF0aCBzeW50YXguXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBtYXRoKG9wdGlvbnMpIHtcbiAgcmV0dXJuIHtcbiAgICBmbG93OiB7W2NvZGVzLmRvbGxhclNpZ25dOiBtYXRoRmxvd30sXG4gICAgdGV4dDoge1tjb2Rlcy5kb2xsYXJTaWduXTogbWF0aFRleHQob3B0aW9ucyl9XG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/micromark-extension-math/dev/lib/syntax.js\n");

/***/ })

};
;