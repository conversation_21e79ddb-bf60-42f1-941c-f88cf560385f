"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/property-information";
exports.ids = ["vendor-chunks/property-information"];
exports.modules = {

/***/ "(ssr)/../../node_modules/property-information/index.js":
/*!********************************************************!*\
  !*** ../../node_modules/property-information/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   find: () => (/* reexport safe */ _lib_find_js__WEBPACK_IMPORTED_MODULE_0__.find),\n/* harmony export */   hastToReact: () => (/* reexport safe */ _lib_hast_to_react_js__WEBPACK_IMPORTED_MODULE_1__.hastToReact),\n/* harmony export */   html: () => (/* binding */ html),\n/* harmony export */   normalize: () => (/* reexport safe */ _lib_normalize_js__WEBPACK_IMPORTED_MODULE_2__.normalize),\n/* harmony export */   svg: () => (/* binding */ svg)\n/* harmony export */ });\n/* harmony import */ var _lib_util_merge_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/util/merge.js */ \"(ssr)/../../node_modules/property-information/lib/util/merge.js\");\n/* harmony import */ var _lib_xlink_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/xlink.js */ \"(ssr)/../../node_modules/property-information/lib/xlink.js\");\n/* harmony import */ var _lib_xml_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/xml.js */ \"(ssr)/../../node_modules/property-information/lib/xml.js\");\n/* harmony import */ var _lib_xmlns_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/xmlns.js */ \"(ssr)/../../node_modules/property-information/lib/xmlns.js\");\n/* harmony import */ var _lib_aria_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/aria.js */ \"(ssr)/../../node_modules/property-information/lib/aria.js\");\n/* harmony import */ var _lib_html_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./lib/html.js */ \"(ssr)/../../node_modules/property-information/lib/html.js\");\n/* harmony import */ var _lib_svg_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./lib/svg.js */ \"(ssr)/../../node_modules/property-information/lib/svg.js\");\n/* harmony import */ var _lib_find_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/find.js */ \"(ssr)/../../node_modules/property-information/lib/find.js\");\n/* harmony import */ var _lib_hast_to_react_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/hast-to-react.js */ \"(ssr)/../../node_modules/property-information/lib/hast-to-react.js\");\n/* harmony import */ var _lib_normalize_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/normalize.js */ \"(ssr)/../../node_modules/property-information/lib/normalize.js\");\n/**\n * @typedef {import('./lib/util/info.js').Info} Info\n * @typedef {import('./lib/util/schema.js').Schema} Schema\n */\n\n\n\n\n\n\n\n\n\n\n\n\nconst html = (0,_lib_util_merge_js__WEBPACK_IMPORTED_MODULE_3__.merge)([_lib_xml_js__WEBPACK_IMPORTED_MODULE_4__.xml, _lib_xlink_js__WEBPACK_IMPORTED_MODULE_5__.xlink, _lib_xmlns_js__WEBPACK_IMPORTED_MODULE_6__.xmlns, _lib_aria_js__WEBPACK_IMPORTED_MODULE_7__.aria, _lib_html_js__WEBPACK_IMPORTED_MODULE_8__.html], 'html')\nconst svg = (0,_lib_util_merge_js__WEBPACK_IMPORTED_MODULE_3__.merge)([_lib_xml_js__WEBPACK_IMPORTED_MODULE_4__.xml, _lib_xlink_js__WEBPACK_IMPORTED_MODULE_5__.xlink, _lib_xmlns_js__WEBPACK_IMPORTED_MODULE_6__.xmlns, _lib_aria_js__WEBPACK_IMPORTED_MODULE_7__.aria, _lib_svg_js__WEBPACK_IMPORTED_MODULE_9__.svg], 'svg')\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0EsYUFBYSxtQ0FBbUM7QUFDaEQsYUFBYSx1Q0FBdUM7QUFDcEQ7O0FBRXlDO0FBQ0w7QUFDSjtBQUNJO0FBQ0Y7QUFDWTtBQUNIOztBQUVUO0FBQ2dCO0FBQ047QUFDckMsYUFBYSx5REFBSyxFQUFFLDRDQUFHLEVBQUUsZ0RBQUssRUFBRSxnREFBSyxFQUFFLDhDQUFJLEVBQUUsOENBQVE7QUFDckQsWUFBWSx5REFBSyxFQUFFLDRDQUFHLEVBQUUsZ0RBQUssRUFBRSxnREFBSyxFQUFFLDhDQUFJLEVBQUUsNENBQU8iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2luZGV4LmpzP2Q5ODAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2xpYi91dGlsL2luZm8uanMnKS5JbmZvfSBJbmZvXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2xpYi91dGlsL3NjaGVtYS5qcycpLlNjaGVtYX0gU2NoZW1hXG4gKi9cblxuaW1wb3J0IHttZXJnZX0gZnJvbSAnLi9saWIvdXRpbC9tZXJnZS5qcydcbmltcG9ydCB7eGxpbmt9IGZyb20gJy4vbGliL3hsaW5rLmpzJ1xuaW1wb3J0IHt4bWx9IGZyb20gJy4vbGliL3htbC5qcydcbmltcG9ydCB7eG1sbnN9IGZyb20gJy4vbGliL3htbG5zLmpzJ1xuaW1wb3J0IHthcmlhfSBmcm9tICcuL2xpYi9hcmlhLmpzJ1xuaW1wb3J0IHtodG1sIGFzIGh0bWxCYXNlfSBmcm9tICcuL2xpYi9odG1sLmpzJ1xuaW1wb3J0IHtzdmcgYXMgc3ZnQmFzZX0gZnJvbSAnLi9saWIvc3ZnLmpzJ1xuXG5leHBvcnQge2ZpbmR9IGZyb20gJy4vbGliL2ZpbmQuanMnXG5leHBvcnQge2hhc3RUb1JlYWN0fSBmcm9tICcuL2xpYi9oYXN0LXRvLXJlYWN0LmpzJ1xuZXhwb3J0IHtub3JtYWxpemV9IGZyb20gJy4vbGliL25vcm1hbGl6ZS5qcydcbmV4cG9ydCBjb25zdCBodG1sID0gbWVyZ2UoW3htbCwgeGxpbmssIHhtbG5zLCBhcmlhLCBodG1sQmFzZV0sICdodG1sJylcbmV4cG9ydCBjb25zdCBzdmcgPSBtZXJnZShbeG1sLCB4bGluaywgeG1sbnMsIGFyaWEsIHN2Z0Jhc2VdLCAnc3ZnJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/property-information/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/property-information/lib/aria.js":
/*!***********************************************************!*\
  !*** ../../node_modules/property-information/lib/aria.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aria: () => (/* binding */ aria)\n/* harmony export */ });\n/* harmony import */ var _util_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/types.js */ \"(ssr)/../../node_modules/property-information/lib/util/types.js\");\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/../../node_modules/property-information/lib/util/create.js\");\n\n\n\nconst aria = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  transform(_, prop) {\n    return prop === 'role' ? prop : 'aria-' + prop.slice(4).toLowerCase()\n  },\n  properties: {\n    ariaActiveDescendant: null,\n    ariaAtomic: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaAutoComplete: null,\n    ariaBusy: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaChecked: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaColCount: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaColIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaColSpan: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaControls: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaCurrent: null,\n    ariaDescribedBy: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaDetails: null,\n    ariaDisabled: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaDropEffect: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaErrorMessage: null,\n    ariaExpanded: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaFlowTo: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaGrabbed: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaHasPopup: null,\n    ariaHidden: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaInvalid: null,\n    ariaKeyShortcuts: null,\n    ariaLabel: null,\n    ariaLabelledBy: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaLevel: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaLive: null,\n    ariaModal: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaMultiLine: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaMultiSelectable: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaOrientation: null,\n    ariaOwns: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaPlaceholder: null,\n    ariaPosInSet: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaPressed: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaReadOnly: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaRelevant: null,\n    ariaRequired: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaRoleDescription: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaRowCount: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaRowIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaRowSpan: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaSelected: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaSetSize: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaSort: null,\n    ariaValueMax: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaValueMin: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaValueNow: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaValueText: null,\n    role: null\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/property-information/lib/aria.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/property-information/lib/find.js":
/*!***********************************************************!*\
  !*** ../../node_modules/property-information/lib/find.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   find: () => (/* binding */ find)\n/* harmony export */ });\n/* harmony import */ var _normalize_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./normalize.js */ \"(ssr)/../../node_modules/property-information/lib/normalize.js\");\n/* harmony import */ var _util_defined_info_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/defined-info.js */ \"(ssr)/../../node_modules/property-information/lib/util/defined-info.js\");\n/* harmony import */ var _util_info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/info.js */ \"(ssr)/../../node_modules/property-information/lib/util/info.js\");\n/**\n * @typedef {import('./util/schema.js').Schema} Schema\n */\n\n\n\n\n\nconst valid = /^data[-\\w.:]+$/i\nconst dash = /-[a-z]/g\nconst cap = /[A-Z]/g\n\n/**\n * @param {Schema} schema\n * @param {string} value\n * @returns {Info}\n */\nfunction find(schema, value) {\n  const normal = (0,_normalize_js__WEBPACK_IMPORTED_MODULE_0__.normalize)(value)\n  let prop = value\n  let Type = _util_info_js__WEBPACK_IMPORTED_MODULE_1__.Info\n\n  if (normal in schema.normal) {\n    return schema.property[schema.normal[normal]]\n  }\n\n  if (normal.length > 4 && normal.slice(0, 4) === 'data' && valid.test(value)) {\n    // Attribute or property.\n    if (value.charAt(4) === '-') {\n      // Turn it into a property.\n      const rest = value.slice(5).replace(dash, camelcase)\n      prop = 'data' + rest.charAt(0).toUpperCase() + rest.slice(1)\n    } else {\n      // Turn it into an attribute.\n      const rest = value.slice(4)\n\n      if (!dash.test(rest)) {\n        let dashes = rest.replace(cap, kebab)\n\n        if (dashes.charAt(0) !== '-') {\n          dashes = '-' + dashes\n        }\n\n        value = 'data' + dashes\n      }\n    }\n\n    Type = _util_defined_info_js__WEBPACK_IMPORTED_MODULE_2__.DefinedInfo\n  }\n\n  return new Type(prop, value)\n}\n\n/**\n * @param {string} $0\n * @returns {string}\n */\nfunction kebab($0) {\n  return '-' + $0.toLowerCase()\n}\n\n/**\n * @param {string} $0\n * @returns {string}\n */\nfunction camelcase($0) {\n  return $0.charAt(1).toUpperCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/property-information/lib/find.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/property-information/lib/hast-to-react.js":
/*!********************************************************************!*\
  !*** ../../node_modules/property-information/lib/hast-to-react.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hastToReact: () => (/* binding */ hastToReact)\n/* harmony export */ });\n/**\n * `hast` is close to `React`, but differs in a couple of cases.\n *\n * To get a React property from a hast property, check if it is in\n * `hastToReact`, if it is, then use the corresponding value,\n * otherwise, use the hast property.\n *\n * @type {Record<string, string>}\n */\nconst hastToReact = {\n  classId: 'classID',\n  dataType: 'datatype',\n  itemId: 'itemID',\n  strokeDashArray: 'strokeDasharray',\n  strokeDashOffset: 'strokeDashoffset',\n  strokeLineCap: 'strokeLinecap',\n  strokeLineJoin: 'strokeLinejoin',\n  strokeMiterLimit: 'strokeMiterlimit',\n  typeOf: 'typeof',\n  xLinkActuate: 'xlinkActuate',\n  xLinkArcRole: 'xlinkArcrole',\n  xLinkHref: 'xlinkHref',\n  xLinkRole: 'xlinkRole',\n  xLinkShow: 'xlinkShow',\n  xLinkTitle: 'xlinkTitle',\n  xLinkType: 'xlinkType',\n  xmlnsXLink: 'xmlnsXlink'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi9oYXN0LXRvLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIvaGFzdC10by1yZWFjdC5qcz8yYTQwIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogYGhhc3RgIGlzIGNsb3NlIHRvIGBSZWFjdGAsIGJ1dCBkaWZmZXJzIGluIGEgY291cGxlIG9mIGNhc2VzLlxuICpcbiAqIFRvIGdldCBhIFJlYWN0IHByb3BlcnR5IGZyb20gYSBoYXN0IHByb3BlcnR5LCBjaGVjayBpZiBpdCBpcyBpblxuICogYGhhc3RUb1JlYWN0YCwgaWYgaXQgaXMsIHRoZW4gdXNlIHRoZSBjb3JyZXNwb25kaW5nIHZhbHVlLFxuICogb3RoZXJ3aXNlLCB1c2UgdGhlIGhhc3QgcHJvcGVydHkuXG4gKlxuICogQHR5cGUge1JlY29yZDxzdHJpbmcsIHN0cmluZz59XG4gKi9cbmV4cG9ydCBjb25zdCBoYXN0VG9SZWFjdCA9IHtcbiAgY2xhc3NJZDogJ2NsYXNzSUQnLFxuICBkYXRhVHlwZTogJ2RhdGF0eXBlJyxcbiAgaXRlbUlkOiAnaXRlbUlEJyxcbiAgc3Ryb2tlRGFzaEFycmF5OiAnc3Ryb2tlRGFzaGFycmF5JyxcbiAgc3Ryb2tlRGFzaE9mZnNldDogJ3N0cm9rZURhc2hvZmZzZXQnLFxuICBzdHJva2VMaW5lQ2FwOiAnc3Ryb2tlTGluZWNhcCcsXG4gIHN0cm9rZUxpbmVKb2luOiAnc3Ryb2tlTGluZWpvaW4nLFxuICBzdHJva2VNaXRlckxpbWl0OiAnc3Ryb2tlTWl0ZXJsaW1pdCcsXG4gIHR5cGVPZjogJ3R5cGVvZicsXG4gIHhMaW5rQWN0dWF0ZTogJ3hsaW5rQWN0dWF0ZScsXG4gIHhMaW5rQXJjUm9sZTogJ3hsaW5rQXJjcm9sZScsXG4gIHhMaW5rSHJlZjogJ3hsaW5rSHJlZicsXG4gIHhMaW5rUm9sZTogJ3hsaW5rUm9sZScsXG4gIHhMaW5rU2hvdzogJ3hsaW5rU2hvdycsXG4gIHhMaW5rVGl0bGU6ICd4bGlua1RpdGxlJyxcbiAgeExpbmtUeXBlOiAneGxpbmtUeXBlJyxcbiAgeG1sbnNYTGluazogJ3htbG5zWGxpbmsnXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/property-information/lib/hast-to-react.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/property-information/lib/html.js":
/*!***********************************************************!*\
  !*** ../../node_modules/property-information/lib/html.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   html: () => (/* binding */ html)\n/* harmony export */ });\n/* harmony import */ var _util_types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/types.js */ \"(ssr)/../../node_modules/property-information/lib/util/types.js\");\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/../../node_modules/property-information/lib/util/create.js\");\n/* harmony import */ var _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/case-insensitive-transform.js */ \"(ssr)/../../node_modules/property-information/lib/util/case-insensitive-transform.js\");\n\n\n\n\nconst html = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  space: 'html',\n  attributes: {\n    acceptcharset: 'accept-charset',\n    classname: 'class',\n    htmlfor: 'for',\n    httpequiv: 'http-equiv'\n  },\n  transform: _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__.caseInsensitiveTransform,\n  mustUseProperty: ['checked', 'multiple', 'muted', 'selected'],\n  properties: {\n    // Standard Properties.\n    abbr: null,\n    accept: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaSeparated,\n    acceptCharset: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    accessKey: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    action: null,\n    allow: null,\n    allowFullScreen: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    allowPaymentRequest: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    allowUserMedia: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    alt: null,\n    as: null,\n    async: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    autoCapitalize: null,\n    autoComplete: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    autoFocus: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    autoPlay: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    blocking: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    capture: null,\n    charSet: null,\n    checked: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    cite: null,\n    className: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    cols: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    colSpan: null,\n    content: null,\n    contentEditable: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.booleanish,\n    controls: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    controlsList: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    coords: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number | _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaSeparated,\n    crossOrigin: null,\n    data: null,\n    dateTime: null,\n    decoding: null,\n    default: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    defer: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    dir: null,\n    dirName: null,\n    disabled: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    download: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.overloadedBoolean,\n    draggable: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.booleanish,\n    encType: null,\n    enterKeyHint: null,\n    fetchPriority: null,\n    form: null,\n    formAction: null,\n    formEncType: null,\n    formMethod: null,\n    formNoValidate: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    formTarget: null,\n    headers: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    height: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    hidden: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    high: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    href: null,\n    hrefLang: null,\n    htmlFor: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    httpEquiv: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    id: null,\n    imageSizes: null,\n    imageSrcSet: null,\n    inert: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    inputMode: null,\n    integrity: null,\n    is: null,\n    isMap: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    itemId: null,\n    itemProp: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    itemRef: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    itemScope: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    itemType: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    kind: null,\n    label: null,\n    lang: null,\n    language: null,\n    list: null,\n    loading: null,\n    loop: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    low: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    manifest: null,\n    max: null,\n    maxLength: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    media: null,\n    method: null,\n    min: null,\n    minLength: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    multiple: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    muted: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    name: null,\n    nonce: null,\n    noModule: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    noValidate: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    onAbort: null,\n    onAfterPrint: null,\n    onAuxClick: null,\n    onBeforeMatch: null,\n    onBeforePrint: null,\n    onBeforeToggle: null,\n    onBeforeUnload: null,\n    onBlur: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onContextLost: null,\n    onContextMenu: null,\n    onContextRestored: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFormData: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLanguageChange: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadEnd: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMessageError: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRejectionHandled: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onScrollEnd: null,\n    onSecurityPolicyViolation: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onSlotChange: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnhandledRejection: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onWheel: null,\n    open: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    optimum: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    pattern: null,\n    ping: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    placeholder: null,\n    playsInline: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    popover: null,\n    popoverTarget: null,\n    popoverTargetAction: null,\n    poster: null,\n    preload: null,\n    readOnly: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    referrerPolicy: null,\n    rel: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    required: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    reversed: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    rows: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    rowSpan: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    sandbox: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    scope: null,\n    scoped: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    seamless: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    selected: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    shadowRootClonable: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    shadowRootDelegatesFocus: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    shadowRootMode: null,\n    shape: null,\n    size: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    sizes: null,\n    slot: null,\n    span: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    spellCheck: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.booleanish,\n    src: null,\n    srcDoc: null,\n    srcLang: null,\n    srcSet: null,\n    start: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    step: null,\n    style: null,\n    tabIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    target: null,\n    title: null,\n    translate: null,\n    type: null,\n    typeMustMatch: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    useMap: null,\n    value: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.booleanish,\n    width: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    wrap: null,\n    writingSuggestions: null,\n\n    // Legacy.\n    // See: https://html.spec.whatwg.org/#other-elements,-attributes-and-apis\n    align: null, // Several. Use CSS `text-align` instead,\n    aLink: null, // `<body>`. Use CSS `a:active {color}` instead\n    archive: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated, // `<object>`. List of URIs to archives\n    axis: null, // `<td>` and `<th>`. Use `scope` on `<th>`\n    background: null, // `<body>`. Use CSS `background-image` instead\n    bgColor: null, // `<body>` and table elements. Use CSS `background-color` instead\n    border: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<table>`. Use CSS `border-width` instead,\n    borderColor: null, // `<table>`. Use CSS `border-color` instead,\n    bottomMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<body>`\n    cellPadding: null, // `<table>`\n    cellSpacing: null, // `<table>`\n    char: null, // Several table elements. When `align=char`, sets the character to align on\n    charOff: null, // Several table elements. When `char`, offsets the alignment\n    classId: null, // `<object>`\n    clear: null, // `<br>`. Use CSS `clear` instead\n    code: null, // `<object>`\n    codeBase: null, // `<object>`\n    codeType: null, // `<object>`\n    color: null, // `<font>` and `<hr>`. Use CSS instead\n    compact: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean, // Lists. Use CSS to reduce space between items instead\n    declare: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean, // `<object>`\n    event: null, // `<script>`\n    face: null, // `<font>`. Use CSS instead\n    frame: null, // `<table>`\n    frameBorder: null, // `<iframe>`. Use CSS `border` instead\n    hSpace: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<img>` and `<object>`\n    leftMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<body>`\n    link: null, // `<body>`. Use CSS `a:link {color: *}` instead\n    longDesc: null, // `<frame>`, `<iframe>`, and `<img>`. Use an `<a>`\n    lowSrc: null, // `<img>`. Use a `<picture>`\n    marginHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<body>`\n    marginWidth: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<body>`\n    noResize: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean, // `<frame>`\n    noHref: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean, // `<area>`. Use no href instead of an explicit `nohref`\n    noShade: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean, // `<hr>`. Use background-color and height instead of borders\n    noWrap: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean, // `<td>` and `<th>`\n    object: null, // `<applet>`\n    profile: null, // `<head>`\n    prompt: null, // `<isindex>`\n    rev: null, // `<link>`\n    rightMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<body>`\n    rules: null, // `<table>`\n    scheme: null, // `<meta>`\n    scrolling: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.booleanish, // `<frame>`. Use overflow in the child context\n    standby: null, // `<object>`\n    summary: null, // `<table>`\n    text: null, // `<body>`. Use CSS `color` instead\n    topMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<body>`\n    valueType: null, // `<param>`\n    version: null, // `<html>`. Use a doctype.\n    vAlign: null, // Several. Use CSS `vertical-align` instead\n    vLink: null, // `<body>`. Use CSS `a:visited {color}` instead\n    vSpace: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<img>` and `<object>`\n\n    // Non-standard Properties.\n    allowTransparency: null,\n    autoCorrect: null,\n    autoSave: null,\n    disablePictureInPicture: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    disableRemotePlayback: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    prefix: null,\n    property: null,\n    results: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    security: null,\n    unselectable: null\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/property-information/lib/html.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/property-information/lib/normalize.js":
/*!****************************************************************!*\
  !*** ../../node_modules/property-information/lib/normalize.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   normalize: () => (/* binding */ normalize)\n/* harmony export */ });\n/**\n * @param {string} value\n * @returns {string}\n */\nfunction normalize(value) {\n  return value.toLowerCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi9ub3JtYWxpemUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL25vcm1hbGl6ZS5qcz85MTUyIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHBhcmFtIHtzdHJpbmd9IHZhbHVlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gbm9ybWFsaXplKHZhbHVlKSB7XG4gIHJldHVybiB2YWx1ZS50b0xvd2VyQ2FzZSgpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/property-information/lib/normalize.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/property-information/lib/svg.js":
/*!**********************************************************!*\
  !*** ../../node_modules/property-information/lib/svg.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   svg: () => (/* binding */ svg)\n/* harmony export */ });\n/* harmony import */ var _util_types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/types.js */ \"(ssr)/../../node_modules/property-information/lib/util/types.js\");\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/../../node_modules/property-information/lib/util/create.js\");\n/* harmony import */ var _util_case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/case-sensitive-transform.js */ \"(ssr)/../../node_modules/property-information/lib/util/case-sensitive-transform.js\");\n\n\n\n\nconst svg = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  space: 'svg',\n  attributes: {\n    accentHeight: 'accent-height',\n    alignmentBaseline: 'alignment-baseline',\n    arabicForm: 'arabic-form',\n    baselineShift: 'baseline-shift',\n    capHeight: 'cap-height',\n    className: 'class',\n    clipPath: 'clip-path',\n    clipRule: 'clip-rule',\n    colorInterpolation: 'color-interpolation',\n    colorInterpolationFilters: 'color-interpolation-filters',\n    colorProfile: 'color-profile',\n    colorRendering: 'color-rendering',\n    crossOrigin: 'crossorigin',\n    dataType: 'datatype',\n    dominantBaseline: 'dominant-baseline',\n    enableBackground: 'enable-background',\n    fillOpacity: 'fill-opacity',\n    fillRule: 'fill-rule',\n    floodColor: 'flood-color',\n    floodOpacity: 'flood-opacity',\n    fontFamily: 'font-family',\n    fontSize: 'font-size',\n    fontSizeAdjust: 'font-size-adjust',\n    fontStretch: 'font-stretch',\n    fontStyle: 'font-style',\n    fontVariant: 'font-variant',\n    fontWeight: 'font-weight',\n    glyphName: 'glyph-name',\n    glyphOrientationHorizontal: 'glyph-orientation-horizontal',\n    glyphOrientationVertical: 'glyph-orientation-vertical',\n    hrefLang: 'hreflang',\n    horizAdvX: 'horiz-adv-x',\n    horizOriginX: 'horiz-origin-x',\n    horizOriginY: 'horiz-origin-y',\n    imageRendering: 'image-rendering',\n    letterSpacing: 'letter-spacing',\n    lightingColor: 'lighting-color',\n    markerEnd: 'marker-end',\n    markerMid: 'marker-mid',\n    markerStart: 'marker-start',\n    navDown: 'nav-down',\n    navDownLeft: 'nav-down-left',\n    navDownRight: 'nav-down-right',\n    navLeft: 'nav-left',\n    navNext: 'nav-next',\n    navPrev: 'nav-prev',\n    navRight: 'nav-right',\n    navUp: 'nav-up',\n    navUpLeft: 'nav-up-left',\n    navUpRight: 'nav-up-right',\n    onAbort: 'onabort',\n    onActivate: 'onactivate',\n    onAfterPrint: 'onafterprint',\n    onBeforePrint: 'onbeforeprint',\n    onBegin: 'onbegin',\n    onCancel: 'oncancel',\n    onCanPlay: 'oncanplay',\n    onCanPlayThrough: 'oncanplaythrough',\n    onChange: 'onchange',\n    onClick: 'onclick',\n    onClose: 'onclose',\n    onCopy: 'oncopy',\n    onCueChange: 'oncuechange',\n    onCut: 'oncut',\n    onDblClick: 'ondblclick',\n    onDrag: 'ondrag',\n    onDragEnd: 'ondragend',\n    onDragEnter: 'ondragenter',\n    onDragExit: 'ondragexit',\n    onDragLeave: 'ondragleave',\n    onDragOver: 'ondragover',\n    onDragStart: 'ondragstart',\n    onDrop: 'ondrop',\n    onDurationChange: 'ondurationchange',\n    onEmptied: 'onemptied',\n    onEnd: 'onend',\n    onEnded: 'onended',\n    onError: 'onerror',\n    onFocus: 'onfocus',\n    onFocusIn: 'onfocusin',\n    onFocusOut: 'onfocusout',\n    onHashChange: 'onhashchange',\n    onInput: 'oninput',\n    onInvalid: 'oninvalid',\n    onKeyDown: 'onkeydown',\n    onKeyPress: 'onkeypress',\n    onKeyUp: 'onkeyup',\n    onLoad: 'onload',\n    onLoadedData: 'onloadeddata',\n    onLoadedMetadata: 'onloadedmetadata',\n    onLoadStart: 'onloadstart',\n    onMessage: 'onmessage',\n    onMouseDown: 'onmousedown',\n    onMouseEnter: 'onmouseenter',\n    onMouseLeave: 'onmouseleave',\n    onMouseMove: 'onmousemove',\n    onMouseOut: 'onmouseout',\n    onMouseOver: 'onmouseover',\n    onMouseUp: 'onmouseup',\n    onMouseWheel: 'onmousewheel',\n    onOffline: 'onoffline',\n    onOnline: 'ononline',\n    onPageHide: 'onpagehide',\n    onPageShow: 'onpageshow',\n    onPaste: 'onpaste',\n    onPause: 'onpause',\n    onPlay: 'onplay',\n    onPlaying: 'onplaying',\n    onPopState: 'onpopstate',\n    onProgress: 'onprogress',\n    onRateChange: 'onratechange',\n    onRepeat: 'onrepeat',\n    onReset: 'onreset',\n    onResize: 'onresize',\n    onScroll: 'onscroll',\n    onSeeked: 'onseeked',\n    onSeeking: 'onseeking',\n    onSelect: 'onselect',\n    onShow: 'onshow',\n    onStalled: 'onstalled',\n    onStorage: 'onstorage',\n    onSubmit: 'onsubmit',\n    onSuspend: 'onsuspend',\n    onTimeUpdate: 'ontimeupdate',\n    onToggle: 'ontoggle',\n    onUnload: 'onunload',\n    onVolumeChange: 'onvolumechange',\n    onWaiting: 'onwaiting',\n    onZoom: 'onzoom',\n    overlinePosition: 'overline-position',\n    overlineThickness: 'overline-thickness',\n    paintOrder: 'paint-order',\n    panose1: 'panose-1',\n    pointerEvents: 'pointer-events',\n    referrerPolicy: 'referrerpolicy',\n    renderingIntent: 'rendering-intent',\n    shapeRendering: 'shape-rendering',\n    stopColor: 'stop-color',\n    stopOpacity: 'stop-opacity',\n    strikethroughPosition: 'strikethrough-position',\n    strikethroughThickness: 'strikethrough-thickness',\n    strokeDashArray: 'stroke-dasharray',\n    strokeDashOffset: 'stroke-dashoffset',\n    strokeLineCap: 'stroke-linecap',\n    strokeLineJoin: 'stroke-linejoin',\n    strokeMiterLimit: 'stroke-miterlimit',\n    strokeOpacity: 'stroke-opacity',\n    strokeWidth: 'stroke-width',\n    tabIndex: 'tabindex',\n    textAnchor: 'text-anchor',\n    textDecoration: 'text-decoration',\n    textRendering: 'text-rendering',\n    transformOrigin: 'transform-origin',\n    typeOf: 'typeof',\n    underlinePosition: 'underline-position',\n    underlineThickness: 'underline-thickness',\n    unicodeBidi: 'unicode-bidi',\n    unicodeRange: 'unicode-range',\n    unitsPerEm: 'units-per-em',\n    vAlphabetic: 'v-alphabetic',\n    vHanging: 'v-hanging',\n    vIdeographic: 'v-ideographic',\n    vMathematical: 'v-mathematical',\n    vectorEffect: 'vector-effect',\n    vertAdvY: 'vert-adv-y',\n    vertOriginX: 'vert-origin-x',\n    vertOriginY: 'vert-origin-y',\n    wordSpacing: 'word-spacing',\n    writingMode: 'writing-mode',\n    xHeight: 'x-height',\n    // These were camelcased in Tiny. Now lowercased in SVG 2\n    playbackOrder: 'playbackorder',\n    timelineBegin: 'timelinebegin'\n  },\n  transform: _util_case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__.caseSensitiveTransform,\n  properties: {\n    about: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    accentHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    accumulate: null,\n    additive: null,\n    alignmentBaseline: null,\n    alphabetic: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    amplitude: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    arabicForm: null,\n    ascent: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    attributeName: null,\n    attributeType: null,\n    azimuth: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    bandwidth: null,\n    baselineShift: null,\n    baseFrequency: null,\n    baseProfile: null,\n    bbox: null,\n    begin: null,\n    bias: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    by: null,\n    calcMode: null,\n    capHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    className: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    clip: null,\n    clipPath: null,\n    clipPathUnits: null,\n    clipRule: null,\n    color: null,\n    colorInterpolation: null,\n    colorInterpolationFilters: null,\n    colorProfile: null,\n    colorRendering: null,\n    content: null,\n    contentScriptType: null,\n    contentStyleType: null,\n    crossOrigin: null,\n    cursor: null,\n    cx: null,\n    cy: null,\n    d: null,\n    dataType: null,\n    defaultAction: null,\n    descent: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    diffuseConstant: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    direction: null,\n    display: null,\n    dur: null,\n    divisor: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    dominantBaseline: null,\n    download: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    dx: null,\n    dy: null,\n    edgeMode: null,\n    editable: null,\n    elevation: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    enableBackground: null,\n    end: null,\n    event: null,\n    exponent: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    externalResourcesRequired: null,\n    fill: null,\n    fillOpacity: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    fillRule: null,\n    filter: null,\n    filterRes: null,\n    filterUnits: null,\n    floodColor: null,\n    floodOpacity: null,\n    focusable: null,\n    focusHighlight: null,\n    fontFamily: null,\n    fontSize: null,\n    fontSizeAdjust: null,\n    fontStretch: null,\n    fontStyle: null,\n    fontVariant: null,\n    fontWeight: null,\n    format: null,\n    fr: null,\n    from: null,\n    fx: null,\n    fy: null,\n    g1: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaSeparated,\n    g2: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaSeparated,\n    glyphName: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaSeparated,\n    glyphOrientationHorizontal: null,\n    glyphOrientationVertical: null,\n    glyphRef: null,\n    gradientTransform: null,\n    gradientUnits: null,\n    handler: null,\n    hanging: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    hatchContentUnits: null,\n    hatchUnits: null,\n    height: null,\n    href: null,\n    hrefLang: null,\n    horizAdvX: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    horizOriginX: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    horizOriginY: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    id: null,\n    ideographic: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    imageRendering: null,\n    initialVisibility: null,\n    in: null,\n    in2: null,\n    intercept: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    k: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    k1: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    k2: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    k3: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    k4: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    kernelMatrix: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    kernelUnitLength: null,\n    keyPoints: null, // SEMI_COLON_SEPARATED\n    keySplines: null, // SEMI_COLON_SEPARATED\n    keyTimes: null, // SEMI_COLON_SEPARATED\n    kerning: null,\n    lang: null,\n    lengthAdjust: null,\n    letterSpacing: null,\n    lightingColor: null,\n    limitingConeAngle: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    local: null,\n    markerEnd: null,\n    markerMid: null,\n    markerStart: null,\n    markerHeight: null,\n    markerUnits: null,\n    markerWidth: null,\n    mask: null,\n    maskContentUnits: null,\n    maskUnits: null,\n    mathematical: null,\n    max: null,\n    media: null,\n    mediaCharacterEncoding: null,\n    mediaContentEncodings: null,\n    mediaSize: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    mediaTime: null,\n    method: null,\n    min: null,\n    mode: null,\n    name: null,\n    navDown: null,\n    navDownLeft: null,\n    navDownRight: null,\n    navLeft: null,\n    navNext: null,\n    navPrev: null,\n    navRight: null,\n    navUp: null,\n    navUpLeft: null,\n    navUpRight: null,\n    numOctaves: null,\n    observer: null,\n    offset: null,\n    onAbort: null,\n    onActivate: null,\n    onAfterPrint: null,\n    onBeforePrint: null,\n    onBegin: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnd: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFocusIn: null,\n    onFocusOut: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onMouseWheel: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRepeat: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onShow: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onZoom: null,\n    opacity: null,\n    operator: null,\n    order: null,\n    orient: null,\n    orientation: null,\n    origin: null,\n    overflow: null,\n    overlay: null,\n    overlinePosition: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    overlineThickness: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    paintOrder: null,\n    panose1: null,\n    path: null,\n    pathLength: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    patternContentUnits: null,\n    patternTransform: null,\n    patternUnits: null,\n    phase: null,\n    ping: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    pitch: null,\n    playbackOrder: null,\n    pointerEvents: null,\n    points: null,\n    pointsAtX: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    pointsAtY: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    pointsAtZ: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    preserveAlpha: null,\n    preserveAspectRatio: null,\n    primitiveUnits: null,\n    propagate: null,\n    property: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    r: null,\n    radius: null,\n    referrerPolicy: null,\n    refX: null,\n    refY: null,\n    rel: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    rev: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    renderingIntent: null,\n    repeatCount: null,\n    repeatDur: null,\n    requiredExtensions: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    requiredFeatures: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    requiredFonts: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    requiredFormats: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    resource: null,\n    restart: null,\n    result: null,\n    rotate: null,\n    rx: null,\n    ry: null,\n    scale: null,\n    seed: null,\n    shapeRendering: null,\n    side: null,\n    slope: null,\n    snapshotTime: null,\n    specularConstant: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    specularExponent: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    spreadMethod: null,\n    spacing: null,\n    startOffset: null,\n    stdDeviation: null,\n    stemh: null,\n    stemv: null,\n    stitchTiles: null,\n    stopColor: null,\n    stopOpacity: null,\n    strikethroughPosition: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    strikethroughThickness: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    string: null,\n    stroke: null,\n    strokeDashArray: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    strokeDashOffset: null,\n    strokeLineCap: null,\n    strokeLineJoin: null,\n    strokeMiterLimit: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    strokeOpacity: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    strokeWidth: null,\n    style: null,\n    surfaceScale: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    syncBehavior: null,\n    syncBehaviorDefault: null,\n    syncMaster: null,\n    syncTolerance: null,\n    syncToleranceDefault: null,\n    systemLanguage: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    tabIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    tableValues: null,\n    target: null,\n    targetX: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    targetY: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    textAnchor: null,\n    textDecoration: null,\n    textRendering: null,\n    textLength: null,\n    timelineBegin: null,\n    title: null,\n    transformBehavior: null,\n    type: null,\n    typeOf: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    to: null,\n    transform: null,\n    transformOrigin: null,\n    u1: null,\n    u2: null,\n    underlinePosition: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    underlineThickness: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    unicode: null,\n    unicodeBidi: null,\n    unicodeRange: null,\n    unitsPerEm: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    values: null,\n    vAlphabetic: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    vMathematical: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    vectorEffect: null,\n    vHanging: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    vIdeographic: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    version: null,\n    vertAdvY: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    vertOriginX: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    vertOriginY: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    viewBox: null,\n    viewTarget: null,\n    visibility: null,\n    width: null,\n    widths: null,\n    wordSpacing: null,\n    writingMode: null,\n    x: null,\n    x1: null,\n    x2: null,\n    xChannelSelector: null,\n    xHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    y: null,\n    y1: null,\n    y2: null,\n    yChannelSelector: null,\n    z: null,\n    zoomAndPan: null\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/property-information/lib/svg.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/property-information/lib/util/case-insensitive-transform.js":
/*!**************************************************************************************!*\
  !*** ../../node_modules/property-information/lib/util/case-insensitive-transform.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   caseInsensitiveTransform: () => (/* binding */ caseInsensitiveTransform)\n/* harmony export */ });\n/* harmony import */ var _case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./case-sensitive-transform.js */ \"(ssr)/../../node_modules/property-information/lib/util/case-sensitive-transform.js\");\n\n\n/**\n * @param {Record<string, string>} attributes\n * @param {string} property\n * @returns {string}\n */\nfunction caseInsensitiveTransform(attributes, property) {\n  return (0,_case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_0__.caseSensitiveTransform)(attributes, property.toLowerCase())\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL2Nhc2UtaW5zZW5zaXRpdmUtdHJhbnNmb3JtLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9FOztBQUVwRTtBQUNBLFdBQVcsd0JBQXdCO0FBQ25DLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDTztBQUNQLFNBQVMsb0ZBQXNCO0FBQy9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIvdXRpbC9jYXNlLWluc2Vuc2l0aXZlLXRyYW5zZm9ybS5qcz81N2U2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Y2FzZVNlbnNpdGl2ZVRyYW5zZm9ybX0gZnJvbSAnLi9jYXNlLXNlbnNpdGl2ZS10cmFuc2Zvcm0uanMnXG5cbi8qKlxuICogQHBhcmFtIHtSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+fSBhdHRyaWJ1dGVzXG4gKiBAcGFyYW0ge3N0cmluZ30gcHJvcGVydHlcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjYXNlSW5zZW5zaXRpdmVUcmFuc2Zvcm0oYXR0cmlidXRlcywgcHJvcGVydHkpIHtcbiAgcmV0dXJuIGNhc2VTZW5zaXRpdmVUcmFuc2Zvcm0oYXR0cmlidXRlcywgcHJvcGVydHkudG9Mb3dlckNhc2UoKSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/property-information/lib/util/case-insensitive-transform.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/property-information/lib/util/case-sensitive-transform.js":
/*!************************************************************************************!*\
  !*** ../../node_modules/property-information/lib/util/case-sensitive-transform.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   caseSensitiveTransform: () => (/* binding */ caseSensitiveTransform)\n/* harmony export */ });\n/**\n * @param {Record<string, string>} attributes\n * @param {string} attribute\n * @returns {string}\n */\nfunction caseSensitiveTransform(attributes, attribute) {\n  return attribute in attributes ? attributes[attribute] : attribute\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL2Nhc2Utc2Vuc2l0aXZlLXRyYW5zZm9ybS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxXQUFXLHdCQUF3QjtBQUNuQyxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIvdXRpbC9jYXNlLXNlbnNpdGl2ZS10cmFuc2Zvcm0uanM/OTNmNCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBwYXJhbSB7UmVjb3JkPHN0cmluZywgc3RyaW5nPn0gYXR0cmlidXRlc1xuICogQHBhcmFtIHtzdHJpbmd9IGF0dHJpYnV0ZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNhc2VTZW5zaXRpdmVUcmFuc2Zvcm0oYXR0cmlidXRlcywgYXR0cmlidXRlKSB7XG4gIHJldHVybiBhdHRyaWJ1dGUgaW4gYXR0cmlidXRlcyA/IGF0dHJpYnV0ZXNbYXR0cmlidXRlXSA6IGF0dHJpYnV0ZVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/property-information/lib/util/case-sensitive-transform.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/property-information/lib/util/create.js":
/*!******************************************************************!*\
  !*** ../../node_modules/property-information/lib/util/create.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create)\n/* harmony export */ });\n/* harmony import */ var _normalize_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../normalize.js */ \"(ssr)/../../node_modules/property-information/lib/normalize.js\");\n/* harmony import */ var _schema_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./schema.js */ \"(ssr)/../../node_modules/property-information/lib/util/schema.js\");\n/* harmony import */ var _defined_info_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defined-info.js */ \"(ssr)/../../node_modules/property-information/lib/util/defined-info.js\");\n/**\n * @typedef {import('./schema.js').Properties} Properties\n * @typedef {import('./schema.js').Normal} Normal\n *\n * @typedef {Record<string, string>} Attributes\n *\n * @typedef {Object} Definition\n * @property {Record<string, number|null>} properties\n * @property {(attributes: Attributes, property: string) => string} transform\n * @property {string} [space]\n * @property {Attributes} [attributes]\n * @property {Array<string>} [mustUseProperty]\n */\n\n\n\n\n\nconst own = {}.hasOwnProperty\n\n/**\n * @param {Definition} definition\n * @returns {Schema}\n */\nfunction create(definition) {\n  /** @type {Properties} */\n  const property = {}\n  /** @type {Normal} */\n  const normal = {}\n  /** @type {string} */\n  let prop\n\n  for (prop in definition.properties) {\n    if (own.call(definition.properties, prop)) {\n      const value = definition.properties[prop]\n      const info = new _defined_info_js__WEBPACK_IMPORTED_MODULE_0__.DefinedInfo(\n        prop,\n        definition.transform(definition.attributes || {}, prop),\n        value,\n        definition.space\n      )\n\n      if (\n        definition.mustUseProperty &&\n        definition.mustUseProperty.includes(prop)\n      ) {\n        info.mustUseProperty = true\n      }\n\n      property[prop] = info\n\n      normal[(0,_normalize_js__WEBPACK_IMPORTED_MODULE_1__.normalize)(prop)] = prop\n      normal[(0,_normalize_js__WEBPACK_IMPORTED_MODULE_1__.normalize)(info.attribute)] = prop\n    }\n  }\n\n  return new _schema_js__WEBPACK_IMPORTED_MODULE_2__.Schema(property, normal, definition.space)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/property-information/lib/util/create.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/property-information/lib/util/defined-info.js":
/*!************************************************************************!*\
  !*** ../../node_modules/property-information/lib/util/defined-info.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefinedInfo: () => (/* binding */ DefinedInfo)\n/* harmony export */ });\n/* harmony import */ var _info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./info.js */ \"(ssr)/../../node_modules/property-information/lib/util/info.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/../../node_modules/property-information/lib/util/types.js\");\n\n\n\n/** @type {Array<keyof types>} */\n// @ts-expect-error: hush.\nconst checks = Object.keys(_types_js__WEBPACK_IMPORTED_MODULE_0__)\n\nclass DefinedInfo extends _info_js__WEBPACK_IMPORTED_MODULE_1__.Info {\n  /**\n   * @constructor\n   * @param {string} property\n   * @param {string} attribute\n   * @param {number|null} [mask]\n   * @param {string} [space]\n   */\n  constructor(property, attribute, mask, space) {\n    let index = -1\n\n    super(property, attribute)\n\n    mark(this, 'space', space)\n\n    if (typeof mask === 'number') {\n      while (++index < checks.length) {\n        const check = checks[index]\n        mark(this, checks[index], (mask & _types_js__WEBPACK_IMPORTED_MODULE_0__[check]) === _types_js__WEBPACK_IMPORTED_MODULE_0__[check])\n      }\n    }\n  }\n}\n\nDefinedInfo.prototype.defined = true\n\n/**\n * @param {DefinedInfo} values\n * @param {string} key\n * @param {unknown} value\n */\nfunction mark(values, key, value) {\n  if (value) {\n    // @ts-expect-error: assume `value` matches the expected value of `key`.\n    values[key] = value\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/property-information/lib/util/defined-info.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/property-information/lib/util/info.js":
/*!****************************************************************!*\
  !*** ../../node_modules/property-information/lib/util/info.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Info: () => (/* binding */ Info)\n/* harmony export */ });\nclass Info {\n  /**\n   * @constructor\n   * @param {string} property\n   * @param {string} attribute\n   */\n  constructor(property, attribute) {\n    /** @type {string} */\n    this.property = property\n    /** @type {string} */\n    this.attribute = attribute\n  }\n}\n\n/** @type {string|null} */\nInfo.prototype.space = null\nInfo.prototype.boolean = false\nInfo.prototype.booleanish = false\nInfo.prototype.overloadedBoolean = false\nInfo.prototype.number = false\nInfo.prototype.commaSeparated = false\nInfo.prototype.spaceSeparated = false\nInfo.prototype.commaOrSpaceSeparated = false\nInfo.prototype.mustUseProperty = false\nInfo.prototype.defined = false\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL2luZm8uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBLGFBQWEsUUFBUTtBQUNyQixhQUFhLFFBQVE7QUFDckI7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QjtBQUNBLGVBQWUsUUFBUTtBQUN2QjtBQUNBO0FBQ0E7O0FBRUEsV0FBVyxhQUFhO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIvdXRpbC9pbmZvLmpzP2NhYzIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNsYXNzIEluZm8ge1xuICAvKipcbiAgICogQGNvbnN0cnVjdG9yXG4gICAqIEBwYXJhbSB7c3RyaW5nfSBwcm9wZXJ0eVxuICAgKiBAcGFyYW0ge3N0cmluZ30gYXR0cmlidXRlXG4gICAqL1xuICBjb25zdHJ1Y3Rvcihwcm9wZXJ0eSwgYXR0cmlidXRlKSB7XG4gICAgLyoqIEB0eXBlIHtzdHJpbmd9ICovXG4gICAgdGhpcy5wcm9wZXJ0eSA9IHByb3BlcnR5XG4gICAgLyoqIEB0eXBlIHtzdHJpbmd9ICovXG4gICAgdGhpcy5hdHRyaWJ1dGUgPSBhdHRyaWJ1dGVcbiAgfVxufVxuXG4vKiogQHR5cGUge3N0cmluZ3xudWxsfSAqL1xuSW5mby5wcm90b3R5cGUuc3BhY2UgPSBudWxsXG5JbmZvLnByb3RvdHlwZS5ib29sZWFuID0gZmFsc2VcbkluZm8ucHJvdG90eXBlLmJvb2xlYW5pc2ggPSBmYWxzZVxuSW5mby5wcm90b3R5cGUub3ZlcmxvYWRlZEJvb2xlYW4gPSBmYWxzZVxuSW5mby5wcm90b3R5cGUubnVtYmVyID0gZmFsc2VcbkluZm8ucHJvdG90eXBlLmNvbW1hU2VwYXJhdGVkID0gZmFsc2VcbkluZm8ucHJvdG90eXBlLnNwYWNlU2VwYXJhdGVkID0gZmFsc2VcbkluZm8ucHJvdG90eXBlLmNvbW1hT3JTcGFjZVNlcGFyYXRlZCA9IGZhbHNlXG5JbmZvLnByb3RvdHlwZS5tdXN0VXNlUHJvcGVydHkgPSBmYWxzZVxuSW5mby5wcm90b3R5cGUuZGVmaW5lZCA9IGZhbHNlXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/property-information/lib/util/info.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/property-information/lib/util/merge.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/property-information/lib/util/merge.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   merge: () => (/* binding */ merge)\n/* harmony export */ });\n/* harmony import */ var _schema_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./schema.js */ \"(ssr)/../../node_modules/property-information/lib/util/schema.js\");\n/**\n * @typedef {import('./schema.js').Properties} Properties\n * @typedef {import('./schema.js').Normal} Normal\n */\n\n\n\n/**\n * @param {Schema[]} definitions\n * @param {string} [space]\n * @returns {Schema}\n */\nfunction merge(definitions, space) {\n  /** @type {Properties} */\n  const property = {}\n  /** @type {Normal} */\n  const normal = {}\n  let index = -1\n\n  while (++index < definitions.length) {\n    Object.assign(property, definitions[index].property)\n    Object.assign(normal, definitions[index].normal)\n  }\n\n  return new _schema_js__WEBPACK_IMPORTED_MODULE_0__.Schema(property, normal, space)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL21lcmdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLGtDQUFrQztBQUMvQyxhQUFhLDhCQUE4QjtBQUMzQzs7QUFFa0M7O0FBRWxDO0FBQ0EsV0FBVyxVQUFVO0FBQ3JCLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDTztBQUNQLGFBQWEsWUFBWTtBQUN6QjtBQUNBLGFBQWEsUUFBUTtBQUNyQjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGFBQWEsOENBQU07QUFDbkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL21lcmdlLmpzPzc0ZWIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL3NjaGVtYS5qcycpLlByb3BlcnRpZXN9IFByb3BlcnRpZXNcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4vc2NoZW1hLmpzJykuTm9ybWFsfSBOb3JtYWxcbiAqL1xuXG5pbXBvcnQge1NjaGVtYX0gZnJvbSAnLi9zY2hlbWEuanMnXG5cbi8qKlxuICogQHBhcmFtIHtTY2hlbWFbXX0gZGVmaW5pdGlvbnNcbiAqIEBwYXJhbSB7c3RyaW5nfSBbc3BhY2VdXG4gKiBAcmV0dXJucyB7U2NoZW1hfVxuICovXG5leHBvcnQgZnVuY3Rpb24gbWVyZ2UoZGVmaW5pdGlvbnMsIHNwYWNlKSB7XG4gIC8qKiBAdHlwZSB7UHJvcGVydGllc30gKi9cbiAgY29uc3QgcHJvcGVydHkgPSB7fVxuICAvKiogQHR5cGUge05vcm1hbH0gKi9cbiAgY29uc3Qgbm9ybWFsID0ge31cbiAgbGV0IGluZGV4ID0gLTFcblxuICB3aGlsZSAoKytpbmRleCA8IGRlZmluaXRpb25zLmxlbmd0aCkge1xuICAgIE9iamVjdC5hc3NpZ24ocHJvcGVydHksIGRlZmluaXRpb25zW2luZGV4XS5wcm9wZXJ0eSlcbiAgICBPYmplY3QuYXNzaWduKG5vcm1hbCwgZGVmaW5pdGlvbnNbaW5kZXhdLm5vcm1hbClcbiAgfVxuXG4gIHJldHVybiBuZXcgU2NoZW1hKHByb3BlcnR5LCBub3JtYWwsIHNwYWNlKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/property-information/lib/util/merge.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/property-information/lib/util/schema.js":
/*!******************************************************************!*\
  !*** ../../node_modules/property-information/lib/util/schema.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Schema: () => (/* binding */ Schema)\n/* harmony export */ });\n/**\n * @typedef {import('./info.js').Info} Info\n * @typedef {Record<string, Info>} Properties\n * @typedef {Record<string, string>} Normal\n */\n\nclass Schema {\n  /**\n   * @constructor\n   * @param {Properties} property\n   * @param {Normal} normal\n   * @param {string} [space]\n   */\n  constructor(property, normal, space) {\n    this.property = property\n    this.normal = normal\n    if (space) {\n      this.space = space\n    }\n  }\n}\n\n/** @type {Properties} */\nSchema.prototype.property = {}\n/** @type {Normal} */\nSchema.prototype.normal = {}\n/** @type {string|null} */\nSchema.prototype.space = null\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL3NjaGVtYS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLDBCQUEwQjtBQUN2QyxhQUFhLHNCQUFzQjtBQUNuQyxhQUFhLHdCQUF3QjtBQUNyQzs7QUFFTztBQUNQO0FBQ0E7QUFDQSxhQUFhLFlBQVk7QUFDekIsYUFBYSxRQUFRO0FBQ3JCLGFBQWEsUUFBUTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsV0FBVyxZQUFZO0FBQ3ZCO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0EsV0FBVyxhQUFhO0FBQ3hCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIvdXRpbC9zY2hlbWEuanM/YzNlNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4vaW5mby5qcycpLkluZm99IEluZm9cbiAqIEB0eXBlZGVmIHtSZWNvcmQ8c3RyaW5nLCBJbmZvPn0gUHJvcGVydGllc1xuICogQHR5cGVkZWYge1JlY29yZDxzdHJpbmcsIHN0cmluZz59IE5vcm1hbFxuICovXG5cbmV4cG9ydCBjbGFzcyBTY2hlbWEge1xuICAvKipcbiAgICogQGNvbnN0cnVjdG9yXG4gICAqIEBwYXJhbSB7UHJvcGVydGllc30gcHJvcGVydHlcbiAgICogQHBhcmFtIHtOb3JtYWx9IG5vcm1hbFxuICAgKiBAcGFyYW0ge3N0cmluZ30gW3NwYWNlXVxuICAgKi9cbiAgY29uc3RydWN0b3IocHJvcGVydHksIG5vcm1hbCwgc3BhY2UpIHtcbiAgICB0aGlzLnByb3BlcnR5ID0gcHJvcGVydHlcbiAgICB0aGlzLm5vcm1hbCA9IG5vcm1hbFxuICAgIGlmIChzcGFjZSkge1xuICAgICAgdGhpcy5zcGFjZSA9IHNwYWNlXG4gICAgfVxuICB9XG59XG5cbi8qKiBAdHlwZSB7UHJvcGVydGllc30gKi9cblNjaGVtYS5wcm90b3R5cGUucHJvcGVydHkgPSB7fVxuLyoqIEB0eXBlIHtOb3JtYWx9ICovXG5TY2hlbWEucHJvdG90eXBlLm5vcm1hbCA9IHt9XG4vKiogQHR5cGUge3N0cmluZ3xudWxsfSAqL1xuU2NoZW1hLnByb3RvdHlwZS5zcGFjZSA9IG51bGxcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/property-information/lib/util/schema.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/property-information/lib/util/types.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/property-information/lib/util/types.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   boolean: () => (/* binding */ boolean),\n/* harmony export */   booleanish: () => (/* binding */ booleanish),\n/* harmony export */   commaOrSpaceSeparated: () => (/* binding */ commaOrSpaceSeparated),\n/* harmony export */   commaSeparated: () => (/* binding */ commaSeparated),\n/* harmony export */   number: () => (/* binding */ number),\n/* harmony export */   overloadedBoolean: () => (/* binding */ overloadedBoolean),\n/* harmony export */   spaceSeparated: () => (/* binding */ spaceSeparated)\n/* harmony export */ });\nlet powers = 0\n\nconst boolean = increment()\nconst booleanish = increment()\nconst overloadedBoolean = increment()\nconst number = increment()\nconst spaceSeparated = increment()\nconst commaSeparated = increment()\nconst commaOrSpaceSeparated = increment()\n\nfunction increment() {\n  return 2 ** ++powers\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL3R5cGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQTs7QUFFTztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFUDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL3R5cGVzLmpzP2NiODgiXSwic291cmNlc0NvbnRlbnQiOlsibGV0IHBvd2VycyA9IDBcblxuZXhwb3J0IGNvbnN0IGJvb2xlYW4gPSBpbmNyZW1lbnQoKVxuZXhwb3J0IGNvbnN0IGJvb2xlYW5pc2ggPSBpbmNyZW1lbnQoKVxuZXhwb3J0IGNvbnN0IG92ZXJsb2FkZWRCb29sZWFuID0gaW5jcmVtZW50KClcbmV4cG9ydCBjb25zdCBudW1iZXIgPSBpbmNyZW1lbnQoKVxuZXhwb3J0IGNvbnN0IHNwYWNlU2VwYXJhdGVkID0gaW5jcmVtZW50KClcbmV4cG9ydCBjb25zdCBjb21tYVNlcGFyYXRlZCA9IGluY3JlbWVudCgpXG5leHBvcnQgY29uc3QgY29tbWFPclNwYWNlU2VwYXJhdGVkID0gaW5jcmVtZW50KClcblxuZnVuY3Rpb24gaW5jcmVtZW50KCkge1xuICByZXR1cm4gMiAqKiArK3Bvd2Vyc1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/property-information/lib/util/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/property-information/lib/xlink.js":
/*!************************************************************!*\
  !*** ../../node_modules/property-information/lib/xlink.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   xlink: () => (/* binding */ xlink)\n/* harmony export */ });\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/../../node_modules/property-information/lib/util/create.js\");\n\n\nconst xlink = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  space: 'xlink',\n  transform(_, prop) {\n    return 'xlink:' + prop.slice(5).toLowerCase()\n  },\n  properties: {\n    xLinkActuate: null,\n    xLinkArcRole: null,\n    xLinkHref: null,\n    xLinkRole: null,\n    xLinkShow: null,\n    xLinkTitle: null,\n    xLinkType: null\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi94bGluay5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1Qzs7QUFFaEMsY0FBYyx1REFBTTtBQUMzQjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3hsaW5rLmpzPzhkZmEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtjcmVhdGV9IGZyb20gJy4vdXRpbC9jcmVhdGUuanMnXG5cbmV4cG9ydCBjb25zdCB4bGluayA9IGNyZWF0ZSh7XG4gIHNwYWNlOiAneGxpbmsnLFxuICB0cmFuc2Zvcm0oXywgcHJvcCkge1xuICAgIHJldHVybiAneGxpbms6JyArIHByb3Auc2xpY2UoNSkudG9Mb3dlckNhc2UoKVxuICB9LFxuICBwcm9wZXJ0aWVzOiB7XG4gICAgeExpbmtBY3R1YXRlOiBudWxsLFxuICAgIHhMaW5rQXJjUm9sZTogbnVsbCxcbiAgICB4TGlua0hyZWY6IG51bGwsXG4gICAgeExpbmtSb2xlOiBudWxsLFxuICAgIHhMaW5rU2hvdzogbnVsbCxcbiAgICB4TGlua1RpdGxlOiBudWxsLFxuICAgIHhMaW5rVHlwZTogbnVsbFxuICB9XG59KVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/property-information/lib/xlink.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/property-information/lib/xml.js":
/*!**********************************************************!*\
  !*** ../../node_modules/property-information/lib/xml.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   xml: () => (/* binding */ xml)\n/* harmony export */ });\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/../../node_modules/property-information/lib/util/create.js\");\n\n\nconst xml = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  space: 'xml',\n  transform(_, prop) {\n    return 'xml:' + prop.slice(3).toLowerCase()\n  },\n  properties: {xmlLang: null, xmlBase: null, xmlSpace: null}\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi94bWwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBdUM7O0FBRWhDLFlBQVksdURBQU07QUFDekI7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILGVBQWU7QUFDZixDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIveG1sLmpzP2I5NzAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtjcmVhdGV9IGZyb20gJy4vdXRpbC9jcmVhdGUuanMnXG5cbmV4cG9ydCBjb25zdCB4bWwgPSBjcmVhdGUoe1xuICBzcGFjZTogJ3htbCcsXG4gIHRyYW5zZm9ybShfLCBwcm9wKSB7XG4gICAgcmV0dXJuICd4bWw6JyArIHByb3Auc2xpY2UoMykudG9Mb3dlckNhc2UoKVxuICB9LFxuICBwcm9wZXJ0aWVzOiB7eG1sTGFuZzogbnVsbCwgeG1sQmFzZTogbnVsbCwgeG1sU3BhY2U6IG51bGx9XG59KVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/property-information/lib/xml.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/property-information/lib/xmlns.js":
/*!************************************************************!*\
  !*** ../../node_modules/property-information/lib/xmlns.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   xmlns: () => (/* binding */ xmlns)\n/* harmony export */ });\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/../../node_modules/property-information/lib/util/create.js\");\n/* harmony import */ var _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/case-insensitive-transform.js */ \"(ssr)/../../node_modules/property-information/lib/util/case-insensitive-transform.js\");\n\n\n\nconst xmlns = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  space: 'xmlns',\n  attributes: {xmlnsxlink: 'xmlns:xlink'},\n  transform: _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__.caseInsensitiveTransform,\n  properties: {xmlns: null, xmlnsXLink: null}\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi94bWxucy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBdUM7QUFDc0M7O0FBRXRFLGNBQWMsdURBQU07QUFDM0I7QUFDQSxlQUFlLDBCQUEwQjtBQUN6QyxhQUFhLHlGQUF3QjtBQUNyQyxlQUFlO0FBQ2YsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3htbG5zLmpzP2I0ODQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtjcmVhdGV9IGZyb20gJy4vdXRpbC9jcmVhdGUuanMnXG5pbXBvcnQge2Nhc2VJbnNlbnNpdGl2ZVRyYW5zZm9ybX0gZnJvbSAnLi91dGlsL2Nhc2UtaW5zZW5zaXRpdmUtdHJhbnNmb3JtLmpzJ1xuXG5leHBvcnQgY29uc3QgeG1sbnMgPSBjcmVhdGUoe1xuICBzcGFjZTogJ3htbG5zJyxcbiAgYXR0cmlidXRlczoge3htbG5zeGxpbms6ICd4bWxuczp4bGluayd9LFxuICB0cmFuc2Zvcm06IGNhc2VJbnNlbnNpdGl2ZVRyYW5zZm9ybSxcbiAgcHJvcGVydGllczoge3htbG5zOiBudWxsLCB4bWxuc1hMaW5rOiBudWxsfVxufSlcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/property-information/lib/xmlns.js\n");

/***/ })

};
;