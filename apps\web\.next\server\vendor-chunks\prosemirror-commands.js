"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-commands";
exports.ids = ["vendor-chunks/prosemirror-commands"];
exports.modules = {

/***/ "(ssr)/../../node_modules/prosemirror-commands/dist/index.js":
/*!*************************************************************!*\
  !*** ../../node_modules/prosemirror-commands/dist/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   autoJoin: () => (/* binding */ autoJoin),\n/* harmony export */   baseKeymap: () => (/* binding */ baseKeymap),\n/* harmony export */   chainCommands: () => (/* binding */ chainCommands),\n/* harmony export */   createParagraphNear: () => (/* binding */ createParagraphNear),\n/* harmony export */   deleteSelection: () => (/* binding */ deleteSelection),\n/* harmony export */   exitCode: () => (/* binding */ exitCode),\n/* harmony export */   joinBackward: () => (/* binding */ joinBackward),\n/* harmony export */   joinDown: () => (/* binding */ joinDown),\n/* harmony export */   joinForward: () => (/* binding */ joinForward),\n/* harmony export */   joinTextblockBackward: () => (/* binding */ joinTextblockBackward),\n/* harmony export */   joinTextblockForward: () => (/* binding */ joinTextblockForward),\n/* harmony export */   joinUp: () => (/* binding */ joinUp),\n/* harmony export */   lift: () => (/* binding */ lift),\n/* harmony export */   liftEmptyBlock: () => (/* binding */ liftEmptyBlock),\n/* harmony export */   macBaseKeymap: () => (/* binding */ macBaseKeymap),\n/* harmony export */   newlineInCode: () => (/* binding */ newlineInCode),\n/* harmony export */   pcBaseKeymap: () => (/* binding */ pcBaseKeymap),\n/* harmony export */   selectAll: () => (/* binding */ selectAll),\n/* harmony export */   selectNodeBackward: () => (/* binding */ selectNodeBackward),\n/* harmony export */   selectNodeForward: () => (/* binding */ selectNodeForward),\n/* harmony export */   selectParentNode: () => (/* binding */ selectParentNode),\n/* harmony export */   selectTextblockEnd: () => (/* binding */ selectTextblockEnd),\n/* harmony export */   selectTextblockStart: () => (/* binding */ selectTextblockStart),\n/* harmony export */   setBlockType: () => (/* binding */ setBlockType),\n/* harmony export */   splitBlock: () => (/* binding */ splitBlock),\n/* harmony export */   splitBlockAs: () => (/* binding */ splitBlockAs),\n/* harmony export */   splitBlockKeepMarks: () => (/* binding */ splitBlockKeepMarks),\n/* harmony export */   toggleMark: () => (/* binding */ toggleMark),\n/* harmony export */   wrapIn: () => (/* binding */ wrapIn)\n/* harmony export */ });\n/* harmony import */ var prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-transform */ \"(ssr)/../../node_modules/prosemirror-transform/dist/index.js\");\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/../../node_modules/prosemirror-model/dist/index.js\");\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/../../node_modules/prosemirror-state/dist/index.js\");\n\n\n\n\n/**\nDelete the selection, if there is one.\n*/\nconst deleteSelection = (state, dispatch) => {\n    if (state.selection.empty)\n        return false;\n    if (dispatch)\n        dispatch(state.tr.deleteSelection().scrollIntoView());\n    return true;\n};\nfunction atBlockStart(state, view) {\n    let { $cursor } = state.selection;\n    if (!$cursor || (view ? !view.endOfTextblock(\"backward\", state)\n        : $cursor.parentOffset > 0))\n        return null;\n    return $cursor;\n}\n/**\nIf the selection is empty and at the start of a textblock, try to\nreduce the distance between that block and the one before it—if\nthere's a block directly before it that can be joined, join them.\nIf not, try to move the selected block closer to the next one in\nthe document structure by lifting it out of its parent or moving it\ninto a parent of the previous block. Will use the view for accurate\n(bidi-aware) start-of-textblock detection if given.\n*/\nconst joinBackward = (state, dispatch, view) => {\n    let $cursor = atBlockStart(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutBefore($cursor);\n    // If there is no node before this, try to lift\n    if (!$cut) {\n        let range = $cursor.blockRange(), target = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.liftTarget)(range);\n        if (target == null)\n            return false;\n        if (dispatch)\n            dispatch(state.tr.lift(range, target).scrollIntoView());\n        return true;\n    }\n    let before = $cut.nodeBefore;\n    // Apply the joining algorithm\n    if (deleteBarrier(state, $cut, dispatch, -1))\n        return true;\n    // If the node below has no content and the node above is\n    // selectable, delete the node below and select the one above.\n    if ($cursor.parent.content.size == 0 &&\n        (textblockAt(before, \"end\") || prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.isSelectable(before))) {\n        for (let depth = $cursor.depth;; depth--) {\n            let delStep = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.replaceStep)(state.doc, $cursor.before(depth), $cursor.after(depth), prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice.empty);\n            if (delStep && delStep.slice.size < delStep.to - delStep.from) {\n                if (dispatch) {\n                    let tr = state.tr.step(delStep);\n                    tr.setSelection(textblockAt(before, \"end\")\n                        ? prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.Selection.findFrom(tr.doc.resolve(tr.mapping.map($cut.pos, -1)), -1)\n                        : prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(tr.doc, $cut.pos - before.nodeSize));\n                    dispatch(tr.scrollIntoView());\n                }\n                return true;\n            }\n            if (depth == 1 || $cursor.node(depth - 1).childCount > 1)\n                break;\n        }\n    }\n    // If the node before is an atom, delete it\n    if (before.isAtom && $cut.depth == $cursor.depth - 1) {\n        if (dispatch)\n            dispatch(state.tr.delete($cut.pos - before.nodeSize, $cut.pos).scrollIntoView());\n        return true;\n    }\n    return false;\n};\n/**\nA more limited form of [`joinBackward`]($commands.joinBackward)\nthat only tries to join the current textblock to the one before\nit, if the cursor is at the start of a textblock.\n*/\nconst joinTextblockBackward = (state, dispatch, view) => {\n    let $cursor = atBlockStart(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutBefore($cursor);\n    return $cut ? joinTextblocksAround(state, $cut, dispatch) : false;\n};\n/**\nA more limited form of [`joinForward`]($commands.joinForward)\nthat only tries to join the current textblock to the one after\nit, if the cursor is at the end of a textblock.\n*/\nconst joinTextblockForward = (state, dispatch, view) => {\n    let $cursor = atBlockEnd(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutAfter($cursor);\n    return $cut ? joinTextblocksAround(state, $cut, dispatch) : false;\n};\nfunction joinTextblocksAround(state, $cut, dispatch) {\n    let before = $cut.nodeBefore, beforeText = before, beforePos = $cut.pos - 1;\n    for (; !beforeText.isTextblock; beforePos--) {\n        if (beforeText.type.spec.isolating)\n            return false;\n        let child = beforeText.lastChild;\n        if (!child)\n            return false;\n        beforeText = child;\n    }\n    let after = $cut.nodeAfter, afterText = after, afterPos = $cut.pos + 1;\n    for (; !afterText.isTextblock; afterPos++) {\n        if (afterText.type.spec.isolating)\n            return false;\n        let child = afterText.firstChild;\n        if (!child)\n            return false;\n        afterText = child;\n    }\n    let step = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.replaceStep)(state.doc, beforePos, afterPos, prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice.empty);\n    if (!step || step.from != beforePos ||\n        step instanceof prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.ReplaceStep && step.slice.size >= afterPos - beforePos)\n        return false;\n    if (dispatch) {\n        let tr = state.tr.step(step);\n        tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.TextSelection.create(tr.doc, beforePos));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n}\nfunction textblockAt(node, side, only = false) {\n    for (let scan = node; scan; scan = (side == \"start\" ? scan.firstChild : scan.lastChild)) {\n        if (scan.isTextblock)\n            return true;\n        if (only && scan.childCount != 1)\n            return false;\n    }\n    return false;\n}\n/**\nWhen the selection is empty and at the start of a textblock, select\nthe node before that textblock, if possible. This is intended to be\nbound to keys like backspace, after\n[`joinBackward`](https://prosemirror.net/docs/ref/#commands.joinBackward) or other deleting\ncommands, as a fall-back behavior when the schema doesn't allow\ndeletion at the selected point.\n*/\nconst selectNodeBackward = (state, dispatch, view) => {\n    let { $head, empty } = state.selection, $cut = $head;\n    if (!empty)\n        return false;\n    if ($head.parent.isTextblock) {\n        if (view ? !view.endOfTextblock(\"backward\", state) : $head.parentOffset > 0)\n            return false;\n        $cut = findCutBefore($head);\n    }\n    let node = $cut && $cut.nodeBefore;\n    if (!node || !prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.isSelectable(node))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(state.doc, $cut.pos - node.nodeSize)).scrollIntoView());\n    return true;\n};\nfunction findCutBefore($pos) {\n    if (!$pos.parent.type.spec.isolating)\n        for (let i = $pos.depth - 1; i >= 0; i--) {\n            if ($pos.index(i) > 0)\n                return $pos.doc.resolve($pos.before(i + 1));\n            if ($pos.node(i).type.spec.isolating)\n                break;\n        }\n    return null;\n}\nfunction atBlockEnd(state, view) {\n    let { $cursor } = state.selection;\n    if (!$cursor || (view ? !view.endOfTextblock(\"forward\", state)\n        : $cursor.parentOffset < $cursor.parent.content.size))\n        return null;\n    return $cursor;\n}\n/**\nIf the selection is empty and the cursor is at the end of a\ntextblock, try to reduce or remove the boundary between that block\nand the one after it, either by joining them or by moving the other\nblock closer to this one in the tree structure. Will use the view\nfor accurate start-of-textblock detection if given.\n*/\nconst joinForward = (state, dispatch, view) => {\n    let $cursor = atBlockEnd(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutAfter($cursor);\n    // If there is no node after this, there's nothing to do\n    if (!$cut)\n        return false;\n    let after = $cut.nodeAfter;\n    // Try the joining algorithm\n    if (deleteBarrier(state, $cut, dispatch, 1))\n        return true;\n    // If the node above has no content and the node below is\n    // selectable, delete the node above and select the one below.\n    if ($cursor.parent.content.size == 0 &&\n        (textblockAt(after, \"start\") || prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.isSelectable(after))) {\n        let delStep = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.replaceStep)(state.doc, $cursor.before(), $cursor.after(), prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice.empty);\n        if (delStep && delStep.slice.size < delStep.to - delStep.from) {\n            if (dispatch) {\n                let tr = state.tr.step(delStep);\n                tr.setSelection(textblockAt(after, \"start\") ? prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.Selection.findFrom(tr.doc.resolve(tr.mapping.map($cut.pos)), 1)\n                    : prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(tr.doc, tr.mapping.map($cut.pos)));\n                dispatch(tr.scrollIntoView());\n            }\n            return true;\n        }\n    }\n    // If the next node is an atom, delete it\n    if (after.isAtom && $cut.depth == $cursor.depth - 1) {\n        if (dispatch)\n            dispatch(state.tr.delete($cut.pos, $cut.pos + after.nodeSize).scrollIntoView());\n        return true;\n    }\n    return false;\n};\n/**\nWhen the selection is empty and at the end of a textblock, select\nthe node coming after that textblock, if possible. This is intended\nto be bound to keys like delete, after\n[`joinForward`](https://prosemirror.net/docs/ref/#commands.joinForward) and similar deleting\ncommands, to provide a fall-back behavior when the schema doesn't\nallow deletion at the selected point.\n*/\nconst selectNodeForward = (state, dispatch, view) => {\n    let { $head, empty } = state.selection, $cut = $head;\n    if (!empty)\n        return false;\n    if ($head.parent.isTextblock) {\n        if (view ? !view.endOfTextblock(\"forward\", state) : $head.parentOffset < $head.parent.content.size)\n            return false;\n        $cut = findCutAfter($head);\n    }\n    let node = $cut && $cut.nodeAfter;\n    if (!node || !prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.isSelectable(node))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(state.doc, $cut.pos)).scrollIntoView());\n    return true;\n};\nfunction findCutAfter($pos) {\n    if (!$pos.parent.type.spec.isolating)\n        for (let i = $pos.depth - 1; i >= 0; i--) {\n            let parent = $pos.node(i);\n            if ($pos.index(i) + 1 < parent.childCount)\n                return $pos.doc.resolve($pos.after(i + 1));\n            if (parent.type.spec.isolating)\n                break;\n        }\n    return null;\n}\n/**\nJoin the selected block or, if there is a text selection, the\nclosest ancestor block of the selection that can be joined, with\nthe sibling above it.\n*/\nconst joinUp = (state, dispatch) => {\n    let sel = state.selection, nodeSel = sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection, point;\n    if (nodeSel) {\n        if (sel.node.isTextblock || !(0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(state.doc, sel.from))\n            return false;\n        point = sel.from;\n    }\n    else {\n        point = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.joinPoint)(state.doc, sel.from, -1);\n        if (point == null)\n            return false;\n    }\n    if (dispatch) {\n        let tr = state.tr.join(point);\n        if (nodeSel)\n            tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(tr.doc, point - state.doc.resolve(point).nodeBefore.nodeSize));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n};\n/**\nJoin the selected block, or the closest ancestor of the selection\nthat can be joined, with the sibling after it.\n*/\nconst joinDown = (state, dispatch) => {\n    let sel = state.selection, point;\n    if (sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection) {\n        if (sel.node.isTextblock || !(0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(state.doc, sel.to))\n            return false;\n        point = sel.to;\n    }\n    else {\n        point = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.joinPoint)(state.doc, sel.to, 1);\n        if (point == null)\n            return false;\n    }\n    if (dispatch)\n        dispatch(state.tr.join(point).scrollIntoView());\n    return true;\n};\n/**\nLift the selected block, or the closest ancestor block of the\nselection that can be lifted, out of its parent node.\n*/\nconst lift = (state, dispatch) => {\n    let { $from, $to } = state.selection;\n    let range = $from.blockRange($to), target = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.liftTarget)(range);\n    if (target == null)\n        return false;\n    if (dispatch)\n        dispatch(state.tr.lift(range, target).scrollIntoView());\n    return true;\n};\n/**\nIf the selection is in a node whose type has a truthy\n[`code`](https://prosemirror.net/docs/ref/#model.NodeSpec.code) property in its spec, replace the\nselection with a newline character.\n*/\nconst newlineInCode = (state, dispatch) => {\n    let { $head, $anchor } = state.selection;\n    if (!$head.parent.type.spec.code || !$head.sameParent($anchor))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.insertText(\"\\n\").scrollIntoView());\n    return true;\n};\nfunction defaultBlockAt(match) {\n    for (let i = 0; i < match.edgeCount; i++) {\n        let { type } = match.edge(i);\n        if (type.isTextblock && !type.hasRequiredAttrs())\n            return type;\n    }\n    return null;\n}\n/**\nWhen the selection is in a node with a truthy\n[`code`](https://prosemirror.net/docs/ref/#model.NodeSpec.code) property in its spec, create a\ndefault block after the code block, and move the cursor there.\n*/\nconst exitCode = (state, dispatch) => {\n    let { $head, $anchor } = state.selection;\n    if (!$head.parent.type.spec.code || !$head.sameParent($anchor))\n        return false;\n    let above = $head.node(-1), after = $head.indexAfter(-1), type = defaultBlockAt(above.contentMatchAt(after));\n    if (!type || !above.canReplaceWith(after, after, type))\n        return false;\n    if (dispatch) {\n        let pos = $head.after(), tr = state.tr.replaceWith(pos, pos, type.createAndFill());\n        tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.Selection.near(tr.doc.resolve(pos), 1));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n};\n/**\nIf a block node is selected, create an empty paragraph before (if\nit is its parent's first child) or after it.\n*/\nconst createParagraphNear = (state, dispatch) => {\n    let sel = state.selection, { $from, $to } = sel;\n    if (sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.AllSelection || $from.parent.inlineContent || $to.parent.inlineContent)\n        return false;\n    let type = defaultBlockAt($to.parent.contentMatchAt($to.indexAfter()));\n    if (!type || !type.isTextblock)\n        return false;\n    if (dispatch) {\n        let side = (!$from.parentOffset && $to.index() < $to.parent.childCount ? $from : $to).pos;\n        let tr = state.tr.insert(side, type.createAndFill());\n        tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.TextSelection.create(tr.doc, side + 1));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n};\n/**\nIf the cursor is in an empty textblock that can be lifted, lift the\nblock.\n*/\nconst liftEmptyBlock = (state, dispatch) => {\n    let { $cursor } = state.selection;\n    if (!$cursor || $cursor.parent.content.size)\n        return false;\n    if ($cursor.depth > 1 && $cursor.after() != $cursor.end(-1)) {\n        let before = $cursor.before();\n        if ((0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canSplit)(state.doc, before)) {\n            if (dispatch)\n                dispatch(state.tr.split(before).scrollIntoView());\n            return true;\n        }\n    }\n    let range = $cursor.blockRange(), target = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.liftTarget)(range);\n    if (target == null)\n        return false;\n    if (dispatch)\n        dispatch(state.tr.lift(range, target).scrollIntoView());\n    return true;\n};\n/**\nCreate a variant of [`splitBlock`](https://prosemirror.net/docs/ref/#commands.splitBlock) that uses\na custom function to determine the type of the newly split off block.\n*/\nfunction splitBlockAs(splitNode) {\n    return (state, dispatch) => {\n        let { $from, $to } = state.selection;\n        if (state.selection instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection && state.selection.node.isBlock) {\n            if (!$from.parentOffset || !(0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canSplit)(state.doc, $from.pos))\n                return false;\n            if (dispatch)\n                dispatch(state.tr.split($from.pos).scrollIntoView());\n            return true;\n        }\n        if (!$from.depth)\n            return false;\n        let types = [];\n        let splitDepth, deflt, atEnd = false, atStart = false;\n        for (let d = $from.depth;; d--) {\n            let node = $from.node(d);\n            if (node.isBlock) {\n                atEnd = $from.end(d) == $from.pos + ($from.depth - d);\n                atStart = $from.start(d) == $from.pos - ($from.depth - d);\n                deflt = defaultBlockAt($from.node(d - 1).contentMatchAt($from.indexAfter(d - 1)));\n                let splitType = splitNode && splitNode($to.parent, atEnd, $from);\n                types.unshift(splitType || (atEnd && deflt ? { type: deflt } : null));\n                splitDepth = d;\n                break;\n            }\n            else {\n                if (d == 1)\n                    return false;\n                types.unshift(null);\n            }\n        }\n        let tr = state.tr;\n        if (state.selection instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.TextSelection || state.selection instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.AllSelection)\n            tr.deleteSelection();\n        let splitPos = tr.mapping.map($from.pos);\n        let can = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canSplit)(tr.doc, splitPos, types.length, types);\n        if (!can) {\n            types[0] = deflt ? { type: deflt } : null;\n            can = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canSplit)(tr.doc, splitPos, types.length, types);\n        }\n        tr.split(splitPos, types.length, types);\n        if (!atEnd && atStart && $from.node(splitDepth).type != deflt) {\n            let first = tr.mapping.map($from.before(splitDepth)), $first = tr.doc.resolve(first);\n            if (deflt && $from.node(splitDepth - 1).canReplaceWith($first.index(), $first.index() + 1, deflt))\n                tr.setNodeMarkup(tr.mapping.map($from.before(splitDepth)), deflt);\n        }\n        if (dispatch)\n            dispatch(tr.scrollIntoView());\n        return true;\n    };\n}\n/**\nSplit the parent block of the selection. If the selection is a text\nselection, also delete its content.\n*/\nconst splitBlock = splitBlockAs();\n/**\nActs like [`splitBlock`](https://prosemirror.net/docs/ref/#commands.splitBlock), but without\nresetting the set of active marks at the cursor.\n*/\nconst splitBlockKeepMarks = (state, dispatch) => {\n    return splitBlock(state, dispatch && (tr => {\n        let marks = state.storedMarks || (state.selection.$to.parentOffset && state.selection.$from.marks());\n        if (marks)\n            tr.ensureMarks(marks);\n        dispatch(tr);\n    }));\n};\n/**\nMove the selection to the node wrapping the current selection, if\nany. (Will not select the document node.)\n*/\nconst selectParentNode = (state, dispatch) => {\n    let { $from, to } = state.selection, pos;\n    let same = $from.sharedDepth(to);\n    if (same == 0)\n        return false;\n    pos = $from.before(same);\n    if (dispatch)\n        dispatch(state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(state.doc, pos)));\n    return true;\n};\n/**\nSelect the whole document.\n*/\nconst selectAll = (state, dispatch) => {\n    if (dispatch)\n        dispatch(state.tr.setSelection(new prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.AllSelection(state.doc)));\n    return true;\n};\nfunction joinMaybeClear(state, $pos, dispatch) {\n    let before = $pos.nodeBefore, after = $pos.nodeAfter, index = $pos.index();\n    if (!before || !after || !before.type.compatibleContent(after.type))\n        return false;\n    if (!before.content.size && $pos.parent.canReplace(index - 1, index)) {\n        if (dispatch)\n            dispatch(state.tr.delete($pos.pos - before.nodeSize, $pos.pos).scrollIntoView());\n        return true;\n    }\n    if (!$pos.parent.canReplace(index, index + 1) || !(after.isTextblock || (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(state.doc, $pos.pos)))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.join($pos.pos).scrollIntoView());\n    return true;\n}\nfunction deleteBarrier(state, $cut, dispatch, dir) {\n    let before = $cut.nodeBefore, after = $cut.nodeAfter, conn, match;\n    let isolated = before.type.spec.isolating || after.type.spec.isolating;\n    if (!isolated && joinMaybeClear(state, $cut, dispatch))\n        return true;\n    let canDelAfter = !isolated && $cut.parent.canReplace($cut.index(), $cut.index() + 1);\n    if (canDelAfter &&\n        (conn = (match = before.contentMatchAt(before.childCount)).findWrapping(after.type)) &&\n        match.matchType(conn[0] || after.type).validEnd) {\n        if (dispatch) {\n            let end = $cut.pos + after.nodeSize, wrap = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.empty;\n            for (let i = conn.length - 1; i >= 0; i--)\n                wrap = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.from(conn[i].create(null, wrap));\n            wrap = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.from(before.copy(wrap));\n            let tr = state.tr.step(new prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.ReplaceAroundStep($cut.pos - 1, end, $cut.pos, end, new prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice(wrap, 1, 0), conn.length, true));\n            let $joinAt = tr.doc.resolve(end + 2 * conn.length);\n            if ($joinAt.nodeAfter && $joinAt.nodeAfter.type == before.type &&\n                (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(tr.doc, $joinAt.pos))\n                tr.join($joinAt.pos);\n            dispatch(tr.scrollIntoView());\n        }\n        return true;\n    }\n    let selAfter = after.type.spec.isolating || (dir > 0 && isolated) ? null : prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.Selection.findFrom($cut, 1);\n    let range = selAfter && selAfter.$from.blockRange(selAfter.$to), target = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.liftTarget)(range);\n    if (target != null && target >= $cut.depth) {\n        if (dispatch)\n            dispatch(state.tr.lift(range, target).scrollIntoView());\n        return true;\n    }\n    if (canDelAfter && textblockAt(after, \"start\", true) && textblockAt(before, \"end\")) {\n        let at = before, wrap = [];\n        for (;;) {\n            wrap.push(at);\n            if (at.isTextblock)\n                break;\n            at = at.lastChild;\n        }\n        let afterText = after, afterDepth = 1;\n        for (; !afterText.isTextblock; afterText = afterText.firstChild)\n            afterDepth++;\n        if (at.canReplace(at.childCount, at.childCount, afterText.content)) {\n            if (dispatch) {\n                let end = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.empty;\n                for (let i = wrap.length - 1; i >= 0; i--)\n                    end = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.from(wrap[i].copy(end));\n                let tr = state.tr.step(new prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.ReplaceAroundStep($cut.pos - wrap.length, $cut.pos + after.nodeSize, $cut.pos + afterDepth, $cut.pos + after.nodeSize - afterDepth, new prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice(end, wrap.length, 0), 0, true));\n                dispatch(tr.scrollIntoView());\n            }\n            return true;\n        }\n    }\n    return false;\n}\nfunction selectTextblockSide(side) {\n    return function (state, dispatch) {\n        let sel = state.selection, $pos = side < 0 ? sel.$from : sel.$to;\n        let depth = $pos.depth;\n        while ($pos.node(depth).isInline) {\n            if (!depth)\n                return false;\n            depth--;\n        }\n        if (!$pos.node(depth).isTextblock)\n            return false;\n        if (dispatch)\n            dispatch(state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.TextSelection.create(state.doc, side < 0 ? $pos.start(depth) : $pos.end(depth))));\n        return true;\n    };\n}\n/**\nMoves the cursor to the start of current text block.\n*/\nconst selectTextblockStart = selectTextblockSide(-1);\n/**\nMoves the cursor to the end of current text block.\n*/\nconst selectTextblockEnd = selectTextblockSide(1);\n// Parameterized commands\n/**\nWrap the selection in a node of the given type with the given\nattributes.\n*/\nfunction wrapIn(nodeType, attrs = null) {\n    return function (state, dispatch) {\n        let { $from, $to } = state.selection;\n        let range = $from.blockRange($to), wrapping = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.findWrapping)(range, nodeType, attrs);\n        if (!wrapping)\n            return false;\n        if (dispatch)\n            dispatch(state.tr.wrap(range, wrapping).scrollIntoView());\n        return true;\n    };\n}\n/**\nReturns a command that tries to set the selected textblocks to the\ngiven node type with the given attributes.\n*/\nfunction setBlockType(nodeType, attrs = null) {\n    return function (state, dispatch) {\n        let applicable = false;\n        for (let i = 0; i < state.selection.ranges.length && !applicable; i++) {\n            let { $from: { pos: from }, $to: { pos: to } } = state.selection.ranges[i];\n            state.doc.nodesBetween(from, to, (node, pos) => {\n                if (applicable)\n                    return false;\n                if (!node.isTextblock || node.hasMarkup(nodeType, attrs))\n                    return;\n                if (node.type == nodeType) {\n                    applicable = true;\n                }\n                else {\n                    let $pos = state.doc.resolve(pos), index = $pos.index();\n                    applicable = $pos.parent.canReplaceWith(index, index + 1, nodeType);\n                }\n            });\n        }\n        if (!applicable)\n            return false;\n        if (dispatch) {\n            let tr = state.tr;\n            for (let i = 0; i < state.selection.ranges.length; i++) {\n                let { $from: { pos: from }, $to: { pos: to } } = state.selection.ranges[i];\n                tr.setBlockType(from, to, nodeType, attrs);\n            }\n            dispatch(tr.scrollIntoView());\n        }\n        return true;\n    };\n}\nfunction markApplies(doc, ranges, type, enterAtoms) {\n    for (let i = 0; i < ranges.length; i++) {\n        let { $from, $to } = ranges[i];\n        let can = $from.depth == 0 ? doc.inlineContent && doc.type.allowsMarkType(type) : false;\n        doc.nodesBetween($from.pos, $to.pos, (node, pos) => {\n            if (can || !enterAtoms && node.isAtom && node.isInline && pos >= $from.pos && pos + node.nodeSize <= $to.pos)\n                return false;\n            can = node.inlineContent && node.type.allowsMarkType(type);\n        });\n        if (can)\n            return true;\n    }\n    return false;\n}\nfunction removeInlineAtoms(ranges) {\n    let result = [];\n    for (let i = 0; i < ranges.length; i++) {\n        let { $from, $to } = ranges[i];\n        $from.doc.nodesBetween($from.pos, $to.pos, (node, pos) => {\n            if (node.isAtom && node.content.size && node.isInline && pos >= $from.pos && pos + node.nodeSize <= $to.pos) {\n                if (pos + 1 > $from.pos)\n                    result.push(new prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.SelectionRange($from, $from.doc.resolve(pos + 1)));\n                $from = $from.doc.resolve(pos + 1 + node.content.size);\n                return false;\n            }\n        });\n        if ($from.pos < $to.pos)\n            result.push(new prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.SelectionRange($from, $to));\n    }\n    return result;\n}\n/**\nCreate a command function that toggles the given mark with the\ngiven attributes. Will return `false` when the current selection\ndoesn't support that mark. This will remove the mark if any marks\nof that type exist in the selection, or add it otherwise. If the\nselection is empty, this applies to the [stored\nmarks](https://prosemirror.net/docs/ref/#state.EditorState.storedMarks) instead of a range of the\ndocument.\n*/\nfunction toggleMark(markType, attrs = null, options) {\n    let removeWhenPresent = (options && options.removeWhenPresent) !== false;\n    let enterAtoms = (options && options.enterInlineAtoms) !== false;\n    return function (state, dispatch) {\n        let { empty, $cursor, ranges } = state.selection;\n        if ((empty && !$cursor) || !markApplies(state.doc, ranges, markType, enterAtoms))\n            return false;\n        if (dispatch) {\n            if ($cursor) {\n                if (markType.isInSet(state.storedMarks || $cursor.marks()))\n                    dispatch(state.tr.removeStoredMark(markType));\n                else\n                    dispatch(state.tr.addStoredMark(markType.create(attrs)));\n            }\n            else {\n                let add, tr = state.tr;\n                if (!enterAtoms)\n                    ranges = removeInlineAtoms(ranges);\n                if (removeWhenPresent) {\n                    add = !ranges.some(r => state.doc.rangeHasMark(r.$from.pos, r.$to.pos, markType));\n                }\n                else {\n                    add = !ranges.every(r => {\n                        let missing = false;\n                        tr.doc.nodesBetween(r.$from.pos, r.$to.pos, (node, pos, parent) => {\n                            if (missing)\n                                return false;\n                            missing = !markType.isInSet(node.marks) && !!parent && parent.type.allowsMarkType(markType) &&\n                                !(node.isText && /^\\s*$/.test(node.textBetween(Math.max(0, r.$from.pos - pos), Math.min(node.nodeSize, r.$to.pos - pos))));\n                        });\n                        return !missing;\n                    });\n                }\n                for (let i = 0; i < ranges.length; i++) {\n                    let { $from, $to } = ranges[i];\n                    if (!add) {\n                        tr.removeMark($from.pos, $to.pos, markType);\n                    }\n                    else {\n                        let from = $from.pos, to = $to.pos, start = $from.nodeAfter, end = $to.nodeBefore;\n                        let spaceStart = start && start.isText ? /^\\s*/.exec(start.text)[0].length : 0;\n                        let spaceEnd = end && end.isText ? /\\s*$/.exec(end.text)[0].length : 0;\n                        if (from + spaceStart < to) {\n                            from += spaceStart;\n                            to -= spaceEnd;\n                        }\n                        tr.addMark(from, to, markType.create(attrs));\n                    }\n                }\n                dispatch(tr.scrollIntoView());\n            }\n        }\n        return true;\n    };\n}\nfunction wrapDispatchForJoin(dispatch, isJoinable) {\n    return (tr) => {\n        if (!tr.isGeneric)\n            return dispatch(tr);\n        let ranges = [];\n        for (let i = 0; i < tr.mapping.maps.length; i++) {\n            let map = tr.mapping.maps[i];\n            for (let j = 0; j < ranges.length; j++)\n                ranges[j] = map.map(ranges[j]);\n            map.forEach((_s, _e, from, to) => ranges.push(from, to));\n        }\n        // Figure out which joinable points exist inside those ranges,\n        // by checking all node boundaries in their parent nodes.\n        let joinable = [];\n        for (let i = 0; i < ranges.length; i += 2) {\n            let from = ranges[i], to = ranges[i + 1];\n            let $from = tr.doc.resolve(from), depth = $from.sharedDepth(to), parent = $from.node(depth);\n            for (let index = $from.indexAfter(depth), pos = $from.after(depth + 1); pos <= to; ++index) {\n                let after = parent.maybeChild(index);\n                if (!after)\n                    break;\n                if (index && joinable.indexOf(pos) == -1) {\n                    let before = parent.child(index - 1);\n                    if (before.type == after.type && isJoinable(before, after))\n                        joinable.push(pos);\n                }\n                pos += after.nodeSize;\n            }\n        }\n        // Join the joinable points\n        joinable.sort((a, b) => a - b);\n        for (let i = joinable.length - 1; i >= 0; i--) {\n            if ((0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(tr.doc, joinable[i]))\n                tr.join(joinable[i]);\n        }\n        dispatch(tr);\n    };\n}\n/**\nWrap a command so that, when it produces a transform that causes\ntwo joinable nodes to end up next to each other, those are joined.\nNodes are considered joinable when they are of the same type and\nwhen the `isJoinable` predicate returns true for them or, if an\narray of strings was passed, if their node type name is in that\narray.\n*/\nfunction autoJoin(command, isJoinable) {\n    let canJoin = Array.isArray(isJoinable) ? (node) => isJoinable.indexOf(node.type.name) > -1\n        : isJoinable;\n    return (state, dispatch, view) => command(state, dispatch && wrapDispatchForJoin(dispatch, canJoin), view);\n}\n/**\nCombine a number of command functions into a single function (which\ncalls them one by one until one returns true).\n*/\nfunction chainCommands(...commands) {\n    return function (state, dispatch, view) {\n        for (let i = 0; i < commands.length; i++)\n            if (commands[i](state, dispatch, view))\n                return true;\n        return false;\n    };\n}\nlet backspace = chainCommands(deleteSelection, joinBackward, selectNodeBackward);\nlet del = chainCommands(deleteSelection, joinForward, selectNodeForward);\n/**\nA basic keymap containing bindings not specific to any schema.\nBinds the following keys (when multiple commands are listed, they\nare chained with [`chainCommands`](https://prosemirror.net/docs/ref/#commands.chainCommands)):\n\n* **Enter** to `newlineInCode`, `createParagraphNear`, `liftEmptyBlock`, `splitBlock`\n* **Mod-Enter** to `exitCode`\n* **Backspace** and **Mod-Backspace** to `deleteSelection`, `joinBackward`, `selectNodeBackward`\n* **Delete** and **Mod-Delete** to `deleteSelection`, `joinForward`, `selectNodeForward`\n* **Mod-Delete** to `deleteSelection`, `joinForward`, `selectNodeForward`\n* **Mod-a** to `selectAll`\n*/\nconst pcBaseKeymap = {\n    \"Enter\": chainCommands(newlineInCode, createParagraphNear, liftEmptyBlock, splitBlock),\n    \"Mod-Enter\": exitCode,\n    \"Backspace\": backspace,\n    \"Mod-Backspace\": backspace,\n    \"Shift-Backspace\": backspace,\n    \"Delete\": del,\n    \"Mod-Delete\": del,\n    \"Mod-a\": selectAll\n};\n/**\nA copy of `pcBaseKeymap` that also binds **Ctrl-h** like Backspace,\n**Ctrl-d** like Delete, **Alt-Backspace** like Ctrl-Backspace, and\n**Ctrl-Alt-Backspace**, **Alt-Delete**, and **Alt-d** like\nCtrl-Delete.\n*/\nconst macBaseKeymap = {\n    \"Ctrl-h\": pcBaseKeymap[\"Backspace\"],\n    \"Alt-Backspace\": pcBaseKeymap[\"Mod-Backspace\"],\n    \"Ctrl-d\": pcBaseKeymap[\"Delete\"],\n    \"Ctrl-Alt-Backspace\": pcBaseKeymap[\"Mod-Delete\"],\n    \"Alt-Delete\": pcBaseKeymap[\"Mod-Delete\"],\n    \"Alt-d\": pcBaseKeymap[\"Mod-Delete\"],\n    \"Ctrl-a\": selectTextblockStart,\n    \"Ctrl-e\": selectTextblockEnd\n};\nfor (let key in pcBaseKeymap)\n    macBaseKeymap[key] = pcBaseKeymap[key];\nconst mac = typeof navigator != \"undefined\" ? /Mac|iP(hone|[oa]d)/.test(navigator.platform)\n    // @ts-ignore\n    : typeof os != \"undefined\" && os.platform ? os.platform() == \"darwin\" : false;\n/**\nDepending on the detected platform, this will hold\n[`pcBasekeymap`](https://prosemirror.net/docs/ref/#commands.pcBaseKeymap) or\n[`macBaseKeymap`](https://prosemirror.net/docs/ref/#commands.macBaseKeymap).\n*/\nconst baseKeymap = mac ? macBaseKeymap : pcBaseKeymap;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/prosemirror-commands/dist/index.js\n");

/***/ })

};
;