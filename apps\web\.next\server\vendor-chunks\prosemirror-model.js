"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-model";
exports.ids = ["vendor-chunks/prosemirror-model"];
exports.modules = {

/***/ "(ssr)/../../node_modules/prosemirror-model/dist/index.js":
/*!**********************************************************!*\
  !*** ../../node_modules/prosemirror-model/dist/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContentMatch: () => (/* binding */ ContentMatch),\n/* harmony export */   DOMParser: () => (/* binding */ DOMParser),\n/* harmony export */   DOMSerializer: () => (/* binding */ DOMSerializer),\n/* harmony export */   Fragment: () => (/* binding */ Fragment),\n/* harmony export */   Mark: () => (/* binding */ Mark),\n/* harmony export */   MarkType: () => (/* binding */ MarkType),\n/* harmony export */   Node: () => (/* binding */ Node),\n/* harmony export */   NodeRange: () => (/* binding */ NodeRange),\n/* harmony export */   NodeType: () => (/* binding */ NodeType),\n/* harmony export */   ReplaceError: () => (/* binding */ ReplaceError),\n/* harmony export */   ResolvedPos: () => (/* binding */ ResolvedPos),\n/* harmony export */   Schema: () => (/* binding */ Schema),\n/* harmony export */   Slice: () => (/* binding */ Slice)\n/* harmony export */ });\n/* harmony import */ var orderedmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! orderedmap */ \"(ssr)/../../node_modules/orderedmap/dist/index.js\");\n\n\nfunction findDiffStart(a, b, pos) {\n    for (let i = 0;; i++) {\n        if (i == a.childCount || i == b.childCount)\n            return a.childCount == b.childCount ? null : pos;\n        let childA = a.child(i), childB = b.child(i);\n        if (childA == childB) {\n            pos += childA.nodeSize;\n            continue;\n        }\n        if (!childA.sameMarkup(childB))\n            return pos;\n        if (childA.isText && childA.text != childB.text) {\n            for (let j = 0; childA.text[j] == childB.text[j]; j++)\n                pos++;\n            return pos;\n        }\n        if (childA.content.size || childB.content.size) {\n            let inner = findDiffStart(childA.content, childB.content, pos + 1);\n            if (inner != null)\n                return inner;\n        }\n        pos += childA.nodeSize;\n    }\n}\nfunction findDiffEnd(a, b, posA, posB) {\n    for (let iA = a.childCount, iB = b.childCount;;) {\n        if (iA == 0 || iB == 0)\n            return iA == iB ? null : { a: posA, b: posB };\n        let childA = a.child(--iA), childB = b.child(--iB), size = childA.nodeSize;\n        if (childA == childB) {\n            posA -= size;\n            posB -= size;\n            continue;\n        }\n        if (!childA.sameMarkup(childB))\n            return { a: posA, b: posB };\n        if (childA.isText && childA.text != childB.text) {\n            let same = 0, minSize = Math.min(childA.text.length, childB.text.length);\n            while (same < minSize && childA.text[childA.text.length - same - 1] == childB.text[childB.text.length - same - 1]) {\n                same++;\n                posA--;\n                posB--;\n            }\n            return { a: posA, b: posB };\n        }\n        if (childA.content.size || childB.content.size) {\n            let inner = findDiffEnd(childA.content, childB.content, posA - 1, posB - 1);\n            if (inner)\n                return inner;\n        }\n        posA -= size;\n        posB -= size;\n    }\n}\n\n/**\nA fragment represents a node's collection of child nodes.\n\nLike nodes, fragments are persistent data structures, and you\nshould not mutate them or their content. Rather, you create new\ninstances whenever needed. The API tries to make this easy.\n*/\nclass Fragment {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The child nodes in this fragment.\n    */\n    content, size) {\n        this.content = content;\n        this.size = size || 0;\n        if (size == null)\n            for (let i = 0; i < content.length; i++)\n                this.size += content[i].nodeSize;\n    }\n    /**\n    Invoke a callback for all descendant nodes between the given two\n    positions (relative to start of this fragment). Doesn't descend\n    into a node when the callback returns `false`.\n    */\n    nodesBetween(from, to, f, nodeStart = 0, parent) {\n        for (let i = 0, pos = 0; pos < to; i++) {\n            let child = this.content[i], end = pos + child.nodeSize;\n            if (end > from && f(child, nodeStart + pos, parent || null, i) !== false && child.content.size) {\n                let start = pos + 1;\n                child.nodesBetween(Math.max(0, from - start), Math.min(child.content.size, to - start), f, nodeStart + start);\n            }\n            pos = end;\n        }\n    }\n    /**\n    Call the given callback for every descendant node. `pos` will be\n    relative to the start of the fragment. The callback may return\n    `false` to prevent traversal of a given node's children.\n    */\n    descendants(f) {\n        this.nodesBetween(0, this.size, f);\n    }\n    /**\n    Extract the text between `from` and `to`. See the same method on\n    [`Node`](https://prosemirror.net/docs/ref/#model.Node.textBetween).\n    */\n    textBetween(from, to, blockSeparator, leafText) {\n        let text = \"\", first = true;\n        this.nodesBetween(from, to, (node, pos) => {\n            let nodeText = node.isText ? node.text.slice(Math.max(from, pos) - pos, to - pos)\n                : !node.isLeaf ? \"\"\n                    : leafText ? (typeof leafText === \"function\" ? leafText(node) : leafText)\n                        : node.type.spec.leafText ? node.type.spec.leafText(node)\n                            : \"\";\n            if (node.isBlock && (node.isLeaf && nodeText || node.isTextblock) && blockSeparator) {\n                if (first)\n                    first = false;\n                else\n                    text += blockSeparator;\n            }\n            text += nodeText;\n        }, 0);\n        return text;\n    }\n    /**\n    Create a new fragment containing the combined content of this\n    fragment and the other.\n    */\n    append(other) {\n        if (!other.size)\n            return this;\n        if (!this.size)\n            return other;\n        let last = this.lastChild, first = other.firstChild, content = this.content.slice(), i = 0;\n        if (last.isText && last.sameMarkup(first)) {\n            content[content.length - 1] = last.withText(last.text + first.text);\n            i = 1;\n        }\n        for (; i < other.content.length; i++)\n            content.push(other.content[i]);\n        return new Fragment(content, this.size + other.size);\n    }\n    /**\n    Cut out the sub-fragment between the two given positions.\n    */\n    cut(from, to = this.size) {\n        if (from == 0 && to == this.size)\n            return this;\n        let result = [], size = 0;\n        if (to > from)\n            for (let i = 0, pos = 0; pos < to; i++) {\n                let child = this.content[i], end = pos + child.nodeSize;\n                if (end > from) {\n                    if (pos < from || end > to) {\n                        if (child.isText)\n                            child = child.cut(Math.max(0, from - pos), Math.min(child.text.length, to - pos));\n                        else\n                            child = child.cut(Math.max(0, from - pos - 1), Math.min(child.content.size, to - pos - 1));\n                    }\n                    result.push(child);\n                    size += child.nodeSize;\n                }\n                pos = end;\n            }\n        return new Fragment(result, size);\n    }\n    /**\n    @internal\n    */\n    cutByIndex(from, to) {\n        if (from == to)\n            return Fragment.empty;\n        if (from == 0 && to == this.content.length)\n            return this;\n        return new Fragment(this.content.slice(from, to));\n    }\n    /**\n    Create a new fragment in which the node at the given index is\n    replaced by the given node.\n    */\n    replaceChild(index, node) {\n        let current = this.content[index];\n        if (current == node)\n            return this;\n        let copy = this.content.slice();\n        let size = this.size + node.nodeSize - current.nodeSize;\n        copy[index] = node;\n        return new Fragment(copy, size);\n    }\n    /**\n    Create a new fragment by prepending the given node to this\n    fragment.\n    */\n    addToStart(node) {\n        return new Fragment([node].concat(this.content), this.size + node.nodeSize);\n    }\n    /**\n    Create a new fragment by appending the given node to this\n    fragment.\n    */\n    addToEnd(node) {\n        return new Fragment(this.content.concat(node), this.size + node.nodeSize);\n    }\n    /**\n    Compare this fragment to another one.\n    */\n    eq(other) {\n        if (this.content.length != other.content.length)\n            return false;\n        for (let i = 0; i < this.content.length; i++)\n            if (!this.content[i].eq(other.content[i]))\n                return false;\n        return true;\n    }\n    /**\n    The first child of the fragment, or `null` if it is empty.\n    */\n    get firstChild() { return this.content.length ? this.content[0] : null; }\n    /**\n    The last child of the fragment, or `null` if it is empty.\n    */\n    get lastChild() { return this.content.length ? this.content[this.content.length - 1] : null; }\n    /**\n    The number of child nodes in this fragment.\n    */\n    get childCount() { return this.content.length; }\n    /**\n    Get the child node at the given index. Raise an error when the\n    index is out of range.\n    */\n    child(index) {\n        let found = this.content[index];\n        if (!found)\n            throw new RangeError(\"Index \" + index + \" out of range for \" + this);\n        return found;\n    }\n    /**\n    Get the child node at the given index, if it exists.\n    */\n    maybeChild(index) {\n        return this.content[index] || null;\n    }\n    /**\n    Call `f` for every child node, passing the node, its offset\n    into this parent node, and its index.\n    */\n    forEach(f) {\n        for (let i = 0, p = 0; i < this.content.length; i++) {\n            let child = this.content[i];\n            f(child, p, i);\n            p += child.nodeSize;\n        }\n    }\n    /**\n    Find the first position at which this fragment and another\n    fragment differ, or `null` if they are the same.\n    */\n    findDiffStart(other, pos = 0) {\n        return findDiffStart(this, other, pos);\n    }\n    /**\n    Find the first position, searching from the end, at which this\n    fragment and the given fragment differ, or `null` if they are\n    the same. Since this position will not be the same in both\n    nodes, an object with two separate positions is returned.\n    */\n    findDiffEnd(other, pos = this.size, otherPos = other.size) {\n        return findDiffEnd(this, other, pos, otherPos);\n    }\n    /**\n    Find the index and inner offset corresponding to a given relative\n    position in this fragment. The result object will be reused\n    (overwritten) the next time the function is called. @internal\n    */\n    findIndex(pos, round = -1) {\n        if (pos == 0)\n            return retIndex(0, pos);\n        if (pos == this.size)\n            return retIndex(this.content.length, pos);\n        if (pos > this.size || pos < 0)\n            throw new RangeError(`Position ${pos} outside of fragment (${this})`);\n        for (let i = 0, curPos = 0;; i++) {\n            let cur = this.child(i), end = curPos + cur.nodeSize;\n            if (end >= pos) {\n                if (end == pos || round > 0)\n                    return retIndex(i + 1, end);\n                return retIndex(i, curPos);\n            }\n            curPos = end;\n        }\n    }\n    /**\n    Return a debugging string that describes this fragment.\n    */\n    toString() { return \"<\" + this.toStringInner() + \">\"; }\n    /**\n    @internal\n    */\n    toStringInner() { return this.content.join(\", \"); }\n    /**\n    Create a JSON-serializeable representation of this fragment.\n    */\n    toJSON() {\n        return this.content.length ? this.content.map(n => n.toJSON()) : null;\n    }\n    /**\n    Deserialize a fragment from its JSON representation.\n    */\n    static fromJSON(schema, value) {\n        if (!value)\n            return Fragment.empty;\n        if (!Array.isArray(value))\n            throw new RangeError(\"Invalid input for Fragment.fromJSON\");\n        return new Fragment(value.map(schema.nodeFromJSON));\n    }\n    /**\n    Build a fragment from an array of nodes. Ensures that adjacent\n    text nodes with the same marks are joined together.\n    */\n    static fromArray(array) {\n        if (!array.length)\n            return Fragment.empty;\n        let joined, size = 0;\n        for (let i = 0; i < array.length; i++) {\n            let node = array[i];\n            size += node.nodeSize;\n            if (i && node.isText && array[i - 1].sameMarkup(node)) {\n                if (!joined)\n                    joined = array.slice(0, i);\n                joined[joined.length - 1] = node\n                    .withText(joined[joined.length - 1].text + node.text);\n            }\n            else if (joined) {\n                joined.push(node);\n            }\n        }\n        return new Fragment(joined || array, size);\n    }\n    /**\n    Create a fragment from something that can be interpreted as a\n    set of nodes. For `null`, it returns the empty fragment. For a\n    fragment, the fragment itself. For a node or array of nodes, a\n    fragment containing those nodes.\n    */\n    static from(nodes) {\n        if (!nodes)\n            return Fragment.empty;\n        if (nodes instanceof Fragment)\n            return nodes;\n        if (Array.isArray(nodes))\n            return this.fromArray(nodes);\n        if (nodes.attrs)\n            return new Fragment([nodes], nodes.nodeSize);\n        throw new RangeError(\"Can not convert \" + nodes + \" to a Fragment\" +\n            (nodes.nodesBetween ? \" (looks like multiple versions of prosemirror-model were loaded)\" : \"\"));\n    }\n}\n/**\nAn empty fragment. Intended to be reused whenever a node doesn't\ncontain anything (rather than allocating a new empty fragment for\neach leaf node).\n*/\nFragment.empty = new Fragment([], 0);\nconst found = { index: 0, offset: 0 };\nfunction retIndex(index, offset) {\n    found.index = index;\n    found.offset = offset;\n    return found;\n}\n\nfunction compareDeep(a, b) {\n    if (a === b)\n        return true;\n    if (!(a && typeof a == \"object\") ||\n        !(b && typeof b == \"object\"))\n        return false;\n    let array = Array.isArray(a);\n    if (Array.isArray(b) != array)\n        return false;\n    if (array) {\n        if (a.length != b.length)\n            return false;\n        for (let i = 0; i < a.length; i++)\n            if (!compareDeep(a[i], b[i]))\n                return false;\n    }\n    else {\n        for (let p in a)\n            if (!(p in b) || !compareDeep(a[p], b[p]))\n                return false;\n        for (let p in b)\n            if (!(p in a))\n                return false;\n    }\n    return true;\n}\n\n/**\nA mark is a piece of information that can be attached to a node,\nsuch as it being emphasized, in code font, or a link. It has a\ntype and optionally a set of attributes that provide further\ninformation (such as the target of the link). Marks are created\nthrough a `Schema`, which controls which types exist and which\nattributes they have.\n*/\nclass Mark {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The type of this mark.\n    */\n    type, \n    /**\n    The attributes associated with this mark.\n    */\n    attrs) {\n        this.type = type;\n        this.attrs = attrs;\n    }\n    /**\n    Given a set of marks, create a new set which contains this one as\n    well, in the right position. If this mark is already in the set,\n    the set itself is returned. If any marks that are set to be\n    [exclusive](https://prosemirror.net/docs/ref/#model.MarkSpec.excludes) with this mark are present,\n    those are replaced by this one.\n    */\n    addToSet(set) {\n        let copy, placed = false;\n        for (let i = 0; i < set.length; i++) {\n            let other = set[i];\n            if (this.eq(other))\n                return set;\n            if (this.type.excludes(other.type)) {\n                if (!copy)\n                    copy = set.slice(0, i);\n            }\n            else if (other.type.excludes(this.type)) {\n                return set;\n            }\n            else {\n                if (!placed && other.type.rank > this.type.rank) {\n                    if (!copy)\n                        copy = set.slice(0, i);\n                    copy.push(this);\n                    placed = true;\n                }\n                if (copy)\n                    copy.push(other);\n            }\n        }\n        if (!copy)\n            copy = set.slice();\n        if (!placed)\n            copy.push(this);\n        return copy;\n    }\n    /**\n    Remove this mark from the given set, returning a new set. If this\n    mark is not in the set, the set itself is returned.\n    */\n    removeFromSet(set) {\n        for (let i = 0; i < set.length; i++)\n            if (this.eq(set[i]))\n                return set.slice(0, i).concat(set.slice(i + 1));\n        return set;\n    }\n    /**\n    Test whether this mark is in the given set of marks.\n    */\n    isInSet(set) {\n        for (let i = 0; i < set.length; i++)\n            if (this.eq(set[i]))\n                return true;\n        return false;\n    }\n    /**\n    Test whether this mark has the same type and attributes as\n    another mark.\n    */\n    eq(other) {\n        return this == other ||\n            (this.type == other.type && compareDeep(this.attrs, other.attrs));\n    }\n    /**\n    Convert this mark to a JSON-serializeable representation.\n    */\n    toJSON() {\n        let obj = { type: this.type.name };\n        for (let _ in this.attrs) {\n            obj.attrs = this.attrs;\n            break;\n        }\n        return obj;\n    }\n    /**\n    Deserialize a mark from JSON.\n    */\n    static fromJSON(schema, json) {\n        if (!json)\n            throw new RangeError(\"Invalid input for Mark.fromJSON\");\n        let type = schema.marks[json.type];\n        if (!type)\n            throw new RangeError(`There is no mark type ${json.type} in this schema`);\n        let mark = type.create(json.attrs);\n        type.checkAttrs(mark.attrs);\n        return mark;\n    }\n    /**\n    Test whether two sets of marks are identical.\n    */\n    static sameSet(a, b) {\n        if (a == b)\n            return true;\n        if (a.length != b.length)\n            return false;\n        for (let i = 0; i < a.length; i++)\n            if (!a[i].eq(b[i]))\n                return false;\n        return true;\n    }\n    /**\n    Create a properly sorted mark set from null, a single mark, or an\n    unsorted array of marks.\n    */\n    static setFrom(marks) {\n        if (!marks || Array.isArray(marks) && marks.length == 0)\n            return Mark.none;\n        if (marks instanceof Mark)\n            return [marks];\n        let copy = marks.slice();\n        copy.sort((a, b) => a.type.rank - b.type.rank);\n        return copy;\n    }\n}\n/**\nThe empty set of marks.\n*/\nMark.none = [];\n\n/**\nError type raised by [`Node.replace`](https://prosemirror.net/docs/ref/#model.Node.replace) when\ngiven an invalid replacement.\n*/\nclass ReplaceError extends Error {\n}\n/*\nReplaceError = function(this: any, message: string) {\n  let err = Error.call(this, message)\n  ;(err as any).__proto__ = ReplaceError.prototype\n  return err\n} as any\n\nReplaceError.prototype = Object.create(Error.prototype)\nReplaceError.prototype.constructor = ReplaceError\nReplaceError.prototype.name = \"ReplaceError\"\n*/\n/**\nA slice represents a piece cut out of a larger document. It\nstores not only a fragment, but also the depth up to which nodes on\nboth side are ‘open’ (cut through).\n*/\nclass Slice {\n    /**\n    Create a slice. When specifying a non-zero open depth, you must\n    make sure that there are nodes of at least that depth at the\n    appropriate side of the fragment—i.e. if the fragment is an\n    empty paragraph node, `openStart` and `openEnd` can't be greater\n    than 1.\n    \n    It is not necessary for the content of open nodes to conform to\n    the schema's content constraints, though it should be a valid\n    start/end/middle for such a node, depending on which sides are\n    open.\n    */\n    constructor(\n    /**\n    The slice's content.\n    */\n    content, \n    /**\n    The open depth at the start of the fragment.\n    */\n    openStart, \n    /**\n    The open depth at the end.\n    */\n    openEnd) {\n        this.content = content;\n        this.openStart = openStart;\n        this.openEnd = openEnd;\n    }\n    /**\n    The size this slice would add when inserted into a document.\n    */\n    get size() {\n        return this.content.size - this.openStart - this.openEnd;\n    }\n    /**\n    @internal\n    */\n    insertAt(pos, fragment) {\n        let content = insertInto(this.content, pos + this.openStart, fragment);\n        return content && new Slice(content, this.openStart, this.openEnd);\n    }\n    /**\n    @internal\n    */\n    removeBetween(from, to) {\n        return new Slice(removeRange(this.content, from + this.openStart, to + this.openStart), this.openStart, this.openEnd);\n    }\n    /**\n    Tests whether this slice is equal to another slice.\n    */\n    eq(other) {\n        return this.content.eq(other.content) && this.openStart == other.openStart && this.openEnd == other.openEnd;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return this.content + \"(\" + this.openStart + \",\" + this.openEnd + \")\";\n    }\n    /**\n    Convert a slice to a JSON-serializable representation.\n    */\n    toJSON() {\n        if (!this.content.size)\n            return null;\n        let json = { content: this.content.toJSON() };\n        if (this.openStart > 0)\n            json.openStart = this.openStart;\n        if (this.openEnd > 0)\n            json.openEnd = this.openEnd;\n        return json;\n    }\n    /**\n    Deserialize a slice from its JSON representation.\n    */\n    static fromJSON(schema, json) {\n        if (!json)\n            return Slice.empty;\n        let openStart = json.openStart || 0, openEnd = json.openEnd || 0;\n        if (typeof openStart != \"number\" || typeof openEnd != \"number\")\n            throw new RangeError(\"Invalid input for Slice.fromJSON\");\n        return new Slice(Fragment.fromJSON(schema, json.content), openStart, openEnd);\n    }\n    /**\n    Create a slice from a fragment by taking the maximum possible\n    open value on both side of the fragment.\n    */\n    static maxOpen(fragment, openIsolating = true) {\n        let openStart = 0, openEnd = 0;\n        for (let n = fragment.firstChild; n && !n.isLeaf && (openIsolating || !n.type.spec.isolating); n = n.firstChild)\n            openStart++;\n        for (let n = fragment.lastChild; n && !n.isLeaf && (openIsolating || !n.type.spec.isolating); n = n.lastChild)\n            openEnd++;\n        return new Slice(fragment, openStart, openEnd);\n    }\n}\n/**\nThe empty slice.\n*/\nSlice.empty = new Slice(Fragment.empty, 0, 0);\nfunction removeRange(content, from, to) {\n    let { index, offset } = content.findIndex(from), child = content.maybeChild(index);\n    let { index: indexTo, offset: offsetTo } = content.findIndex(to);\n    if (offset == from || child.isText) {\n        if (offsetTo != to && !content.child(indexTo).isText)\n            throw new RangeError(\"Removing non-flat range\");\n        return content.cut(0, from).append(content.cut(to));\n    }\n    if (index != indexTo)\n        throw new RangeError(\"Removing non-flat range\");\n    return content.replaceChild(index, child.copy(removeRange(child.content, from - offset - 1, to - offset - 1)));\n}\nfunction insertInto(content, dist, insert, parent) {\n    let { index, offset } = content.findIndex(dist), child = content.maybeChild(index);\n    if (offset == dist || child.isText) {\n        if (parent && !parent.canReplace(index, index, insert))\n            return null;\n        return content.cut(0, dist).append(insert).append(content.cut(dist));\n    }\n    let inner = insertInto(child.content, dist - offset - 1, insert);\n    return inner && content.replaceChild(index, child.copy(inner));\n}\nfunction replace($from, $to, slice) {\n    if (slice.openStart > $from.depth)\n        throw new ReplaceError(\"Inserted content deeper than insertion position\");\n    if ($from.depth - slice.openStart != $to.depth - slice.openEnd)\n        throw new ReplaceError(\"Inconsistent open depths\");\n    return replaceOuter($from, $to, slice, 0);\n}\nfunction replaceOuter($from, $to, slice, depth) {\n    let index = $from.index(depth), node = $from.node(depth);\n    if (index == $to.index(depth) && depth < $from.depth - slice.openStart) {\n        let inner = replaceOuter($from, $to, slice, depth + 1);\n        return node.copy(node.content.replaceChild(index, inner));\n    }\n    else if (!slice.content.size) {\n        return close(node, replaceTwoWay($from, $to, depth));\n    }\n    else if (!slice.openStart && !slice.openEnd && $from.depth == depth && $to.depth == depth) { // Simple, flat case\n        let parent = $from.parent, content = parent.content;\n        return close(parent, content.cut(0, $from.parentOffset).append(slice.content).append(content.cut($to.parentOffset)));\n    }\n    else {\n        let { start, end } = prepareSliceForReplace(slice, $from);\n        return close(node, replaceThreeWay($from, start, end, $to, depth));\n    }\n}\nfunction checkJoin(main, sub) {\n    if (!sub.type.compatibleContent(main.type))\n        throw new ReplaceError(\"Cannot join \" + sub.type.name + \" onto \" + main.type.name);\n}\nfunction joinable($before, $after, depth) {\n    let node = $before.node(depth);\n    checkJoin(node, $after.node(depth));\n    return node;\n}\nfunction addNode(child, target) {\n    let last = target.length - 1;\n    if (last >= 0 && child.isText && child.sameMarkup(target[last]))\n        target[last] = child.withText(target[last].text + child.text);\n    else\n        target.push(child);\n}\nfunction addRange($start, $end, depth, target) {\n    let node = ($end || $start).node(depth);\n    let startIndex = 0, endIndex = $end ? $end.index(depth) : node.childCount;\n    if ($start) {\n        startIndex = $start.index(depth);\n        if ($start.depth > depth) {\n            startIndex++;\n        }\n        else if ($start.textOffset) {\n            addNode($start.nodeAfter, target);\n            startIndex++;\n        }\n    }\n    for (let i = startIndex; i < endIndex; i++)\n        addNode(node.child(i), target);\n    if ($end && $end.depth == depth && $end.textOffset)\n        addNode($end.nodeBefore, target);\n}\nfunction close(node, content) {\n    node.type.checkContent(content);\n    return node.copy(content);\n}\nfunction replaceThreeWay($from, $start, $end, $to, depth) {\n    let openStart = $from.depth > depth && joinable($from, $start, depth + 1);\n    let openEnd = $to.depth > depth && joinable($end, $to, depth + 1);\n    let content = [];\n    addRange(null, $from, depth, content);\n    if (openStart && openEnd && $start.index(depth) == $end.index(depth)) {\n        checkJoin(openStart, openEnd);\n        addNode(close(openStart, replaceThreeWay($from, $start, $end, $to, depth + 1)), content);\n    }\n    else {\n        if (openStart)\n            addNode(close(openStart, replaceTwoWay($from, $start, depth + 1)), content);\n        addRange($start, $end, depth, content);\n        if (openEnd)\n            addNode(close(openEnd, replaceTwoWay($end, $to, depth + 1)), content);\n    }\n    addRange($to, null, depth, content);\n    return new Fragment(content);\n}\nfunction replaceTwoWay($from, $to, depth) {\n    let content = [];\n    addRange(null, $from, depth, content);\n    if ($from.depth > depth) {\n        let type = joinable($from, $to, depth + 1);\n        addNode(close(type, replaceTwoWay($from, $to, depth + 1)), content);\n    }\n    addRange($to, null, depth, content);\n    return new Fragment(content);\n}\nfunction prepareSliceForReplace(slice, $along) {\n    let extra = $along.depth - slice.openStart, parent = $along.node(extra);\n    let node = parent.copy(slice.content);\n    for (let i = extra - 1; i >= 0; i--)\n        node = $along.node(i).copy(Fragment.from(node));\n    return { start: node.resolveNoCache(slice.openStart + extra),\n        end: node.resolveNoCache(node.content.size - slice.openEnd - extra) };\n}\n\n/**\nYou can [_resolve_](https://prosemirror.net/docs/ref/#model.Node.resolve) a position to get more\ninformation about it. Objects of this class represent such a\nresolved position, providing various pieces of context\ninformation, and some helper methods.\n\nThroughout this interface, methods that take an optional `depth`\nparameter will interpret undefined as `this.depth` and negative\nnumbers as `this.depth + value`.\n*/\nclass ResolvedPos {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The position that was resolved.\n    */\n    pos, \n    /**\n    @internal\n    */\n    path, \n    /**\n    The offset this position has into its parent node.\n    */\n    parentOffset) {\n        this.pos = pos;\n        this.path = path;\n        this.parentOffset = parentOffset;\n        this.depth = path.length / 3 - 1;\n    }\n    /**\n    @internal\n    */\n    resolveDepth(val) {\n        if (val == null)\n            return this.depth;\n        if (val < 0)\n            return this.depth + val;\n        return val;\n    }\n    /**\n    The parent node that the position points into. Note that even if\n    a position points into a text node, that node is not considered\n    the parent—text nodes are ‘flat’ in this model, and have no content.\n    */\n    get parent() { return this.node(this.depth); }\n    /**\n    The root node in which the position was resolved.\n    */\n    get doc() { return this.node(0); }\n    /**\n    The ancestor node at the given level. `p.node(p.depth)` is the\n    same as `p.parent`.\n    */\n    node(depth) { return this.path[this.resolveDepth(depth) * 3]; }\n    /**\n    The index into the ancestor at the given level. If this points\n    at the 3rd node in the 2nd paragraph on the top level, for\n    example, `p.index(0)` is 1 and `p.index(1)` is 2.\n    */\n    index(depth) { return this.path[this.resolveDepth(depth) * 3 + 1]; }\n    /**\n    The index pointing after this position into the ancestor at the\n    given level.\n    */\n    indexAfter(depth) {\n        depth = this.resolveDepth(depth);\n        return this.index(depth) + (depth == this.depth && !this.textOffset ? 0 : 1);\n    }\n    /**\n    The (absolute) position at the start of the node at the given\n    level.\n    */\n    start(depth) {\n        depth = this.resolveDepth(depth);\n        return depth == 0 ? 0 : this.path[depth * 3 - 1] + 1;\n    }\n    /**\n    The (absolute) position at the end of the node at the given\n    level.\n    */\n    end(depth) {\n        depth = this.resolveDepth(depth);\n        return this.start(depth) + this.node(depth).content.size;\n    }\n    /**\n    The (absolute) position directly before the wrapping node at the\n    given level, or, when `depth` is `this.depth + 1`, the original\n    position.\n    */\n    before(depth) {\n        depth = this.resolveDepth(depth);\n        if (!depth)\n            throw new RangeError(\"There is no position before the top-level node\");\n        return depth == this.depth + 1 ? this.pos : this.path[depth * 3 - 1];\n    }\n    /**\n    The (absolute) position directly after the wrapping node at the\n    given level, or the original position when `depth` is `this.depth + 1`.\n    */\n    after(depth) {\n        depth = this.resolveDepth(depth);\n        if (!depth)\n            throw new RangeError(\"There is no position after the top-level node\");\n        return depth == this.depth + 1 ? this.pos : this.path[depth * 3 - 1] + this.path[depth * 3].nodeSize;\n    }\n    /**\n    When this position points into a text node, this returns the\n    distance between the position and the start of the text node.\n    Will be zero for positions that point between nodes.\n    */\n    get textOffset() { return this.pos - this.path[this.path.length - 1]; }\n    /**\n    Get the node directly after the position, if any. If the position\n    points into a text node, only the part of that node after the\n    position is returned.\n    */\n    get nodeAfter() {\n        let parent = this.parent, index = this.index(this.depth);\n        if (index == parent.childCount)\n            return null;\n        let dOff = this.pos - this.path[this.path.length - 1], child = parent.child(index);\n        return dOff ? parent.child(index).cut(dOff) : child;\n    }\n    /**\n    Get the node directly before the position, if any. If the\n    position points into a text node, only the part of that node\n    before the position is returned.\n    */\n    get nodeBefore() {\n        let index = this.index(this.depth);\n        let dOff = this.pos - this.path[this.path.length - 1];\n        if (dOff)\n            return this.parent.child(index).cut(0, dOff);\n        return index == 0 ? null : this.parent.child(index - 1);\n    }\n    /**\n    Get the position at the given index in the parent node at the\n    given depth (which defaults to `this.depth`).\n    */\n    posAtIndex(index, depth) {\n        depth = this.resolveDepth(depth);\n        let node = this.path[depth * 3], pos = depth == 0 ? 0 : this.path[depth * 3 - 1] + 1;\n        for (let i = 0; i < index; i++)\n            pos += node.child(i).nodeSize;\n        return pos;\n    }\n    /**\n    Get the marks at this position, factoring in the surrounding\n    marks' [`inclusive`](https://prosemirror.net/docs/ref/#model.MarkSpec.inclusive) property. If the\n    position is at the start of a non-empty node, the marks of the\n    node after it (if any) are returned.\n    */\n    marks() {\n        let parent = this.parent, index = this.index();\n        // In an empty parent, return the empty array\n        if (parent.content.size == 0)\n            return Mark.none;\n        // When inside a text node, just return the text node's marks\n        if (this.textOffset)\n            return parent.child(index).marks;\n        let main = parent.maybeChild(index - 1), other = parent.maybeChild(index);\n        // If the `after` flag is true of there is no node before, make\n        // the node after this position the main reference.\n        if (!main) {\n            let tmp = main;\n            main = other;\n            other = tmp;\n        }\n        // Use all marks in the main node, except those that have\n        // `inclusive` set to false and are not present in the other node.\n        let marks = main.marks;\n        for (var i = 0; i < marks.length; i++)\n            if (marks[i].type.spec.inclusive === false && (!other || !marks[i].isInSet(other.marks)))\n                marks = marks[i--].removeFromSet(marks);\n        return marks;\n    }\n    /**\n    Get the marks after the current position, if any, except those\n    that are non-inclusive and not present at position `$end`. This\n    is mostly useful for getting the set of marks to preserve after a\n    deletion. Will return `null` if this position is at the end of\n    its parent node or its parent node isn't a textblock (in which\n    case no marks should be preserved).\n    */\n    marksAcross($end) {\n        let after = this.parent.maybeChild(this.index());\n        if (!after || !after.isInline)\n            return null;\n        let marks = after.marks, next = $end.parent.maybeChild($end.index());\n        for (var i = 0; i < marks.length; i++)\n            if (marks[i].type.spec.inclusive === false && (!next || !marks[i].isInSet(next.marks)))\n                marks = marks[i--].removeFromSet(marks);\n        return marks;\n    }\n    /**\n    The depth up to which this position and the given (non-resolved)\n    position share the same parent nodes.\n    */\n    sharedDepth(pos) {\n        for (let depth = this.depth; depth > 0; depth--)\n            if (this.start(depth) <= pos && this.end(depth) >= pos)\n                return depth;\n        return 0;\n    }\n    /**\n    Returns a range based on the place where this position and the\n    given position diverge around block content. If both point into\n    the same textblock, for example, a range around that textblock\n    will be returned. If they point into different blocks, the range\n    around those blocks in their shared ancestor is returned. You can\n    pass in an optional predicate that will be called with a parent\n    node to see if a range into that parent is acceptable.\n    */\n    blockRange(other = this, pred) {\n        if (other.pos < this.pos)\n            return other.blockRange(this);\n        for (let d = this.depth - (this.parent.inlineContent || this.pos == other.pos ? 1 : 0); d >= 0; d--)\n            if (other.pos <= this.end(d) && (!pred || pred(this.node(d))))\n                return new NodeRange(this, other, d);\n        return null;\n    }\n    /**\n    Query whether the given position shares the same parent node.\n    */\n    sameParent(other) {\n        return this.pos - this.parentOffset == other.pos - other.parentOffset;\n    }\n    /**\n    Return the greater of this and the given position.\n    */\n    max(other) {\n        return other.pos > this.pos ? other : this;\n    }\n    /**\n    Return the smaller of this and the given position.\n    */\n    min(other) {\n        return other.pos < this.pos ? other : this;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let str = \"\";\n        for (let i = 1; i <= this.depth; i++)\n            str += (str ? \"/\" : \"\") + this.node(i).type.name + \"_\" + this.index(i - 1);\n        return str + \":\" + this.parentOffset;\n    }\n    /**\n    @internal\n    */\n    static resolve(doc, pos) {\n        if (!(pos >= 0 && pos <= doc.content.size))\n            throw new RangeError(\"Position \" + pos + \" out of range\");\n        let path = [];\n        let start = 0, parentOffset = pos;\n        for (let node = doc;;) {\n            let { index, offset } = node.content.findIndex(parentOffset);\n            let rem = parentOffset - offset;\n            path.push(node, index, start + offset);\n            if (!rem)\n                break;\n            node = node.child(index);\n            if (node.isText)\n                break;\n            parentOffset = rem - 1;\n            start += offset + 1;\n        }\n        return new ResolvedPos(pos, path, parentOffset);\n    }\n    /**\n    @internal\n    */\n    static resolveCached(doc, pos) {\n        let cache = resolveCache.get(doc);\n        if (cache) {\n            for (let i = 0; i < cache.elts.length; i++) {\n                let elt = cache.elts[i];\n                if (elt.pos == pos)\n                    return elt;\n            }\n        }\n        else {\n            resolveCache.set(doc, cache = new ResolveCache);\n        }\n        let result = cache.elts[cache.i] = ResolvedPos.resolve(doc, pos);\n        cache.i = (cache.i + 1) % resolveCacheSize;\n        return result;\n    }\n}\nclass ResolveCache {\n    constructor() {\n        this.elts = [];\n        this.i = 0;\n    }\n}\nconst resolveCacheSize = 12, resolveCache = new WeakMap();\n/**\nRepresents a flat range of content, i.e. one that starts and\nends in the same node.\n*/\nclass NodeRange {\n    /**\n    Construct a node range. `$from` and `$to` should point into the\n    same node until at least the given `depth`, since a node range\n    denotes an adjacent set of nodes in a single parent node.\n    */\n    constructor(\n    /**\n    A resolved position along the start of the content. May have a\n    `depth` greater than this object's `depth` property, since\n    these are the positions that were used to compute the range,\n    not re-resolved positions directly at its boundaries.\n    */\n    $from, \n    /**\n    A position along the end of the content. See\n    caveat for [`$from`](https://prosemirror.net/docs/ref/#model.NodeRange.$from).\n    */\n    $to, \n    /**\n    The depth of the node that this range points into.\n    */\n    depth) {\n        this.$from = $from;\n        this.$to = $to;\n        this.depth = depth;\n    }\n    /**\n    The position at the start of the range.\n    */\n    get start() { return this.$from.before(this.depth + 1); }\n    /**\n    The position at the end of the range.\n    */\n    get end() { return this.$to.after(this.depth + 1); }\n    /**\n    The parent node that the range points into.\n    */\n    get parent() { return this.$from.node(this.depth); }\n    /**\n    The start index of the range in the parent node.\n    */\n    get startIndex() { return this.$from.index(this.depth); }\n    /**\n    The end index of the range in the parent node.\n    */\n    get endIndex() { return this.$to.indexAfter(this.depth); }\n}\n\nconst emptyAttrs = Object.create(null);\n/**\nThis class represents a node in the tree that makes up a\nProseMirror document. So a document is an instance of `Node`, with\nchildren that are also instances of `Node`.\n\nNodes are persistent data structures. Instead of changing them, you\ncreate new ones with the content you want. Old ones keep pointing\nat the old document shape. This is made cheaper by sharing\nstructure between the old and new data as much as possible, which a\ntree shape like this (without back pointers) makes easy.\n\n**Do not** directly mutate the properties of a `Node` object. See\n[the guide](/docs/guide/#doc) for more information.\n*/\nclass Node {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The type of node that this is.\n    */\n    type, \n    /**\n    An object mapping attribute names to values. The kind of\n    attributes allowed and required are\n    [determined](https://prosemirror.net/docs/ref/#model.NodeSpec.attrs) by the node type.\n    */\n    attrs, \n    // A fragment holding the node's children.\n    content, \n    /**\n    The marks (things like whether it is emphasized or part of a\n    link) applied to this node.\n    */\n    marks = Mark.none) {\n        this.type = type;\n        this.attrs = attrs;\n        this.marks = marks;\n        this.content = content || Fragment.empty;\n    }\n    /**\n    The array of this node's child nodes.\n    */\n    get children() { return this.content.content; }\n    /**\n    The size of this node, as defined by the integer-based [indexing\n    scheme](/docs/guide/#doc.indexing). For text nodes, this is the\n    amount of characters. For other leaf nodes, it is one. For\n    non-leaf nodes, it is the size of the content plus two (the\n    start and end token).\n    */\n    get nodeSize() { return this.isLeaf ? 1 : 2 + this.content.size; }\n    /**\n    The number of children that the node has.\n    */\n    get childCount() { return this.content.childCount; }\n    /**\n    Get the child node at the given index. Raises an error when the\n    index is out of range.\n    */\n    child(index) { return this.content.child(index); }\n    /**\n    Get the child node at the given index, if it exists.\n    */\n    maybeChild(index) { return this.content.maybeChild(index); }\n    /**\n    Call `f` for every child node, passing the node, its offset\n    into this parent node, and its index.\n    */\n    forEach(f) { this.content.forEach(f); }\n    /**\n    Invoke a callback for all descendant nodes recursively between\n    the given two positions that are relative to start of this\n    node's content. The callback is invoked with the node, its\n    position relative to the original node (method receiver),\n    its parent node, and its child index. When the callback returns\n    false for a given node, that node's children will not be\n    recursed over. The last parameter can be used to specify a\n    starting position to count from.\n    */\n    nodesBetween(from, to, f, startPos = 0) {\n        this.content.nodesBetween(from, to, f, startPos, this);\n    }\n    /**\n    Call the given callback for every descendant node. Doesn't\n    descend into a node when the callback returns `false`.\n    */\n    descendants(f) {\n        this.nodesBetween(0, this.content.size, f);\n    }\n    /**\n    Concatenates all the text nodes found in this fragment and its\n    children.\n    */\n    get textContent() {\n        return (this.isLeaf && this.type.spec.leafText)\n            ? this.type.spec.leafText(this)\n            : this.textBetween(0, this.content.size, \"\");\n    }\n    /**\n    Get all text between positions `from` and `to`. When\n    `blockSeparator` is given, it will be inserted to separate text\n    from different block nodes. If `leafText` is given, it'll be\n    inserted for every non-text leaf node encountered, otherwise\n    [`leafText`](https://prosemirror.net/docs/ref/#model.NodeSpec^leafText) will be used.\n    */\n    textBetween(from, to, blockSeparator, leafText) {\n        return this.content.textBetween(from, to, blockSeparator, leafText);\n    }\n    /**\n    Returns this node's first child, or `null` if there are no\n    children.\n    */\n    get firstChild() { return this.content.firstChild; }\n    /**\n    Returns this node's last child, or `null` if there are no\n    children.\n    */\n    get lastChild() { return this.content.lastChild; }\n    /**\n    Test whether two nodes represent the same piece of document.\n    */\n    eq(other) {\n        return this == other || (this.sameMarkup(other) && this.content.eq(other.content));\n    }\n    /**\n    Compare the markup (type, attributes, and marks) of this node to\n    those of another. Returns `true` if both have the same markup.\n    */\n    sameMarkup(other) {\n        return this.hasMarkup(other.type, other.attrs, other.marks);\n    }\n    /**\n    Check whether this node's markup correspond to the given type,\n    attributes, and marks.\n    */\n    hasMarkup(type, attrs, marks) {\n        return this.type == type &&\n            compareDeep(this.attrs, attrs || type.defaultAttrs || emptyAttrs) &&\n            Mark.sameSet(this.marks, marks || Mark.none);\n    }\n    /**\n    Create a new node with the same markup as this node, containing\n    the given content (or empty, if no content is given).\n    */\n    copy(content = null) {\n        if (content == this.content)\n            return this;\n        return new Node(this.type, this.attrs, content, this.marks);\n    }\n    /**\n    Create a copy of this node, with the given set of marks instead\n    of the node's own marks.\n    */\n    mark(marks) {\n        return marks == this.marks ? this : new Node(this.type, this.attrs, this.content, marks);\n    }\n    /**\n    Create a copy of this node with only the content between the\n    given positions. If `to` is not given, it defaults to the end of\n    the node.\n    */\n    cut(from, to = this.content.size) {\n        if (from == 0 && to == this.content.size)\n            return this;\n        return this.copy(this.content.cut(from, to));\n    }\n    /**\n    Cut out the part of the document between the given positions, and\n    return it as a `Slice` object.\n    */\n    slice(from, to = this.content.size, includeParents = false) {\n        if (from == to)\n            return Slice.empty;\n        let $from = this.resolve(from), $to = this.resolve(to);\n        let depth = includeParents ? 0 : $from.sharedDepth(to);\n        let start = $from.start(depth), node = $from.node(depth);\n        let content = node.content.cut($from.pos - start, $to.pos - start);\n        return new Slice(content, $from.depth - depth, $to.depth - depth);\n    }\n    /**\n    Replace the part of the document between the given positions with\n    the given slice. The slice must 'fit', meaning its open sides\n    must be able to connect to the surrounding content, and its\n    content nodes must be valid children for the node they are placed\n    into. If any of this is violated, an error of type\n    [`ReplaceError`](https://prosemirror.net/docs/ref/#model.ReplaceError) is thrown.\n    */\n    replace(from, to, slice) {\n        return replace(this.resolve(from), this.resolve(to), slice);\n    }\n    /**\n    Find the node directly after the given position.\n    */\n    nodeAt(pos) {\n        for (let node = this;;) {\n            let { index, offset } = node.content.findIndex(pos);\n            node = node.maybeChild(index);\n            if (!node)\n                return null;\n            if (offset == pos || node.isText)\n                return node;\n            pos -= offset + 1;\n        }\n    }\n    /**\n    Find the (direct) child node after the given offset, if any,\n    and return it along with its index and offset relative to this\n    node.\n    */\n    childAfter(pos) {\n        let { index, offset } = this.content.findIndex(pos);\n        return { node: this.content.maybeChild(index), index, offset };\n    }\n    /**\n    Find the (direct) child node before the given offset, if any,\n    and return it along with its index and offset relative to this\n    node.\n    */\n    childBefore(pos) {\n        if (pos == 0)\n            return { node: null, index: 0, offset: 0 };\n        let { index, offset } = this.content.findIndex(pos);\n        if (offset < pos)\n            return { node: this.content.child(index), index, offset };\n        let node = this.content.child(index - 1);\n        return { node, index: index - 1, offset: offset - node.nodeSize };\n    }\n    /**\n    Resolve the given position in the document, returning an\n    [object](https://prosemirror.net/docs/ref/#model.ResolvedPos) with information about its context.\n    */\n    resolve(pos) { return ResolvedPos.resolveCached(this, pos); }\n    /**\n    @internal\n    */\n    resolveNoCache(pos) { return ResolvedPos.resolve(this, pos); }\n    /**\n    Test whether a given mark or mark type occurs in this document\n    between the two given positions.\n    */\n    rangeHasMark(from, to, type) {\n        let found = false;\n        if (to > from)\n            this.nodesBetween(from, to, node => {\n                if (type.isInSet(node.marks))\n                    found = true;\n                return !found;\n            });\n        return found;\n    }\n    /**\n    True when this is a block (non-inline node)\n    */\n    get isBlock() { return this.type.isBlock; }\n    /**\n    True when this is a textblock node, a block node with inline\n    content.\n    */\n    get isTextblock() { return this.type.isTextblock; }\n    /**\n    True when this node allows inline content.\n    */\n    get inlineContent() { return this.type.inlineContent; }\n    /**\n    True when this is an inline node (a text node or a node that can\n    appear among text).\n    */\n    get isInline() { return this.type.isInline; }\n    /**\n    True when this is a text node.\n    */\n    get isText() { return this.type.isText; }\n    /**\n    True when this is a leaf node.\n    */\n    get isLeaf() { return this.type.isLeaf; }\n    /**\n    True when this is an atom, i.e. when it does not have directly\n    editable content. This is usually the same as `isLeaf`, but can\n    be configured with the [`atom` property](https://prosemirror.net/docs/ref/#model.NodeSpec.atom)\n    on a node's spec (typically used when the node is displayed as\n    an uneditable [node view](https://prosemirror.net/docs/ref/#view.NodeView)).\n    */\n    get isAtom() { return this.type.isAtom; }\n    /**\n    Return a string representation of this node for debugging\n    purposes.\n    */\n    toString() {\n        if (this.type.spec.toDebugString)\n            return this.type.spec.toDebugString(this);\n        let name = this.type.name;\n        if (this.content.size)\n            name += \"(\" + this.content.toStringInner() + \")\";\n        return wrapMarks(this.marks, name);\n    }\n    /**\n    Get the content match in this node at the given index.\n    */\n    contentMatchAt(index) {\n        let match = this.type.contentMatch.matchFragment(this.content, 0, index);\n        if (!match)\n            throw new Error(\"Called contentMatchAt on a node with invalid content\");\n        return match;\n    }\n    /**\n    Test whether replacing the range between `from` and `to` (by\n    child index) with the given replacement fragment (which defaults\n    to the empty fragment) would leave the node's content valid. You\n    can optionally pass `start` and `end` indices into the\n    replacement fragment.\n    */\n    canReplace(from, to, replacement = Fragment.empty, start = 0, end = replacement.childCount) {\n        let one = this.contentMatchAt(from).matchFragment(replacement, start, end);\n        let two = one && one.matchFragment(this.content, to);\n        if (!two || !two.validEnd)\n            return false;\n        for (let i = start; i < end; i++)\n            if (!this.type.allowsMarks(replacement.child(i).marks))\n                return false;\n        return true;\n    }\n    /**\n    Test whether replacing the range `from` to `to` (by index) with\n    a node of the given type would leave the node's content valid.\n    */\n    canReplaceWith(from, to, type, marks) {\n        if (marks && !this.type.allowsMarks(marks))\n            return false;\n        let start = this.contentMatchAt(from).matchType(type);\n        let end = start && start.matchFragment(this.content, to);\n        return end ? end.validEnd : false;\n    }\n    /**\n    Test whether the given node's content could be appended to this\n    node. If that node is empty, this will only return true if there\n    is at least one node type that can appear in both nodes (to avoid\n    merging completely incompatible nodes).\n    */\n    canAppend(other) {\n        if (other.content.size)\n            return this.canReplace(this.childCount, this.childCount, other.content);\n        else\n            return this.type.compatibleContent(other.type);\n    }\n    /**\n    Check whether this node and its descendants conform to the\n    schema, and raise an exception when they do not.\n    */\n    check() {\n        this.type.checkContent(this.content);\n        this.type.checkAttrs(this.attrs);\n        let copy = Mark.none;\n        for (let i = 0; i < this.marks.length; i++) {\n            let mark = this.marks[i];\n            mark.type.checkAttrs(mark.attrs);\n            copy = mark.addToSet(copy);\n        }\n        if (!Mark.sameSet(copy, this.marks))\n            throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(m => m.type.name)}`);\n        this.content.forEach(node => node.check());\n    }\n    /**\n    Return a JSON-serializeable representation of this node.\n    */\n    toJSON() {\n        let obj = { type: this.type.name };\n        for (let _ in this.attrs) {\n            obj.attrs = this.attrs;\n            break;\n        }\n        if (this.content.size)\n            obj.content = this.content.toJSON();\n        if (this.marks.length)\n            obj.marks = this.marks.map(n => n.toJSON());\n        return obj;\n    }\n    /**\n    Deserialize a node from its JSON representation.\n    */\n    static fromJSON(schema, json) {\n        if (!json)\n            throw new RangeError(\"Invalid input for Node.fromJSON\");\n        let marks = undefined;\n        if (json.marks) {\n            if (!Array.isArray(json.marks))\n                throw new RangeError(\"Invalid mark data for Node.fromJSON\");\n            marks = json.marks.map(schema.markFromJSON);\n        }\n        if (json.type == \"text\") {\n            if (typeof json.text != \"string\")\n                throw new RangeError(\"Invalid text node in JSON\");\n            return schema.text(json.text, marks);\n        }\n        let content = Fragment.fromJSON(schema, json.content);\n        let node = schema.nodeType(json.type).create(json.attrs, content, marks);\n        node.type.checkAttrs(node.attrs);\n        return node;\n    }\n}\nNode.prototype.text = undefined;\nclass TextNode extends Node {\n    /**\n    @internal\n    */\n    constructor(type, attrs, content, marks) {\n        super(type, attrs, null, marks);\n        if (!content)\n            throw new RangeError(\"Empty text nodes are not allowed\");\n        this.text = content;\n    }\n    toString() {\n        if (this.type.spec.toDebugString)\n            return this.type.spec.toDebugString(this);\n        return wrapMarks(this.marks, JSON.stringify(this.text));\n    }\n    get textContent() { return this.text; }\n    textBetween(from, to) { return this.text.slice(from, to); }\n    get nodeSize() { return this.text.length; }\n    mark(marks) {\n        return marks == this.marks ? this : new TextNode(this.type, this.attrs, this.text, marks);\n    }\n    withText(text) {\n        if (text == this.text)\n            return this;\n        return new TextNode(this.type, this.attrs, text, this.marks);\n    }\n    cut(from = 0, to = this.text.length) {\n        if (from == 0 && to == this.text.length)\n            return this;\n        return this.withText(this.text.slice(from, to));\n    }\n    eq(other) {\n        return this.sameMarkup(other) && this.text == other.text;\n    }\n    toJSON() {\n        let base = super.toJSON();\n        base.text = this.text;\n        return base;\n    }\n}\nfunction wrapMarks(marks, str) {\n    for (let i = marks.length - 1; i >= 0; i--)\n        str = marks[i].type.name + \"(\" + str + \")\";\n    return str;\n}\n\n/**\nInstances of this class represent a match state of a node type's\n[content expression](https://prosemirror.net/docs/ref/#model.NodeSpec.content), and can be used to\nfind out whether further content matches here, and whether a given\nposition is a valid end of the node.\n*/\nclass ContentMatch {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    True when this match state represents a valid end of the node.\n    */\n    validEnd) {\n        this.validEnd = validEnd;\n        /**\n        @internal\n        */\n        this.next = [];\n        /**\n        @internal\n        */\n        this.wrapCache = [];\n    }\n    /**\n    @internal\n    */\n    static parse(string, nodeTypes) {\n        let stream = new TokenStream(string, nodeTypes);\n        if (stream.next == null)\n            return ContentMatch.empty;\n        let expr = parseExpr(stream);\n        if (stream.next)\n            stream.err(\"Unexpected trailing text\");\n        let match = dfa(nfa(expr));\n        checkForDeadEnds(match, stream);\n        return match;\n    }\n    /**\n    Match a node type, returning a match after that node if\n    successful.\n    */\n    matchType(type) {\n        for (let i = 0; i < this.next.length; i++)\n            if (this.next[i].type == type)\n                return this.next[i].next;\n        return null;\n    }\n    /**\n    Try to match a fragment. Returns the resulting match when\n    successful.\n    */\n    matchFragment(frag, start = 0, end = frag.childCount) {\n        let cur = this;\n        for (let i = start; cur && i < end; i++)\n            cur = cur.matchType(frag.child(i).type);\n        return cur;\n    }\n    /**\n    @internal\n    */\n    get inlineContent() {\n        return this.next.length != 0 && this.next[0].type.isInline;\n    }\n    /**\n    Get the first matching node type at this match position that can\n    be generated.\n    */\n    get defaultType() {\n        for (let i = 0; i < this.next.length; i++) {\n            let { type } = this.next[i];\n            if (!(type.isText || type.hasRequiredAttrs()))\n                return type;\n        }\n        return null;\n    }\n    /**\n    @internal\n    */\n    compatible(other) {\n        for (let i = 0; i < this.next.length; i++)\n            for (let j = 0; j < other.next.length; j++)\n                if (this.next[i].type == other.next[j].type)\n                    return true;\n        return false;\n    }\n    /**\n    Try to match the given fragment, and if that fails, see if it can\n    be made to match by inserting nodes in front of it. When\n    successful, return a fragment of inserted nodes (which may be\n    empty if nothing had to be inserted). When `toEnd` is true, only\n    return a fragment if the resulting match goes to the end of the\n    content expression.\n    */\n    fillBefore(after, toEnd = false, startIndex = 0) {\n        let seen = [this];\n        function search(match, types) {\n            let finished = match.matchFragment(after, startIndex);\n            if (finished && (!toEnd || finished.validEnd))\n                return Fragment.from(types.map(tp => tp.createAndFill()));\n            for (let i = 0; i < match.next.length; i++) {\n                let { type, next } = match.next[i];\n                if (!(type.isText || type.hasRequiredAttrs()) && seen.indexOf(next) == -1) {\n                    seen.push(next);\n                    let found = search(next, types.concat(type));\n                    if (found)\n                        return found;\n                }\n            }\n            return null;\n        }\n        return search(this, []);\n    }\n    /**\n    Find a set of wrapping node types that would allow a node of the\n    given type to appear at this position. The result may be empty\n    (when it fits directly) and will be null when no such wrapping\n    exists.\n    */\n    findWrapping(target) {\n        for (let i = 0; i < this.wrapCache.length; i += 2)\n            if (this.wrapCache[i] == target)\n                return this.wrapCache[i + 1];\n        let computed = this.computeWrapping(target);\n        this.wrapCache.push(target, computed);\n        return computed;\n    }\n    /**\n    @internal\n    */\n    computeWrapping(target) {\n        let seen = Object.create(null), active = [{ match: this, type: null, via: null }];\n        while (active.length) {\n            let current = active.shift(), match = current.match;\n            if (match.matchType(target)) {\n                let result = [];\n                for (let obj = current; obj.type; obj = obj.via)\n                    result.push(obj.type);\n                return result.reverse();\n            }\n            for (let i = 0; i < match.next.length; i++) {\n                let { type, next } = match.next[i];\n                if (!type.isLeaf && !type.hasRequiredAttrs() && !(type.name in seen) && (!current.type || next.validEnd)) {\n                    active.push({ match: type.contentMatch, type, via: current });\n                    seen[type.name] = true;\n                }\n            }\n        }\n        return null;\n    }\n    /**\n    The number of outgoing edges this node has in the finite\n    automaton that describes the content expression.\n    */\n    get edgeCount() {\n        return this.next.length;\n    }\n    /**\n    Get the _n_​th outgoing edge from this node in the finite\n    automaton that describes the content expression.\n    */\n    edge(n) {\n        if (n >= this.next.length)\n            throw new RangeError(`There's no ${n}th edge in this content match`);\n        return this.next[n];\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let seen = [];\n        function scan(m) {\n            seen.push(m);\n            for (let i = 0; i < m.next.length; i++)\n                if (seen.indexOf(m.next[i].next) == -1)\n                    scan(m.next[i].next);\n        }\n        scan(this);\n        return seen.map((m, i) => {\n            let out = i + (m.validEnd ? \"*\" : \" \") + \" \";\n            for (let i = 0; i < m.next.length; i++)\n                out += (i ? \", \" : \"\") + m.next[i].type.name + \"->\" + seen.indexOf(m.next[i].next);\n            return out;\n        }).join(\"\\n\");\n    }\n}\n/**\n@internal\n*/\nContentMatch.empty = new ContentMatch(true);\nclass TokenStream {\n    constructor(string, nodeTypes) {\n        this.string = string;\n        this.nodeTypes = nodeTypes;\n        this.inline = null;\n        this.pos = 0;\n        this.tokens = string.split(/\\s*(?=\\b|\\W|$)/);\n        if (this.tokens[this.tokens.length - 1] == \"\")\n            this.tokens.pop();\n        if (this.tokens[0] == \"\")\n            this.tokens.shift();\n    }\n    get next() { return this.tokens[this.pos]; }\n    eat(tok) { return this.next == tok && (this.pos++ || true); }\n    err(str) { throw new SyntaxError(str + \" (in content expression '\" + this.string + \"')\"); }\n}\nfunction parseExpr(stream) {\n    let exprs = [];\n    do {\n        exprs.push(parseExprSeq(stream));\n    } while (stream.eat(\"|\"));\n    return exprs.length == 1 ? exprs[0] : { type: \"choice\", exprs };\n}\nfunction parseExprSeq(stream) {\n    let exprs = [];\n    do {\n        exprs.push(parseExprSubscript(stream));\n    } while (stream.next && stream.next != \")\" && stream.next != \"|\");\n    return exprs.length == 1 ? exprs[0] : { type: \"seq\", exprs };\n}\nfunction parseExprSubscript(stream) {\n    let expr = parseExprAtom(stream);\n    for (;;) {\n        if (stream.eat(\"+\"))\n            expr = { type: \"plus\", expr };\n        else if (stream.eat(\"*\"))\n            expr = { type: \"star\", expr };\n        else if (stream.eat(\"?\"))\n            expr = { type: \"opt\", expr };\n        else if (stream.eat(\"{\"))\n            expr = parseExprRange(stream, expr);\n        else\n            break;\n    }\n    return expr;\n}\nfunction parseNum(stream) {\n    if (/\\D/.test(stream.next))\n        stream.err(\"Expected number, got '\" + stream.next + \"'\");\n    let result = Number(stream.next);\n    stream.pos++;\n    return result;\n}\nfunction parseExprRange(stream, expr) {\n    let min = parseNum(stream), max = min;\n    if (stream.eat(\",\")) {\n        if (stream.next != \"}\")\n            max = parseNum(stream);\n        else\n            max = -1;\n    }\n    if (!stream.eat(\"}\"))\n        stream.err(\"Unclosed braced range\");\n    return { type: \"range\", min, max, expr };\n}\nfunction resolveName(stream, name) {\n    let types = stream.nodeTypes, type = types[name];\n    if (type)\n        return [type];\n    let result = [];\n    for (let typeName in types) {\n        let type = types[typeName];\n        if (type.isInGroup(name))\n            result.push(type);\n    }\n    if (result.length == 0)\n        stream.err(\"No node type or group '\" + name + \"' found\");\n    return result;\n}\nfunction parseExprAtom(stream) {\n    if (stream.eat(\"(\")) {\n        let expr = parseExpr(stream);\n        if (!stream.eat(\")\"))\n            stream.err(\"Missing closing paren\");\n        return expr;\n    }\n    else if (!/\\W/.test(stream.next)) {\n        let exprs = resolveName(stream, stream.next).map(type => {\n            if (stream.inline == null)\n                stream.inline = type.isInline;\n            else if (stream.inline != type.isInline)\n                stream.err(\"Mixing inline and block content\");\n            return { type: \"name\", value: type };\n        });\n        stream.pos++;\n        return exprs.length == 1 ? exprs[0] : { type: \"choice\", exprs };\n    }\n    else {\n        stream.err(\"Unexpected token '\" + stream.next + \"'\");\n    }\n}\n// Construct an NFA from an expression as returned by the parser. The\n// NFA is represented as an array of states, which are themselves\n// arrays of edges, which are `{term, to}` objects. The first state is\n// the entry state and the last node is the success state.\n//\n// Note that unlike typical NFAs, the edge ordering in this one is\n// significant, in that it is used to contruct filler content when\n// necessary.\nfunction nfa(expr) {\n    let nfa = [[]];\n    connect(compile(expr, 0), node());\n    return nfa;\n    function node() { return nfa.push([]) - 1; }\n    function edge(from, to, term) {\n        let edge = { term, to };\n        nfa[from].push(edge);\n        return edge;\n    }\n    function connect(edges, to) {\n        edges.forEach(edge => edge.to = to);\n    }\n    function compile(expr, from) {\n        if (expr.type == \"choice\") {\n            return expr.exprs.reduce((out, expr) => out.concat(compile(expr, from)), []);\n        }\n        else if (expr.type == \"seq\") {\n            for (let i = 0;; i++) {\n                let next = compile(expr.exprs[i], from);\n                if (i == expr.exprs.length - 1)\n                    return next;\n                connect(next, from = node());\n            }\n        }\n        else if (expr.type == \"star\") {\n            let loop = node();\n            edge(from, loop);\n            connect(compile(expr.expr, loop), loop);\n            return [edge(loop)];\n        }\n        else if (expr.type == \"plus\") {\n            let loop = node();\n            connect(compile(expr.expr, from), loop);\n            connect(compile(expr.expr, loop), loop);\n            return [edge(loop)];\n        }\n        else if (expr.type == \"opt\") {\n            return [edge(from)].concat(compile(expr.expr, from));\n        }\n        else if (expr.type == \"range\") {\n            let cur = from;\n            for (let i = 0; i < expr.min; i++) {\n                let next = node();\n                connect(compile(expr.expr, cur), next);\n                cur = next;\n            }\n            if (expr.max == -1) {\n                connect(compile(expr.expr, cur), cur);\n            }\n            else {\n                for (let i = expr.min; i < expr.max; i++) {\n                    let next = node();\n                    edge(cur, next);\n                    connect(compile(expr.expr, cur), next);\n                    cur = next;\n                }\n            }\n            return [edge(cur)];\n        }\n        else if (expr.type == \"name\") {\n            return [edge(from, undefined, expr.value)];\n        }\n        else {\n            throw new Error(\"Unknown expr type\");\n        }\n    }\n}\nfunction cmp(a, b) { return b - a; }\n// Get the set of nodes reachable by null edges from `node`. Omit\n// nodes with only a single null-out-edge, since they may lead to\n// needless duplicated nodes.\nfunction nullFrom(nfa, node) {\n    let result = [];\n    scan(node);\n    return result.sort(cmp);\n    function scan(node) {\n        let edges = nfa[node];\n        if (edges.length == 1 && !edges[0].term)\n            return scan(edges[0].to);\n        result.push(node);\n        for (let i = 0; i < edges.length; i++) {\n            let { term, to } = edges[i];\n            if (!term && result.indexOf(to) == -1)\n                scan(to);\n        }\n    }\n}\n// Compiles an NFA as produced by `nfa` into a DFA, modeled as a set\n// of state objects (`ContentMatch` instances) with transitions\n// between them.\nfunction dfa(nfa) {\n    let labeled = Object.create(null);\n    return explore(nullFrom(nfa, 0));\n    function explore(states) {\n        let out = [];\n        states.forEach(node => {\n            nfa[node].forEach(({ term, to }) => {\n                if (!term)\n                    return;\n                let set;\n                for (let i = 0; i < out.length; i++)\n                    if (out[i][0] == term)\n                        set = out[i][1];\n                nullFrom(nfa, to).forEach(node => {\n                    if (!set)\n                        out.push([term, set = []]);\n                    if (set.indexOf(node) == -1)\n                        set.push(node);\n                });\n            });\n        });\n        let state = labeled[states.join(\",\")] = new ContentMatch(states.indexOf(nfa.length - 1) > -1);\n        for (let i = 0; i < out.length; i++) {\n            let states = out[i][1].sort(cmp);\n            state.next.push({ type: out[i][0], next: labeled[states.join(\",\")] || explore(states) });\n        }\n        return state;\n    }\n}\nfunction checkForDeadEnds(match, stream) {\n    for (let i = 0, work = [match]; i < work.length; i++) {\n        let state = work[i], dead = !state.validEnd, nodes = [];\n        for (let j = 0; j < state.next.length; j++) {\n            let { type, next } = state.next[j];\n            nodes.push(type.name);\n            if (dead && !(type.isText || type.hasRequiredAttrs()))\n                dead = false;\n            if (work.indexOf(next) == -1)\n                work.push(next);\n        }\n        if (dead)\n            stream.err(\"Only non-generatable nodes (\" + nodes.join(\", \") + \") in a required position (see https://prosemirror.net/docs/guide/#generatable)\");\n    }\n}\n\n// For node types where all attrs have a default value (or which don't\n// have any attributes), build up a single reusable default attribute\n// object, and use it for all nodes that don't specify specific\n// attributes.\nfunction defaultAttrs(attrs) {\n    let defaults = Object.create(null);\n    for (let attrName in attrs) {\n        let attr = attrs[attrName];\n        if (!attr.hasDefault)\n            return null;\n        defaults[attrName] = attr.default;\n    }\n    return defaults;\n}\nfunction computeAttrs(attrs, value) {\n    let built = Object.create(null);\n    for (let name in attrs) {\n        let given = value && value[name];\n        if (given === undefined) {\n            let attr = attrs[name];\n            if (attr.hasDefault)\n                given = attr.default;\n            else\n                throw new RangeError(\"No value supplied for attribute \" + name);\n        }\n        built[name] = given;\n    }\n    return built;\n}\nfunction checkAttrs(attrs, values, type, name) {\n    for (let name in values)\n        if (!(name in attrs))\n            throw new RangeError(`Unsupported attribute ${name} for ${type} of type ${name}`);\n    for (let name in attrs) {\n        let attr = attrs[name];\n        if (attr.validate)\n            attr.validate(values[name]);\n    }\n}\nfunction initAttrs(typeName, attrs) {\n    let result = Object.create(null);\n    if (attrs)\n        for (let name in attrs)\n            result[name] = new Attribute(typeName, name, attrs[name]);\n    return result;\n}\n/**\nNode types are objects allocated once per `Schema` and used to\n[tag](https://prosemirror.net/docs/ref/#model.Node.type) `Node` instances. They contain information\nabout the node type, such as its name and what kind of node it\nrepresents.\n*/\nclass NodeType {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The name the node type has in this schema.\n    */\n    name, \n    /**\n    A link back to the `Schema` the node type belongs to.\n    */\n    schema, \n    /**\n    The spec that this type is based on\n    */\n    spec) {\n        this.name = name;\n        this.schema = schema;\n        this.spec = spec;\n        /**\n        The set of marks allowed in this node. `null` means all marks\n        are allowed.\n        */\n        this.markSet = null;\n        this.groups = spec.group ? spec.group.split(\" \") : [];\n        this.attrs = initAttrs(name, spec.attrs);\n        this.defaultAttrs = defaultAttrs(this.attrs);\n        this.contentMatch = null;\n        this.inlineContent = null;\n        this.isBlock = !(spec.inline || name == \"text\");\n        this.isText = name == \"text\";\n    }\n    /**\n    True if this is an inline type.\n    */\n    get isInline() { return !this.isBlock; }\n    /**\n    True if this is a textblock type, a block that contains inline\n    content.\n    */\n    get isTextblock() { return this.isBlock && this.inlineContent; }\n    /**\n    True for node types that allow no content.\n    */\n    get isLeaf() { return this.contentMatch == ContentMatch.empty; }\n    /**\n    True when this node is an atom, i.e. when it does not have\n    directly editable content.\n    */\n    get isAtom() { return this.isLeaf || !!this.spec.atom; }\n    /**\n    Return true when this node type is part of the given\n    [group](https://prosemirror.net/docs/ref/#model.NodeSpec.group).\n    */\n    isInGroup(group) {\n        return this.groups.indexOf(group) > -1;\n    }\n    /**\n    The node type's [whitespace](https://prosemirror.net/docs/ref/#model.NodeSpec.whitespace) option.\n    */\n    get whitespace() {\n        return this.spec.whitespace || (this.spec.code ? \"pre\" : \"normal\");\n    }\n    /**\n    Tells you whether this node type has any required attributes.\n    */\n    hasRequiredAttrs() {\n        for (let n in this.attrs)\n            if (this.attrs[n].isRequired)\n                return true;\n        return false;\n    }\n    /**\n    Indicates whether this node allows some of the same content as\n    the given node type.\n    */\n    compatibleContent(other) {\n        return this == other || this.contentMatch.compatible(other.contentMatch);\n    }\n    /**\n    @internal\n    */\n    computeAttrs(attrs) {\n        if (!attrs && this.defaultAttrs)\n            return this.defaultAttrs;\n        else\n            return computeAttrs(this.attrs, attrs);\n    }\n    /**\n    Create a `Node` of this type. The given attributes are\n    checked and defaulted (you can pass `null` to use the type's\n    defaults entirely, if no required attributes exist). `content`\n    may be a `Fragment`, a node, an array of nodes, or\n    `null`. Similarly `marks` may be `null` to default to the empty\n    set of marks.\n    */\n    create(attrs = null, content, marks) {\n        if (this.isText)\n            throw new Error(\"NodeType.create can't construct text nodes\");\n        return new Node(this, this.computeAttrs(attrs), Fragment.from(content), Mark.setFrom(marks));\n    }\n    /**\n    Like [`create`](https://prosemirror.net/docs/ref/#model.NodeType.create), but check the given content\n    against the node type's content restrictions, and throw an error\n    if it doesn't match.\n    */\n    createChecked(attrs = null, content, marks) {\n        content = Fragment.from(content);\n        this.checkContent(content);\n        return new Node(this, this.computeAttrs(attrs), content, Mark.setFrom(marks));\n    }\n    /**\n    Like [`create`](https://prosemirror.net/docs/ref/#model.NodeType.create), but see if it is\n    necessary to add nodes to the start or end of the given fragment\n    to make it fit the node. If no fitting wrapping can be found,\n    return null. Note that, due to the fact that required nodes can\n    always be created, this will always succeed if you pass null or\n    `Fragment.empty` as content.\n    */\n    createAndFill(attrs = null, content, marks) {\n        attrs = this.computeAttrs(attrs);\n        content = Fragment.from(content);\n        if (content.size) {\n            let before = this.contentMatch.fillBefore(content);\n            if (!before)\n                return null;\n            content = before.append(content);\n        }\n        let matched = this.contentMatch.matchFragment(content);\n        let after = matched && matched.fillBefore(Fragment.empty, true);\n        if (!after)\n            return null;\n        return new Node(this, attrs, content.append(after), Mark.setFrom(marks));\n    }\n    /**\n    Returns true if the given fragment is valid content for this node\n    type.\n    */\n    validContent(content) {\n        let result = this.contentMatch.matchFragment(content);\n        if (!result || !result.validEnd)\n            return false;\n        for (let i = 0; i < content.childCount; i++)\n            if (!this.allowsMarks(content.child(i).marks))\n                return false;\n        return true;\n    }\n    /**\n    Throws a RangeError if the given fragment is not valid content for this\n    node type.\n    @internal\n    */\n    checkContent(content) {\n        if (!this.validContent(content))\n            throw new RangeError(`Invalid content for node ${this.name}: ${content.toString().slice(0, 50)}`);\n    }\n    /**\n    @internal\n    */\n    checkAttrs(attrs) {\n        checkAttrs(this.attrs, attrs, \"node\", this.name);\n    }\n    /**\n    Check whether the given mark type is allowed in this node.\n    */\n    allowsMarkType(markType) {\n        return this.markSet == null || this.markSet.indexOf(markType) > -1;\n    }\n    /**\n    Test whether the given set of marks are allowed in this node.\n    */\n    allowsMarks(marks) {\n        if (this.markSet == null)\n            return true;\n        for (let i = 0; i < marks.length; i++)\n            if (!this.allowsMarkType(marks[i].type))\n                return false;\n        return true;\n    }\n    /**\n    Removes the marks that are not allowed in this node from the given set.\n    */\n    allowedMarks(marks) {\n        if (this.markSet == null)\n            return marks;\n        let copy;\n        for (let i = 0; i < marks.length; i++) {\n            if (!this.allowsMarkType(marks[i].type)) {\n                if (!copy)\n                    copy = marks.slice(0, i);\n            }\n            else if (copy) {\n                copy.push(marks[i]);\n            }\n        }\n        return !copy ? marks : copy.length ? copy : Mark.none;\n    }\n    /**\n    @internal\n    */\n    static compile(nodes, schema) {\n        let result = Object.create(null);\n        nodes.forEach((name, spec) => result[name] = new NodeType(name, schema, spec));\n        let topType = schema.spec.topNode || \"doc\";\n        if (!result[topType])\n            throw new RangeError(\"Schema is missing its top node type ('\" + topType + \"')\");\n        if (!result.text)\n            throw new RangeError(\"Every schema needs a 'text' type\");\n        for (let _ in result.text.attrs)\n            throw new RangeError(\"The text node type should not have attributes\");\n        return result;\n    }\n}\nfunction validateType(typeName, attrName, type) {\n    let types = type.split(\"|\");\n    return (value) => {\n        let name = value === null ? \"null\" : typeof value;\n        if (types.indexOf(name) < 0)\n            throw new RangeError(`Expected value of type ${types} for attribute ${attrName} on type ${typeName}, got ${name}`);\n    };\n}\n// Attribute descriptors\nclass Attribute {\n    constructor(typeName, attrName, options) {\n        this.hasDefault = Object.prototype.hasOwnProperty.call(options, \"default\");\n        this.default = options.default;\n        this.validate = typeof options.validate == \"string\" ? validateType(typeName, attrName, options.validate) : options.validate;\n    }\n    get isRequired() {\n        return !this.hasDefault;\n    }\n}\n// Marks\n/**\nLike nodes, marks (which are associated with nodes to signify\nthings like emphasis or being part of a link) are\n[tagged](https://prosemirror.net/docs/ref/#model.Mark.type) with type objects, which are\ninstantiated once per `Schema`.\n*/\nclass MarkType {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The name of the mark type.\n    */\n    name, \n    /**\n    @internal\n    */\n    rank, \n    /**\n    The schema that this mark type instance is part of.\n    */\n    schema, \n    /**\n    The spec on which the type is based.\n    */\n    spec) {\n        this.name = name;\n        this.rank = rank;\n        this.schema = schema;\n        this.spec = spec;\n        this.attrs = initAttrs(name, spec.attrs);\n        this.excluded = null;\n        let defaults = defaultAttrs(this.attrs);\n        this.instance = defaults ? new Mark(this, defaults) : null;\n    }\n    /**\n    Create a mark of this type. `attrs` may be `null` or an object\n    containing only some of the mark's attributes. The others, if\n    they have defaults, will be added.\n    */\n    create(attrs = null) {\n        if (!attrs && this.instance)\n            return this.instance;\n        return new Mark(this, computeAttrs(this.attrs, attrs));\n    }\n    /**\n    @internal\n    */\n    static compile(marks, schema) {\n        let result = Object.create(null), rank = 0;\n        marks.forEach((name, spec) => result[name] = new MarkType(name, rank++, schema, spec));\n        return result;\n    }\n    /**\n    When there is a mark of this type in the given set, a new set\n    without it is returned. Otherwise, the input set is returned.\n    */\n    removeFromSet(set) {\n        for (var i = 0; i < set.length; i++)\n            if (set[i].type == this) {\n                set = set.slice(0, i).concat(set.slice(i + 1));\n                i--;\n            }\n        return set;\n    }\n    /**\n    Tests whether there is a mark of this type in the given set.\n    */\n    isInSet(set) {\n        for (let i = 0; i < set.length; i++)\n            if (set[i].type == this)\n                return set[i];\n    }\n    /**\n    @internal\n    */\n    checkAttrs(attrs) {\n        checkAttrs(this.attrs, attrs, \"mark\", this.name);\n    }\n    /**\n    Queries whether a given mark type is\n    [excluded](https://prosemirror.net/docs/ref/#model.MarkSpec.excludes) by this one.\n    */\n    excludes(other) {\n        return this.excluded.indexOf(other) > -1;\n    }\n}\n/**\nA document schema. Holds [node](https://prosemirror.net/docs/ref/#model.NodeType) and [mark\ntype](https://prosemirror.net/docs/ref/#model.MarkType) objects for the nodes and marks that may\noccur in conforming documents, and provides functionality for\ncreating and deserializing such documents.\n\nWhen given, the type parameters provide the names of the nodes and\nmarks in this schema.\n*/\nclass Schema {\n    /**\n    Construct a schema from a schema [specification](https://prosemirror.net/docs/ref/#model.SchemaSpec).\n    */\n    constructor(spec) {\n        /**\n        The [linebreak\n        replacement](https://prosemirror.net/docs/ref/#model.NodeSpec.linebreakReplacement) node defined\n        in this schema, if any.\n        */\n        this.linebreakReplacement = null;\n        /**\n        An object for storing whatever values modules may want to\n        compute and cache per schema. (If you want to store something\n        in it, try to use property names unlikely to clash.)\n        */\n        this.cached = Object.create(null);\n        let instanceSpec = this.spec = {};\n        for (let prop in spec)\n            instanceSpec[prop] = spec[prop];\n        instanceSpec.nodes = orderedmap__WEBPACK_IMPORTED_MODULE_0__[\"default\"].from(spec.nodes),\n            instanceSpec.marks = orderedmap__WEBPACK_IMPORTED_MODULE_0__[\"default\"].from(spec.marks || {}),\n            this.nodes = NodeType.compile(this.spec.nodes, this);\n        this.marks = MarkType.compile(this.spec.marks, this);\n        let contentExprCache = Object.create(null);\n        for (let prop in this.nodes) {\n            if (prop in this.marks)\n                throw new RangeError(prop + \" can not be both a node and a mark\");\n            let type = this.nodes[prop], contentExpr = type.spec.content || \"\", markExpr = type.spec.marks;\n            type.contentMatch = contentExprCache[contentExpr] ||\n                (contentExprCache[contentExpr] = ContentMatch.parse(contentExpr, this.nodes));\n            type.inlineContent = type.contentMatch.inlineContent;\n            if (type.spec.linebreakReplacement) {\n                if (this.linebreakReplacement)\n                    throw new RangeError(\"Multiple linebreak nodes defined\");\n                if (!type.isInline || !type.isLeaf)\n                    throw new RangeError(\"Linebreak replacement nodes must be inline leaf nodes\");\n                this.linebreakReplacement = type;\n            }\n            type.markSet = markExpr == \"_\" ? null :\n                markExpr ? gatherMarks(this, markExpr.split(\" \")) :\n                    markExpr == \"\" || !type.inlineContent ? [] : null;\n        }\n        for (let prop in this.marks) {\n            let type = this.marks[prop], excl = type.spec.excludes;\n            type.excluded = excl == null ? [type] : excl == \"\" ? [] : gatherMarks(this, excl.split(\" \"));\n        }\n        this.nodeFromJSON = this.nodeFromJSON.bind(this);\n        this.markFromJSON = this.markFromJSON.bind(this);\n        this.topNodeType = this.nodes[this.spec.topNode || \"doc\"];\n        this.cached.wrappings = Object.create(null);\n    }\n    /**\n    Create a node in this schema. The `type` may be a string or a\n    `NodeType` instance. Attributes will be extended with defaults,\n    `content` may be a `Fragment`, `null`, a `Node`, or an array of\n    nodes.\n    */\n    node(type, attrs = null, content, marks) {\n        if (typeof type == \"string\")\n            type = this.nodeType(type);\n        else if (!(type instanceof NodeType))\n            throw new RangeError(\"Invalid node type: \" + type);\n        else if (type.schema != this)\n            throw new RangeError(\"Node type from different schema used (\" + type.name + \")\");\n        return type.createChecked(attrs, content, marks);\n    }\n    /**\n    Create a text node in the schema. Empty text nodes are not\n    allowed.\n    */\n    text(text, marks) {\n        let type = this.nodes.text;\n        return new TextNode(type, type.defaultAttrs, text, Mark.setFrom(marks));\n    }\n    /**\n    Create a mark with the given type and attributes.\n    */\n    mark(type, attrs) {\n        if (typeof type == \"string\")\n            type = this.marks[type];\n        return type.create(attrs);\n    }\n    /**\n    Deserialize a node from its JSON representation. This method is\n    bound.\n    */\n    nodeFromJSON(json) {\n        return Node.fromJSON(this, json);\n    }\n    /**\n    Deserialize a mark from its JSON representation. This method is\n    bound.\n    */\n    markFromJSON(json) {\n        return Mark.fromJSON(this, json);\n    }\n    /**\n    @internal\n    */\n    nodeType(name) {\n        let found = this.nodes[name];\n        if (!found)\n            throw new RangeError(\"Unknown node type: \" + name);\n        return found;\n    }\n}\nfunction gatherMarks(schema, marks) {\n    let found = [];\n    for (let i = 0; i < marks.length; i++) {\n        let name = marks[i], mark = schema.marks[name], ok = mark;\n        if (mark) {\n            found.push(mark);\n        }\n        else {\n            for (let prop in schema.marks) {\n                let mark = schema.marks[prop];\n                if (name == \"_\" || (mark.spec.group && mark.spec.group.split(\" \").indexOf(name) > -1))\n                    found.push(ok = mark);\n            }\n        }\n        if (!ok)\n            throw new SyntaxError(\"Unknown mark type: '\" + marks[i] + \"'\");\n    }\n    return found;\n}\n\nfunction isTagRule(rule) { return rule.tag != null; }\nfunction isStyleRule(rule) { return rule.style != null; }\n/**\nA DOM parser represents a strategy for parsing DOM content into a\nProseMirror document conforming to a given schema. Its behavior is\ndefined by an array of [rules](https://prosemirror.net/docs/ref/#model.ParseRule).\n*/\nclass DOMParser {\n    /**\n    Create a parser that targets the given schema, using the given\n    parsing rules.\n    */\n    constructor(\n    /**\n    The schema into which the parser parses.\n    */\n    schema, \n    /**\n    The set of [parse rules](https://prosemirror.net/docs/ref/#model.ParseRule) that the parser\n    uses, in order of precedence.\n    */\n    rules) {\n        this.schema = schema;\n        this.rules = rules;\n        /**\n        @internal\n        */\n        this.tags = [];\n        /**\n        @internal\n        */\n        this.styles = [];\n        let matchedStyles = this.matchedStyles = [];\n        rules.forEach(rule => {\n            if (isTagRule(rule)) {\n                this.tags.push(rule);\n            }\n            else if (isStyleRule(rule)) {\n                let prop = /[^=]*/.exec(rule.style)[0];\n                if (matchedStyles.indexOf(prop) < 0)\n                    matchedStyles.push(prop);\n                this.styles.push(rule);\n            }\n        });\n        // Only normalize list elements when lists in the schema can't directly contain themselves\n        this.normalizeLists = !this.tags.some(r => {\n            if (!/^(ul|ol)\\b/.test(r.tag) || !r.node)\n                return false;\n            let node = schema.nodes[r.node];\n            return node.contentMatch.matchType(node);\n        });\n    }\n    /**\n    Parse a document from the content of a DOM node.\n    */\n    parse(dom, options = {}) {\n        let context = new ParseContext(this, options, false);\n        context.addAll(dom, Mark.none, options.from, options.to);\n        return context.finish();\n    }\n    /**\n    Parses the content of the given DOM node, like\n    [`parse`](https://prosemirror.net/docs/ref/#model.DOMParser.parse), and takes the same set of\n    options. But unlike that method, which produces a whole node,\n    this one returns a slice that is open at the sides, meaning that\n    the schema constraints aren't applied to the start of nodes to\n    the left of the input and the end of nodes at the end.\n    */\n    parseSlice(dom, options = {}) {\n        let context = new ParseContext(this, options, true);\n        context.addAll(dom, Mark.none, options.from, options.to);\n        return Slice.maxOpen(context.finish());\n    }\n    /**\n    @internal\n    */\n    matchTag(dom, context, after) {\n        for (let i = after ? this.tags.indexOf(after) + 1 : 0; i < this.tags.length; i++) {\n            let rule = this.tags[i];\n            if (matches(dom, rule.tag) &&\n                (rule.namespace === undefined || dom.namespaceURI == rule.namespace) &&\n                (!rule.context || context.matchesContext(rule.context))) {\n                if (rule.getAttrs) {\n                    let result = rule.getAttrs(dom);\n                    if (result === false)\n                        continue;\n                    rule.attrs = result || undefined;\n                }\n                return rule;\n            }\n        }\n    }\n    /**\n    @internal\n    */\n    matchStyle(prop, value, context, after) {\n        for (let i = after ? this.styles.indexOf(after) + 1 : 0; i < this.styles.length; i++) {\n            let rule = this.styles[i], style = rule.style;\n            if (style.indexOf(prop) != 0 ||\n                rule.context && !context.matchesContext(rule.context) ||\n                // Test that the style string either precisely matches the prop,\n                // or has an '=' sign after the prop, followed by the given\n                // value.\n                style.length > prop.length &&\n                    (style.charCodeAt(prop.length) != 61 || style.slice(prop.length + 1) != value))\n                continue;\n            if (rule.getAttrs) {\n                let result = rule.getAttrs(value);\n                if (result === false)\n                    continue;\n                rule.attrs = result || undefined;\n            }\n            return rule;\n        }\n    }\n    /**\n    @internal\n    */\n    static schemaRules(schema) {\n        let result = [];\n        function insert(rule) {\n            let priority = rule.priority == null ? 50 : rule.priority, i = 0;\n            for (; i < result.length; i++) {\n                let next = result[i], nextPriority = next.priority == null ? 50 : next.priority;\n                if (nextPriority < priority)\n                    break;\n            }\n            result.splice(i, 0, rule);\n        }\n        for (let name in schema.marks) {\n            let rules = schema.marks[name].spec.parseDOM;\n            if (rules)\n                rules.forEach(rule => {\n                    insert(rule = copy(rule));\n                    if (!(rule.mark || rule.ignore || rule.clearMark))\n                        rule.mark = name;\n                });\n        }\n        for (let name in schema.nodes) {\n            let rules = schema.nodes[name].spec.parseDOM;\n            if (rules)\n                rules.forEach(rule => {\n                    insert(rule = copy(rule));\n                    if (!(rule.node || rule.ignore || rule.mark))\n                        rule.node = name;\n                });\n        }\n        return result;\n    }\n    /**\n    Construct a DOM parser using the parsing rules listed in a\n    schema's [node specs](https://prosemirror.net/docs/ref/#model.NodeSpec.parseDOM), reordered by\n    [priority](https://prosemirror.net/docs/ref/#model.ParseRule.priority).\n    */\n    static fromSchema(schema) {\n        return schema.cached.domParser ||\n            (schema.cached.domParser = new DOMParser(schema, DOMParser.schemaRules(schema)));\n    }\n}\nconst blockTags = {\n    address: true, article: true, aside: true, blockquote: true, canvas: true,\n    dd: true, div: true, dl: true, fieldset: true, figcaption: true, figure: true,\n    footer: true, form: true, h1: true, h2: true, h3: true, h4: true, h5: true,\n    h6: true, header: true, hgroup: true, hr: true, li: true, noscript: true, ol: true,\n    output: true, p: true, pre: true, section: true, table: true, tfoot: true, ul: true\n};\nconst ignoreTags = {\n    head: true, noscript: true, object: true, script: true, style: true, title: true\n};\nconst listTags = { ol: true, ul: true };\n// Using a bitfield for node context options\nconst OPT_PRESERVE_WS = 1, OPT_PRESERVE_WS_FULL = 2, OPT_OPEN_LEFT = 4;\nfunction wsOptionsFor(type, preserveWhitespace, base) {\n    if (preserveWhitespace != null)\n        return (preserveWhitespace ? OPT_PRESERVE_WS : 0) |\n            (preserveWhitespace === \"full\" ? OPT_PRESERVE_WS_FULL : 0);\n    return type && type.whitespace == \"pre\" ? OPT_PRESERVE_WS | OPT_PRESERVE_WS_FULL : base & ~OPT_OPEN_LEFT;\n}\nclass NodeContext {\n    constructor(type, attrs, marks, solid, match, options) {\n        this.type = type;\n        this.attrs = attrs;\n        this.marks = marks;\n        this.solid = solid;\n        this.options = options;\n        this.content = [];\n        // Marks applied to the node's children\n        this.activeMarks = Mark.none;\n        this.match = match || (options & OPT_OPEN_LEFT ? null : type.contentMatch);\n    }\n    findWrapping(node) {\n        if (!this.match) {\n            if (!this.type)\n                return [];\n            let fill = this.type.contentMatch.fillBefore(Fragment.from(node));\n            if (fill) {\n                this.match = this.type.contentMatch.matchFragment(fill);\n            }\n            else {\n                let start = this.type.contentMatch, wrap;\n                if (wrap = start.findWrapping(node.type)) {\n                    this.match = start;\n                    return wrap;\n                }\n                else {\n                    return null;\n                }\n            }\n        }\n        return this.match.findWrapping(node.type);\n    }\n    finish(openEnd) {\n        if (!(this.options & OPT_PRESERVE_WS)) { // Strip trailing whitespace\n            let last = this.content[this.content.length - 1], m;\n            if (last && last.isText && (m = /[ \\t\\r\\n\\u000c]+$/.exec(last.text))) {\n                let text = last;\n                if (last.text.length == m[0].length)\n                    this.content.pop();\n                else\n                    this.content[this.content.length - 1] = text.withText(text.text.slice(0, text.text.length - m[0].length));\n            }\n        }\n        let content = Fragment.from(this.content);\n        if (!openEnd && this.match)\n            content = content.append(this.match.fillBefore(Fragment.empty, true));\n        return this.type ? this.type.create(this.attrs, content, this.marks) : content;\n    }\n    inlineContext(node) {\n        if (this.type)\n            return this.type.inlineContent;\n        if (this.content.length)\n            return this.content[0].isInline;\n        return node.parentNode && !blockTags.hasOwnProperty(node.parentNode.nodeName.toLowerCase());\n    }\n}\nclass ParseContext {\n    constructor(\n    // The parser we are using.\n    parser, \n    // The options passed to this parse.\n    options, isOpen) {\n        this.parser = parser;\n        this.options = options;\n        this.isOpen = isOpen;\n        this.open = 0;\n        this.localPreserveWS = false;\n        let topNode = options.topNode, topContext;\n        let topOptions = wsOptionsFor(null, options.preserveWhitespace, 0) | (isOpen ? OPT_OPEN_LEFT : 0);\n        if (topNode)\n            topContext = new NodeContext(topNode.type, topNode.attrs, Mark.none, true, options.topMatch || topNode.type.contentMatch, topOptions);\n        else if (isOpen)\n            topContext = new NodeContext(null, null, Mark.none, true, null, topOptions);\n        else\n            topContext = new NodeContext(parser.schema.topNodeType, null, Mark.none, true, null, topOptions);\n        this.nodes = [topContext];\n        this.find = options.findPositions;\n        this.needsBlock = false;\n    }\n    get top() {\n        return this.nodes[this.open];\n    }\n    // Add a DOM node to the content. Text is inserted as text node,\n    // otherwise, the node is passed to `addElement` or, if it has a\n    // `style` attribute, `addElementWithStyles`.\n    addDOM(dom, marks) {\n        if (dom.nodeType == 3)\n            this.addTextNode(dom, marks);\n        else if (dom.nodeType == 1)\n            this.addElement(dom, marks);\n    }\n    addTextNode(dom, marks) {\n        let value = dom.nodeValue;\n        let top = this.top, preserveWS = (top.options & OPT_PRESERVE_WS_FULL) ? \"full\"\n            : this.localPreserveWS || (top.options & OPT_PRESERVE_WS) > 0;\n        if (preserveWS === \"full\" ||\n            top.inlineContext(dom) ||\n            /[^ \\t\\r\\n\\u000c]/.test(value)) {\n            if (!preserveWS) {\n                value = value.replace(/[ \\t\\r\\n\\u000c]+/g, \" \");\n                // If this starts with whitespace, and there is no node before it, or\n                // a hard break, or a text node that ends with whitespace, strip the\n                // leading space.\n                if (/^[ \\t\\r\\n\\u000c]/.test(value) && this.open == this.nodes.length - 1) {\n                    let nodeBefore = top.content[top.content.length - 1];\n                    let domNodeBefore = dom.previousSibling;\n                    if (!nodeBefore ||\n                        (domNodeBefore && domNodeBefore.nodeName == 'BR') ||\n                        (nodeBefore.isText && /[ \\t\\r\\n\\u000c]$/.test(nodeBefore.text)))\n                        value = value.slice(1);\n                }\n            }\n            else if (preserveWS !== \"full\") {\n                value = value.replace(/\\r?\\n|\\r/g, \" \");\n            }\n            else {\n                value = value.replace(/\\r\\n?/g, \"\\n\");\n            }\n            if (value)\n                this.insertNode(this.parser.schema.text(value), marks);\n            this.findInText(dom);\n        }\n        else {\n            this.findInside(dom);\n        }\n    }\n    // Try to find a handler for the given tag and use that to parse. If\n    // none is found, the element's content nodes are added directly.\n    addElement(dom, marks, matchAfter) {\n        let outerWS = this.localPreserveWS, top = this.top;\n        if (dom.tagName == \"PRE\" || /pre/.test(dom.style && dom.style.whiteSpace))\n            this.localPreserveWS = true;\n        let name = dom.nodeName.toLowerCase(), ruleID;\n        if (listTags.hasOwnProperty(name) && this.parser.normalizeLists)\n            normalizeList(dom);\n        let rule = (this.options.ruleFromNode && this.options.ruleFromNode(dom)) ||\n            (ruleID = this.parser.matchTag(dom, this, matchAfter));\n        out: if (rule ? rule.ignore : ignoreTags.hasOwnProperty(name)) {\n            this.findInside(dom);\n            this.ignoreFallback(dom, marks);\n        }\n        else if (!rule || rule.skip || rule.closeParent) {\n            if (rule && rule.closeParent)\n                this.open = Math.max(0, this.open - 1);\n            else if (rule && rule.skip.nodeType)\n                dom = rule.skip;\n            let sync, oldNeedsBlock = this.needsBlock;\n            if (blockTags.hasOwnProperty(name)) {\n                if (top.content.length && top.content[0].isInline && this.open) {\n                    this.open--;\n                    top = this.top;\n                }\n                sync = true;\n                if (!top.type)\n                    this.needsBlock = true;\n            }\n            else if (!dom.firstChild) {\n                this.leafFallback(dom, marks);\n                break out;\n            }\n            let innerMarks = rule && rule.skip ? marks : this.readStyles(dom, marks);\n            if (innerMarks)\n                this.addAll(dom, innerMarks);\n            if (sync)\n                this.sync(top);\n            this.needsBlock = oldNeedsBlock;\n        }\n        else {\n            let innerMarks = this.readStyles(dom, marks);\n            if (innerMarks)\n                this.addElementByRule(dom, rule, innerMarks, rule.consuming === false ? ruleID : undefined);\n        }\n        this.localPreserveWS = outerWS;\n    }\n    // Called for leaf DOM nodes that would otherwise be ignored\n    leafFallback(dom, marks) {\n        if (dom.nodeName == \"BR\" && this.top.type && this.top.type.inlineContent)\n            this.addTextNode(dom.ownerDocument.createTextNode(\"\\n\"), marks);\n    }\n    // Called for ignored nodes\n    ignoreFallback(dom, marks) {\n        // Ignored BR nodes should at least create an inline context\n        if (dom.nodeName == \"BR\" && (!this.top.type || !this.top.type.inlineContent))\n            this.findPlace(this.parser.schema.text(\"-\"), marks);\n    }\n    // Run any style parser associated with the node's styles. Either\n    // return an updated array of marks, or null to indicate some of the\n    // styles had a rule with `ignore` set.\n    readStyles(dom, marks) {\n        let styles = dom.style;\n        // Because many properties will only show up in 'normalized' form\n        // in `style.item` (i.e. text-decoration becomes\n        // text-decoration-line, text-decoration-color, etc), we directly\n        // query the styles mentioned in our rules instead of iterating\n        // over the items.\n        if (styles && styles.length)\n            for (let i = 0; i < this.parser.matchedStyles.length; i++) {\n                let name = this.parser.matchedStyles[i], value = styles.getPropertyValue(name);\n                if (value)\n                    for (let after = undefined;;) {\n                        let rule = this.parser.matchStyle(name, value, this, after);\n                        if (!rule)\n                            break;\n                        if (rule.ignore)\n                            return null;\n                        if (rule.clearMark)\n                            marks = marks.filter(m => !rule.clearMark(m));\n                        else\n                            marks = marks.concat(this.parser.schema.marks[rule.mark].create(rule.attrs));\n                        if (rule.consuming === false)\n                            after = rule;\n                        else\n                            break;\n                    }\n            }\n        return marks;\n    }\n    // Look up a handler for the given node. If none are found, return\n    // false. Otherwise, apply it, use its return value to drive the way\n    // the node's content is wrapped, and return true.\n    addElementByRule(dom, rule, marks, continueAfter) {\n        let sync, nodeType;\n        if (rule.node) {\n            nodeType = this.parser.schema.nodes[rule.node];\n            if (!nodeType.isLeaf) {\n                let inner = this.enter(nodeType, rule.attrs || null, marks, rule.preserveWhitespace);\n                if (inner) {\n                    sync = true;\n                    marks = inner;\n                }\n            }\n            else if (!this.insertNode(nodeType.create(rule.attrs), marks)) {\n                this.leafFallback(dom, marks);\n            }\n        }\n        else {\n            let markType = this.parser.schema.marks[rule.mark];\n            marks = marks.concat(markType.create(rule.attrs));\n        }\n        let startIn = this.top;\n        if (nodeType && nodeType.isLeaf) {\n            this.findInside(dom);\n        }\n        else if (continueAfter) {\n            this.addElement(dom, marks, continueAfter);\n        }\n        else if (rule.getContent) {\n            this.findInside(dom);\n            rule.getContent(dom, this.parser.schema).forEach(node => this.insertNode(node, marks));\n        }\n        else {\n            let contentDOM = dom;\n            if (typeof rule.contentElement == \"string\")\n                contentDOM = dom.querySelector(rule.contentElement);\n            else if (typeof rule.contentElement == \"function\")\n                contentDOM = rule.contentElement(dom);\n            else if (rule.contentElement)\n                contentDOM = rule.contentElement;\n            this.findAround(dom, contentDOM, true);\n            this.addAll(contentDOM, marks);\n            this.findAround(dom, contentDOM, false);\n        }\n        if (sync && this.sync(startIn))\n            this.open--;\n    }\n    // Add all child nodes between `startIndex` and `endIndex` (or the\n    // whole node, if not given). If `sync` is passed, use it to\n    // synchronize after every block element.\n    addAll(parent, marks, startIndex, endIndex) {\n        let index = startIndex || 0;\n        for (let dom = startIndex ? parent.childNodes[startIndex] : parent.firstChild, end = endIndex == null ? null : parent.childNodes[endIndex]; dom != end; dom = dom.nextSibling, ++index) {\n            this.findAtPoint(parent, index);\n            this.addDOM(dom, marks);\n        }\n        this.findAtPoint(parent, index);\n    }\n    // Try to find a way to fit the given node type into the current\n    // context. May add intermediate wrappers and/or leave non-solid\n    // nodes that we're in.\n    findPlace(node, marks) {\n        let route, sync;\n        for (let depth = this.open; depth >= 0; depth--) {\n            let cx = this.nodes[depth];\n            let found = cx.findWrapping(node);\n            if (found && (!route || route.length > found.length)) {\n                route = found;\n                sync = cx;\n                if (!found.length)\n                    break;\n            }\n            if (cx.solid)\n                break;\n        }\n        if (!route)\n            return null;\n        this.sync(sync);\n        for (let i = 0; i < route.length; i++)\n            marks = this.enterInner(route[i], null, marks, false);\n        return marks;\n    }\n    // Try to insert the given node, adjusting the context when needed.\n    insertNode(node, marks) {\n        if (node.isInline && this.needsBlock && !this.top.type) {\n            let block = this.textblockFromContext();\n            if (block)\n                marks = this.enterInner(block, null, marks);\n        }\n        let innerMarks = this.findPlace(node, marks);\n        if (innerMarks) {\n            this.closeExtra();\n            let top = this.top;\n            if (top.match)\n                top.match = top.match.matchType(node.type);\n            let nodeMarks = Mark.none;\n            for (let m of innerMarks.concat(node.marks))\n                if (top.type ? top.type.allowsMarkType(m.type) : markMayApply(m.type, node.type))\n                    nodeMarks = m.addToSet(nodeMarks);\n            top.content.push(node.mark(nodeMarks));\n            return true;\n        }\n        return false;\n    }\n    // Try to start a node of the given type, adjusting the context when\n    // necessary.\n    enter(type, attrs, marks, preserveWS) {\n        let innerMarks = this.findPlace(type.create(attrs), marks);\n        if (innerMarks)\n            innerMarks = this.enterInner(type, attrs, marks, true, preserveWS);\n        return innerMarks;\n    }\n    // Open a node of the given type\n    enterInner(type, attrs, marks, solid = false, preserveWS) {\n        this.closeExtra();\n        let top = this.top;\n        top.match = top.match && top.match.matchType(type);\n        let options = wsOptionsFor(type, preserveWS, top.options);\n        if ((top.options & OPT_OPEN_LEFT) && top.content.length == 0)\n            options |= OPT_OPEN_LEFT;\n        let applyMarks = Mark.none;\n        marks = marks.filter(m => {\n            if (top.type ? top.type.allowsMarkType(m.type) : markMayApply(m.type, type)) {\n                applyMarks = m.addToSet(applyMarks);\n                return false;\n            }\n            return true;\n        });\n        this.nodes.push(new NodeContext(type, attrs, applyMarks, solid, null, options));\n        this.open++;\n        return marks;\n    }\n    // Make sure all nodes above this.open are finished and added to\n    // their parents\n    closeExtra(openEnd = false) {\n        let i = this.nodes.length - 1;\n        if (i > this.open) {\n            for (; i > this.open; i--)\n                this.nodes[i - 1].content.push(this.nodes[i].finish(openEnd));\n            this.nodes.length = this.open + 1;\n        }\n    }\n    finish() {\n        this.open = 0;\n        this.closeExtra(this.isOpen);\n        return this.nodes[0].finish(!!(this.isOpen || this.options.topOpen));\n    }\n    sync(to) {\n        for (let i = this.open; i >= 0; i--) {\n            if (this.nodes[i] == to) {\n                this.open = i;\n                return true;\n            }\n            else if (this.localPreserveWS) {\n                this.nodes[i].options |= OPT_PRESERVE_WS;\n            }\n        }\n        return false;\n    }\n    get currentPos() {\n        this.closeExtra();\n        let pos = 0;\n        for (let i = this.open; i >= 0; i--) {\n            let content = this.nodes[i].content;\n            for (let j = content.length - 1; j >= 0; j--)\n                pos += content[j].nodeSize;\n            if (i)\n                pos++;\n        }\n        return pos;\n    }\n    findAtPoint(parent, offset) {\n        if (this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].node == parent && this.find[i].offset == offset)\n                    this.find[i].pos = this.currentPos;\n            }\n    }\n    findInside(parent) {\n        if (this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].pos == null && parent.nodeType == 1 && parent.contains(this.find[i].node))\n                    this.find[i].pos = this.currentPos;\n            }\n    }\n    findAround(parent, content, before) {\n        if (parent != content && this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].pos == null && parent.nodeType == 1 && parent.contains(this.find[i].node)) {\n                    let pos = content.compareDocumentPosition(this.find[i].node);\n                    if (pos & (before ? 2 : 4))\n                        this.find[i].pos = this.currentPos;\n                }\n            }\n    }\n    findInText(textNode) {\n        if (this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].node == textNode)\n                    this.find[i].pos = this.currentPos - (textNode.nodeValue.length - this.find[i].offset);\n            }\n    }\n    // Determines whether the given context string matches this context.\n    matchesContext(context) {\n        if (context.indexOf(\"|\") > -1)\n            return context.split(/\\s*\\|\\s*/).some(this.matchesContext, this);\n        let parts = context.split(\"/\");\n        let option = this.options.context;\n        let useRoot = !this.isOpen && (!option || option.parent.type == this.nodes[0].type);\n        let minDepth = -(option ? option.depth + 1 : 0) + (useRoot ? 0 : 1);\n        let match = (i, depth) => {\n            for (; i >= 0; i--) {\n                let part = parts[i];\n                if (part == \"\") {\n                    if (i == parts.length - 1 || i == 0)\n                        continue;\n                    for (; depth >= minDepth; depth--)\n                        if (match(i - 1, depth))\n                            return true;\n                    return false;\n                }\n                else {\n                    let next = depth > 0 || (depth == 0 && useRoot) ? this.nodes[depth].type\n                        : option && depth >= minDepth ? option.node(depth - minDepth).type\n                            : null;\n                    if (!next || (next.name != part && !next.isInGroup(part)))\n                        return false;\n                    depth--;\n                }\n            }\n            return true;\n        };\n        return match(parts.length - 1, this.open);\n    }\n    textblockFromContext() {\n        let $context = this.options.context;\n        if ($context)\n            for (let d = $context.depth; d >= 0; d--) {\n                let deflt = $context.node(d).contentMatchAt($context.indexAfter(d)).defaultType;\n                if (deflt && deflt.isTextblock && deflt.defaultAttrs)\n                    return deflt;\n            }\n        for (let name in this.parser.schema.nodes) {\n            let type = this.parser.schema.nodes[name];\n            if (type.isTextblock && type.defaultAttrs)\n                return type;\n        }\n    }\n}\n// Kludge to work around directly nested list nodes produced by some\n// tools and allowed by browsers to mean that the nested list is\n// actually part of the list item above it.\nfunction normalizeList(dom) {\n    for (let child = dom.firstChild, prevItem = null; child; child = child.nextSibling) {\n        let name = child.nodeType == 1 ? child.nodeName.toLowerCase() : null;\n        if (name && listTags.hasOwnProperty(name) && prevItem) {\n            prevItem.appendChild(child);\n            child = prevItem;\n        }\n        else if (name == \"li\") {\n            prevItem = child;\n        }\n        else if (name) {\n            prevItem = null;\n        }\n    }\n}\n// Apply a CSS selector.\nfunction matches(dom, selector) {\n    return (dom.matches || dom.msMatchesSelector || dom.webkitMatchesSelector || dom.mozMatchesSelector).call(dom, selector);\n}\nfunction copy(obj) {\n    let copy = {};\n    for (let prop in obj)\n        copy[prop] = obj[prop];\n    return copy;\n}\n// Used when finding a mark at the top level of a fragment parse.\n// Checks whether it would be reasonable to apply a given mark type to\n// a given node, by looking at the way the mark occurs in the schema.\nfunction markMayApply(markType, nodeType) {\n    let nodes = nodeType.schema.nodes;\n    for (let name in nodes) {\n        let parent = nodes[name];\n        if (!parent.allowsMarkType(markType))\n            continue;\n        let seen = [], scan = (match) => {\n            seen.push(match);\n            for (let i = 0; i < match.edgeCount; i++) {\n                let { type, next } = match.edge(i);\n                if (type == nodeType)\n                    return true;\n                if (seen.indexOf(next) < 0 && scan(next))\n                    return true;\n            }\n        };\n        if (scan(parent.contentMatch))\n            return true;\n    }\n}\n\n/**\nA DOM serializer knows how to convert ProseMirror nodes and\nmarks of various types to DOM nodes.\n*/\nclass DOMSerializer {\n    /**\n    Create a serializer. `nodes` should map node names to functions\n    that take a node and return a description of the corresponding\n    DOM. `marks` does the same for mark names, but also gets an\n    argument that tells it whether the mark's content is block or\n    inline content (for typical use, it'll always be inline). A mark\n    serializer may be `null` to indicate that marks of that type\n    should not be serialized.\n    */\n    constructor(\n    /**\n    The node serialization functions.\n    */\n    nodes, \n    /**\n    The mark serialization functions.\n    */\n    marks) {\n        this.nodes = nodes;\n        this.marks = marks;\n    }\n    /**\n    Serialize the content of this fragment to a DOM fragment. When\n    not in the browser, the `document` option, containing a DOM\n    document, should be passed so that the serializer can create\n    nodes.\n    */\n    serializeFragment(fragment, options = {}, target) {\n        if (!target)\n            target = doc(options).createDocumentFragment();\n        let top = target, active = [];\n        fragment.forEach(node => {\n            if (active.length || node.marks.length) {\n                let keep = 0, rendered = 0;\n                while (keep < active.length && rendered < node.marks.length) {\n                    let next = node.marks[rendered];\n                    if (!this.marks[next.type.name]) {\n                        rendered++;\n                        continue;\n                    }\n                    if (!next.eq(active[keep][0]) || next.type.spec.spanning === false)\n                        break;\n                    keep++;\n                    rendered++;\n                }\n                while (keep < active.length)\n                    top = active.pop()[1];\n                while (rendered < node.marks.length) {\n                    let add = node.marks[rendered++];\n                    let markDOM = this.serializeMark(add, node.isInline, options);\n                    if (markDOM) {\n                        active.push([add, top]);\n                        top.appendChild(markDOM.dom);\n                        top = markDOM.contentDOM || markDOM.dom;\n                    }\n                }\n            }\n            top.appendChild(this.serializeNodeInner(node, options));\n        });\n        return target;\n    }\n    /**\n    @internal\n    */\n    serializeNodeInner(node, options) {\n        let { dom, contentDOM } = renderSpec(doc(options), this.nodes[node.type.name](node), null, node.attrs);\n        if (contentDOM) {\n            if (node.isLeaf)\n                throw new RangeError(\"Content hole not allowed in a leaf node spec\");\n            this.serializeFragment(node.content, options, contentDOM);\n        }\n        return dom;\n    }\n    /**\n    Serialize this node to a DOM node. This can be useful when you\n    need to serialize a part of a document, as opposed to the whole\n    document. To serialize a whole document, use\n    [`serializeFragment`](https://prosemirror.net/docs/ref/#model.DOMSerializer.serializeFragment) on\n    its [content](https://prosemirror.net/docs/ref/#model.Node.content).\n    */\n    serializeNode(node, options = {}) {\n        let dom = this.serializeNodeInner(node, options);\n        for (let i = node.marks.length - 1; i >= 0; i--) {\n            let wrap = this.serializeMark(node.marks[i], node.isInline, options);\n            if (wrap) {\n                (wrap.contentDOM || wrap.dom).appendChild(dom);\n                dom = wrap.dom;\n            }\n        }\n        return dom;\n    }\n    /**\n    @internal\n    */\n    serializeMark(mark, inline, options = {}) {\n        let toDOM = this.marks[mark.type.name];\n        return toDOM && renderSpec(doc(options), toDOM(mark, inline), null, mark.attrs);\n    }\n    static renderSpec(doc, structure, xmlNS = null, blockArraysIn) {\n        return renderSpec(doc, structure, xmlNS, blockArraysIn);\n    }\n    /**\n    Build a serializer using the [`toDOM`](https://prosemirror.net/docs/ref/#model.NodeSpec.toDOM)\n    properties in a schema's node and mark specs.\n    */\n    static fromSchema(schema) {\n        return schema.cached.domSerializer ||\n            (schema.cached.domSerializer = new DOMSerializer(this.nodesFromSchema(schema), this.marksFromSchema(schema)));\n    }\n    /**\n    Gather the serializers in a schema's node specs into an object.\n    This can be useful as a base to build a custom serializer from.\n    */\n    static nodesFromSchema(schema) {\n        let result = gatherToDOM(schema.nodes);\n        if (!result.text)\n            result.text = node => node.text;\n        return result;\n    }\n    /**\n    Gather the serializers in a schema's mark specs into an object.\n    */\n    static marksFromSchema(schema) {\n        return gatherToDOM(schema.marks);\n    }\n}\nfunction gatherToDOM(obj) {\n    let result = {};\n    for (let name in obj) {\n        let toDOM = obj[name].spec.toDOM;\n        if (toDOM)\n            result[name] = toDOM;\n    }\n    return result;\n}\nfunction doc(options) {\n    return options.document || window.document;\n}\nconst suspiciousAttributeCache = new WeakMap();\nfunction suspiciousAttributes(attrs) {\n    let value = suspiciousAttributeCache.get(attrs);\n    if (value === undefined)\n        suspiciousAttributeCache.set(attrs, value = suspiciousAttributesInner(attrs));\n    return value;\n}\nfunction suspiciousAttributesInner(attrs) {\n    let result = null;\n    function scan(value) {\n        if (value && typeof value == \"object\") {\n            if (Array.isArray(value)) {\n                if (typeof value[0] == \"string\") {\n                    if (!result)\n                        result = [];\n                    result.push(value);\n                }\n                else {\n                    for (let i = 0; i < value.length; i++)\n                        scan(value[i]);\n                }\n            }\n            else {\n                for (let prop in value)\n                    scan(value[prop]);\n            }\n        }\n    }\n    scan(attrs);\n    return result;\n}\nfunction renderSpec(doc, structure, xmlNS, blockArraysIn) {\n    if (typeof structure == \"string\")\n        return { dom: doc.createTextNode(structure) };\n    if (structure.nodeType != null)\n        return { dom: structure };\n    if (structure.dom && structure.dom.nodeType != null)\n        return structure;\n    let tagName = structure[0], suspicious;\n    if (typeof tagName != \"string\")\n        throw new RangeError(\"Invalid array passed to renderSpec\");\n    if (blockArraysIn && (suspicious = suspiciousAttributes(blockArraysIn)) &&\n        suspicious.indexOf(structure) > -1)\n        throw new RangeError(\"Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.\");\n    let space = tagName.indexOf(\" \");\n    if (space > 0) {\n        xmlNS = tagName.slice(0, space);\n        tagName = tagName.slice(space + 1);\n    }\n    let contentDOM;\n    let dom = (xmlNS ? doc.createElementNS(xmlNS, tagName) : doc.createElement(tagName));\n    let attrs = structure[1], start = 1;\n    if (attrs && typeof attrs == \"object\" && attrs.nodeType == null && !Array.isArray(attrs)) {\n        start = 2;\n        for (let name in attrs)\n            if (attrs[name] != null) {\n                let space = name.indexOf(\" \");\n                if (space > 0)\n                    dom.setAttributeNS(name.slice(0, space), name.slice(space + 1), attrs[name]);\n                else\n                    dom.setAttribute(name, attrs[name]);\n            }\n    }\n    for (let i = start; i < structure.length; i++) {\n        let child = structure[i];\n        if (child === 0) {\n            if (i < structure.length - 1 || i > start)\n                throw new RangeError(\"Content hole must be the only child of its parent node\");\n            return { dom, contentDOM: dom };\n        }\n        else {\n            let { dom: inner, contentDOM: innerContent } = renderSpec(doc, child, xmlNS, blockArraysIn);\n            dom.appendChild(inner);\n            if (innerContent) {\n                if (contentDOM)\n                    throw new RangeError(\"Multiple content holes\");\n                contentDOM = innerContent;\n            }\n        }\n    }\n    return { dom, contentDOM };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/prosemirror-model/dist/index.js\n");

/***/ })

};
;