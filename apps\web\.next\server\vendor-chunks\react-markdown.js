"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-markdown";
exports.ids = ["vendor-chunks/react-markdown"];
exports.modules = {

/***/ "(ssr)/../../node_modules/react-markdown/lib/index.js":
/*!******************************************************!*\
  !*** ../../node_modules/react-markdown/lib/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Markdown: () => (/* binding */ Markdown),\n/* harmony export */   defaultUrlTransform: () => (/* binding */ defaultUrlTransform)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! devlop */ \"(ssr)/../../node_modules/devlop/lib/development.js\");\n/* harmony import */ var hast_util_to_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! hast-util-to-jsx-runtime */ \"(ssr)/../../node_modules/hast-util-to-jsx-runtime/lib/index.js\");\n/* harmony import */ var html_url_attributes__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! html-url-attributes */ \"(ssr)/../../node_modules/html-url-attributes/lib/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var remark_parse__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! remark-parse */ \"(ssr)/../../node_modules/remark-parse/lib/index.js\");\n/* harmony import */ var remark_rehype__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! remark-rehype */ \"(ssr)/../../node_modules/react-markdown/node_modules/remark-rehype/lib/index.js\");\n/* harmony import */ var unified__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unified */ \"(ssr)/../../node_modules/unified/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/../../node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var vfile__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! vfile */ \"(ssr)/../../node_modules/vfile/lib/index.js\");\n/**\n * @import {Element, ElementContent, Nodes, Parents, Root} from 'hast'\n * @import {ComponentProps, ElementType, ReactElement} from 'react'\n * @import {Options as RemarkRehypeOptions} from 'remark-rehype'\n * @import {BuildVisitor} from 'unist-util-visit'\n * @import {PluggableList} from 'unified'\n */\n\n/**\n * @callback AllowElement\n *   Filter elements.\n * @param {Readonly<Element>} element\n *   Element to check.\n * @param {number} index\n *   Index of `element` in `parent`.\n * @param {Readonly<Parents> | undefined} parent\n *   Parent of `element`.\n * @returns {boolean | null | undefined}\n *   Whether to allow `element` (default: `false`).\n */\n\n/**\n * @typedef ExtraProps\n *   Extra fields we pass.\n * @property {Element | undefined} [node]\n *   passed when `passNode` is on.\n */\n\n/**\n * @typedef {{\n *   [Key in Extract<ElementType, string>]?: ElementType<ComponentProps<Key> & ExtraProps>\n * }} Components\n *   Map tag names to components.\n */\n\n/**\n * @typedef Deprecation\n *   Deprecation.\n * @property {string} from\n *   Old field.\n * @property {string} id\n *   ID in readme.\n * @property {keyof Options} [to]\n *   New field.\n */\n\n/**\n * @typedef Options\n *   Configuration.\n * @property {AllowElement | null | undefined} [allowElement]\n *   Filter elements (optional);\n *   `allowedElements` / `disallowedElements` is used first.\n * @property {ReadonlyArray<string> | null | undefined} [allowedElements]\n *   Tag names to allow (default: all tag names);\n *   cannot combine w/ `disallowedElements`.\n * @property {string | null | undefined} [children]\n *   Markdown.\n * @property {string | null | undefined} [className]\n *   Wrap in a `div` with this class name.\n * @property {Components | null | undefined} [components]\n *   Map tag names to components.\n * @property {ReadonlyArray<string> | null | undefined} [disallowedElements]\n *   Tag names to disallow (default: `[]`);\n *   cannot combine w/ `allowedElements`.\n * @property {PluggableList | null | undefined} [rehypePlugins]\n *   List of rehype plugins to use.\n * @property {PluggableList | null | undefined} [remarkPlugins]\n *   List of remark plugins to use.\n * @property {Readonly<RemarkRehypeOptions> | null | undefined} [remarkRehypeOptions]\n *   Options to pass through to `remark-rehype`.\n * @property {boolean | null | undefined} [skipHtml=false]\n *   Ignore HTML in markdown completely (default: `false`).\n * @property {boolean | null | undefined} [unwrapDisallowed=false]\n *   Extract (unwrap) what’s in disallowed elements (default: `false`);\n *   normally when say `strong` is not allowed, it and it’s children are dropped,\n *   with `unwrapDisallowed` the element itself is replaced by its children.\n * @property {UrlTransform | null | undefined} [urlTransform]\n *   Change URLs (default: `defaultUrlTransform`)\n */\n\n/**\n * @callback UrlTransform\n *   Transform all URLs.\n * @param {string} url\n *   URL.\n * @param {string} key\n *   Property name (example: `'href'`).\n * @param {Readonly<Element>} node\n *   Node.\n * @returns {string | null | undefined}\n *   Transformed URL (optional).\n */\n\n\n\n\n\n\n\n\n\n\n\nconst changelog =\n  'https://github.com/remarkjs/react-markdown/blob/main/changelog.md'\n\n/** @type {PluggableList} */\nconst emptyPlugins = []\n/** @type {Readonly<RemarkRehypeOptions>} */\nconst emptyRemarkRehypeOptions = {allowDangerousHtml: true}\nconst safeProtocol = /^(https?|ircs?|mailto|xmpp)$/i\n\n// Mutable because we `delete` any time it’s used and a message is sent.\n/** @type {ReadonlyArray<Readonly<Deprecation>>} */\nconst deprecations = [\n  {from: 'astPlugins', id: 'remove-buggy-html-in-markdown-parser'},\n  {from: 'allowDangerousHtml', id: 'remove-buggy-html-in-markdown-parser'},\n  {\n    from: 'allowNode',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'allowElement'\n  },\n  {\n    from: 'allowedTypes',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'allowedElements'\n  },\n  {\n    from: 'disallowedTypes',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'disallowedElements'\n  },\n  {from: 'escapeHtml', id: 'remove-buggy-html-in-markdown-parser'},\n  {from: 'includeElementIndex', id: '#remove-includeelementindex'},\n  {\n    from: 'includeNodeIndex',\n    id: 'change-includenodeindex-to-includeelementindex'\n  },\n  {from: 'linkTarget', id: 'remove-linktarget'},\n  {from: 'plugins', id: 'change-plugins-to-remarkplugins', to: 'remarkPlugins'},\n  {from: 'rawSourcePos', id: '#remove-rawsourcepos'},\n  {from: 'renderers', id: 'change-renderers-to-components', to: 'components'},\n  {from: 'source', id: 'change-source-to-children', to: 'children'},\n  {from: 'sourcePos', id: '#remove-sourcepos'},\n  {from: 'transformImageUri', id: '#add-urltransform', to: 'urlTransform'},\n  {from: 'transformLinkUri', id: '#add-urltransform', to: 'urlTransform'}\n]\n\n/**\n * Component to render markdown.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */\nfunction Markdown(options) {\n  const allowedElements = options.allowedElements\n  const allowElement = options.allowElement\n  const children = options.children || ''\n  const className = options.className\n  const components = options.components\n  const disallowedElements = options.disallowedElements\n  const rehypePlugins = options.rehypePlugins || emptyPlugins\n  const remarkPlugins = options.remarkPlugins || emptyPlugins\n  const remarkRehypeOptions = options.remarkRehypeOptions\n    ? {...options.remarkRehypeOptions, ...emptyRemarkRehypeOptions}\n    : emptyRemarkRehypeOptions\n  const skipHtml = options.skipHtml\n  const unwrapDisallowed = options.unwrapDisallowed\n  const urlTransform = options.urlTransform || defaultUrlTransform\n\n  const processor = (0,unified__WEBPACK_IMPORTED_MODULE_1__.unified)()\n    .use(remark_parse__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n    .use(remarkPlugins)\n    .use(remark_rehype__WEBPACK_IMPORTED_MODULE_3__[\"default\"], remarkRehypeOptions)\n    .use(rehypePlugins)\n\n  const file = new vfile__WEBPACK_IMPORTED_MODULE_4__.VFile()\n\n  if (typeof children === 'string') {\n    file.value = children\n  } else {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_5__.unreachable)(\n      'Unexpected value `' +\n        children +\n        '` for `children` prop, expected `string`'\n    )\n  }\n\n  if (allowedElements && disallowedElements) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_5__.unreachable)(\n      'Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other'\n    )\n  }\n\n  for (const deprecation of deprecations) {\n    if (Object.hasOwn(options, deprecation.from)) {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_5__.unreachable)(\n        'Unexpected `' +\n          deprecation.from +\n          '` prop, ' +\n          (deprecation.to\n            ? 'use `' + deprecation.to + '` instead'\n            : 'remove it') +\n          ' (see <' +\n          changelog +\n          '#' +\n          deprecation.id +\n          '> for more info)'\n      )\n    }\n  }\n\n  const mdastTree = processor.parse(file)\n  /** @type {Nodes} */\n  let hastTree = processor.runSync(mdastTree, file)\n\n  // Wrap in `div` if there’s a class name.\n  if (className) {\n    hastTree = {\n      type: 'element',\n      tagName: 'div',\n      properties: {className},\n      // Assume no doctypes.\n      children: /** @type {Array<ElementContent>} */ (\n        hastTree.type === 'root' ? hastTree.children : [hastTree]\n      )\n    }\n  }\n\n  (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_6__.visit)(hastTree, transform)\n\n  return (0,hast_util_to_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.toJsxRuntime)(hastTree, {\n    Fragment: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment,\n    // @ts-expect-error\n    // React components are allowed to return numbers,\n    // but not according to the types in hast-util-to-jsx-runtime\n    components,\n    ignoreInvalidStyle: true,\n    jsx: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx,\n    jsxs: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs,\n    passKeys: true,\n    passNode: true\n  })\n\n  /** @type {BuildVisitor<Root>} */\n  function transform(node, index, parent) {\n    if (node.type === 'raw' && parent && typeof index === 'number') {\n      if (skipHtml) {\n        parent.children.splice(index, 1)\n      } else {\n        parent.children[index] = {type: 'text', value: node.value}\n      }\n\n      return index\n    }\n\n    if (node.type === 'element') {\n      /** @type {string} */\n      let key\n\n      for (key in html_url_attributes__WEBPACK_IMPORTED_MODULE_8__.urlAttributes) {\n        if (\n          Object.hasOwn(html_url_attributes__WEBPACK_IMPORTED_MODULE_8__.urlAttributes, key) &&\n          Object.hasOwn(node.properties, key)\n        ) {\n          const value = node.properties[key]\n          const test = html_url_attributes__WEBPACK_IMPORTED_MODULE_8__.urlAttributes[key]\n          if (test === null || test.includes(node.tagName)) {\n            node.properties[key] = urlTransform(String(value || ''), key, node)\n          }\n        }\n      }\n    }\n\n    if (node.type === 'element') {\n      let remove = allowedElements\n        ? !allowedElements.includes(node.tagName)\n        : disallowedElements\n          ? disallowedElements.includes(node.tagName)\n          : false\n\n      if (!remove && allowElement && typeof index === 'number') {\n        remove = !allowElement(node, index, parent)\n      }\n\n      if (remove && parent && typeof index === 'number') {\n        if (unwrapDisallowed && node.children) {\n          parent.children.splice(index, 1, ...node.children)\n        } else {\n          parent.children.splice(index, 1)\n        }\n\n        return index\n      }\n    }\n  }\n}\n\n/**\n * Make a URL safe.\n *\n * @satisfies {UrlTransform}\n * @param {string} value\n *   URL.\n * @returns {string}\n *   Safe URL.\n */\nfunction defaultUrlTransform(value) {\n  // Same as:\n  // <https://github.com/micromark/micromark/blob/929275e/packages/micromark-util-sanitize-uri/dev/index.js#L34>\n  // But without the `encode` part.\n  const colon = value.indexOf(':')\n  const questionMark = value.indexOf('?')\n  const numberSign = value.indexOf('#')\n  const slash = value.indexOf('/')\n\n  if (\n    // If there is no protocol, it’s relative.\n    colon === -1 ||\n    // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    (slash !== -1 && colon > slash) ||\n    (questionMark !== -1 && colon > questionMark) ||\n    (numberSign !== -1 && colon > numberSign) ||\n    // It is a protocol, it should be allowed.\n    safeProtocol.test(value.slice(0, colon))\n  ) {\n    return value\n  }\n\n  return ''\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-markdown/lib/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-markdown/node_modules/remark-rehype/lib/index.js":
/*!*********************************************************************************!*\
  !*** ../../node_modules/react-markdown/node_modules/remark-rehype/lib/index.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ remarkRehype)\n/* harmony export */ });\n/* harmony import */ var mdast_util_to_hast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-to-hast */ \"(ssr)/../../node_modules/mdast-util-to-hast/lib/index.js\");\n// Include `data` fields in mdast and `raw` nodes in hast.\n/// <reference types=\"mdast-util-to-hast\" />\n\n/**\n * @import {Root as HastRoot} from 'hast'\n * @import {Root as MdastRoot} from 'mdast'\n * @import {Options as ToHastOptions} from 'mdast-util-to-hast'\n * @import {Processor} from 'unified'\n * @import {VFile} from 'vfile'\n */\n\n/**\n * @typedef {Omit<ToHastOptions, 'file'>} Options\n *\n * @callback TransformBridge\n *   Bridge-mode.\n *\n *   Runs the destination with the new hast tree.\n *   Discards result.\n * @param {MdastRoot} tree\n *   Tree.\n * @param {VFile} file\n *   File.\n * @returns {Promise<undefined>}\n *   Nothing.\n *\n * @callback TransformMutate\n *  Mutate-mode.\n *\n *  Further transformers run on the hast tree.\n * @param {MdastRoot} tree\n *   Tree.\n * @param {VFile} file\n *   File.\n * @returns {HastRoot}\n *   Tree (hast).\n */\n\n\n\n/**\n * Turn markdown into HTML.\n *\n * ##### Notes\n *\n * ###### Signature\n *\n * *   if a processor is given, runs the (rehype) plugins used on it with a\n *     hast tree, then discards the result (*bridge mode*)\n * *   otherwise, returns a hast tree, the plugins used after `remarkRehype`\n *     are rehype plugins (*mutate mode*)\n *\n * > 👉 **Note**: It’s highly unlikely that you want to pass a `processor`.\n *\n * ###### HTML\n *\n * Raw HTML is available in mdast as `html` nodes and can be embedded in hast\n * as semistandard `raw` nodes.\n * Most plugins ignore `raw` nodes but two notable ones don’t:\n *\n * *   `rehype-stringify` also has an option `allowDangerousHtml` which will\n *     output the raw HTML.\n *     This is typically discouraged as noted by the option name but is useful if\n *     you completely trust authors\n * *   `rehype-raw` can handle the raw embedded HTML strings by parsing them\n *     into standard hast nodes (`element`, `text`, etc).\n *     This is a heavy task as it needs a full HTML parser, but it is the only way\n *     to support untrusted content\n *\n * ###### Footnotes\n *\n * Many options supported here relate to footnotes.\n * Footnotes are not specified by CommonMark, which we follow by default.\n * They are supported by GitHub, so footnotes can be enabled in markdown with\n * `remark-gfm`.\n *\n * The options `footnoteBackLabel` and `footnoteLabel` define natural language\n * that explains footnotes, which is hidden for sighted users but shown to\n * assistive technology.\n * When your page is not in English, you must define translated values.\n *\n * Back references use ARIA attributes, but the section label itself uses a\n * heading that is hidden with an `sr-only` class.\n * To show it to sighted users, define different attributes in\n * `footnoteLabelProperties`.\n *\n * ###### Clobbering\n *\n * Footnotes introduces a problem, as it links footnote calls to footnote\n * definitions on the page through `id` attributes generated from user content,\n * which results in DOM clobbering.\n *\n * DOM clobbering is this:\n *\n * ```html\n * <p id=x></p>\n * <script>alert(x) // `x` now refers to the DOM `p#x` element</script>\n * ```\n *\n * Elements by their ID are made available by browsers on the `window` object,\n * which is a security risk.\n * Using a prefix solves this problem.\n *\n * More information on how to handle clobbering and the prefix is explained in\n * *Example: headings (DOM clobbering)* in `rehype-sanitize`.\n *\n * ###### Unknown nodes\n *\n * Unknown nodes are nodes with a type that isn’t in `handlers` or `passThrough`.\n * The default behavior for unknown nodes is:\n *\n * *   when the node has a `value` (and doesn’t have `data.hName`,\n *     `data.hProperties`, or `data.hChildren`, see later), create a hast `text`\n *     node\n * *   otherwise, create a `<div>` element (which could be changed with\n *     `data.hName`), with its children mapped from mdast to hast as well\n *\n * This behavior can be changed by passing an `unknownHandler`.\n *\n * @overload\n * @param {Processor} processor\n * @param {Readonly<Options> | null | undefined} [options]\n * @returns {TransformBridge}\n *\n * @overload\n * @param {Readonly<Options> | null | undefined} [options]\n * @returns {TransformMutate}\n *\n * @param {Readonly<Options> | Processor | null | undefined} [destination]\n *   Processor or configuration (optional).\n * @param {Readonly<Options> | null | undefined} [options]\n *   When a processor was given, configuration (optional).\n * @returns {TransformBridge | TransformMutate}\n *   Transform.\n */\nfunction remarkRehype(destination, options) {\n  if (destination && 'run' in destination) {\n    /**\n     * @type {TransformBridge}\n     */\n    return async function (tree, file) {\n      // Cast because root in -> root out.\n      const hastTree = /** @type {HastRoot} */ (\n        (0,mdast_util_to_hast__WEBPACK_IMPORTED_MODULE_0__.toHast)(tree, {file, ...options})\n      )\n      await destination.run(hastTree, file)\n    }\n  }\n\n  /**\n   * @type {TransformMutate}\n   */\n  return function (tree, file) {\n    // Cast because root in -> root out.\n    // To do: in the future, disallow ` || options` fallback.\n    // With `unified-engine`, `destination` can be `undefined` but\n    // `options` will be the file set.\n    // We should not pass that as `options`.\n    return /** @type {HastRoot} */ (\n      (0,mdast_util_to_hast__WEBPACK_IMPORTED_MODULE_0__.toHast)(tree, {file, ...(destination || options)})\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-markdown/node_modules/remark-rehype/lib/index.js\n");

/***/ })

};
;