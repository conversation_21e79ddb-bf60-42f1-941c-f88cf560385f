"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-resizable-panels";
exports.ids = ["vendor-chunks/react-resizable-panels"];
exports.modules = {

/***/ "(ssr)/../../node_modules/react-resizable-panels/dist/react-resizable-panels.development.node.esm.js":
/*!*****************************************************************************************************!*\
  !*** ../../node_modules/react-resizable-panels/dist/react-resizable-panels.development.node.esm.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Panel: () => (/* binding */ Panel),\n/* harmony export */   PanelGroup: () => (/* binding */ PanelGroup),\n/* harmony export */   PanelResizeHandle: () => (/* binding */ PanelResizeHandle),\n/* harmony export */   assert: () => (/* binding */ assert),\n/* harmony export */   disableGlobalCursorStyles: () => (/* binding */ disableGlobalCursorStyles),\n/* harmony export */   enableGlobalCursorStyles: () => (/* binding */ enableGlobalCursorStyles),\n/* harmony export */   getIntersectingRectangle: () => (/* binding */ getIntersectingRectangle),\n/* harmony export */   getPanelElement: () => (/* binding */ getPanelElement),\n/* harmony export */   getPanelElementsForGroup: () => (/* binding */ getPanelElementsForGroup),\n/* harmony export */   getPanelGroupElement: () => (/* binding */ getPanelGroupElement),\n/* harmony export */   getResizeHandleElement: () => (/* binding */ getResizeHandleElement),\n/* harmony export */   getResizeHandleElementIndex: () => (/* binding */ getResizeHandleElementIndex),\n/* harmony export */   getResizeHandleElementsForGroup: () => (/* binding */ getResizeHandleElementsForGroup),\n/* harmony export */   getResizeHandlePanelIds: () => (/* binding */ getResizeHandlePanelIds),\n/* harmony export */   intersects: () => (/* binding */ intersects),\n/* harmony export */   setNonce: () => (/* binding */ setNonce)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// This module exists to work around Webpack issue https://github.com/webpack/webpack/issues/14814\n\n// eslint-disable-next-line no-restricted-imports\n\nconst {\n  createElement,\n  createContext,\n  createRef,\n  forwardRef,\n  useCallback,\n  useContext,\n  useEffect,\n  useImperativeHandle,\n  useLayoutEffect,\n  useMemo,\n  useRef,\n  useState\n} = react__WEBPACK_IMPORTED_MODULE_0__;\n\n// `Math.random()` and `.slice(0, 5)` prevents bundlers from trying to `import { useId } from 'react'`\nconst useId = react__WEBPACK_IMPORTED_MODULE_0__[`useId${Math.random()}`.slice(0, 5)];\n\n// The \"contextmenu\" event is not supported as a PointerEvent in all browsers yet, so MouseEvent still need to be handled\n\nconst PanelGroupContext = createContext(null);\nPanelGroupContext.displayName = \"PanelGroupContext\";\n\nconst wrappedUseId = typeof useId === \"function\" ? useId : () => null;\nlet counter = 0;\nfunction useUniqueId(idFromParams = null) {\n  const idFromUseId = wrappedUseId();\n  const idRef = useRef(idFromParams || idFromUseId || null);\n  if (idRef.current === null) {\n    idRef.current = \"\" + counter++;\n  }\n  return idFromParams !== null && idFromParams !== void 0 ? idFromParams : idRef.current;\n}\n\nfunction PanelWithForwardedRef({\n  children,\n  className: classNameFromProps = \"\",\n  collapsedSize,\n  collapsible,\n  defaultSize,\n  forwardedRef,\n  id: idFromProps,\n  maxSize,\n  minSize,\n  onCollapse,\n  onExpand,\n  onResize,\n  order,\n  style: styleFromProps,\n  tagName: Type = \"div\",\n  ...rest\n}) {\n  const context = useContext(PanelGroupContext);\n  if (context === null) {\n    throw Error(`Panel components must be rendered within a PanelGroup container`);\n  }\n  const {\n    collapsePanel,\n    expandPanel,\n    getPanelSize,\n    getPanelStyle,\n    groupId,\n    isPanelCollapsed,\n    reevaluatePanelConstraints,\n    registerPanel,\n    resizePanel,\n    unregisterPanel\n  } = context;\n  const panelId = useUniqueId(idFromProps);\n  const panelDataRef = useRef({\n    callbacks: {\n      onCollapse,\n      onExpand,\n      onResize\n    },\n    constraints: {\n      collapsedSize,\n      collapsible,\n      defaultSize,\n      maxSize,\n      minSize\n    },\n    id: panelId,\n    idIsFromProps: idFromProps !== undefined,\n    order\n  });\n  const devWarningsRef = useRef({\n    didLogMissingDefaultSizeWarning: false\n  });\n\n  // Normally we wouldn't log a warning during render,\n  // but effects don't run on the server, so we can't do it there\n  {\n    if (!devWarningsRef.current.didLogMissingDefaultSizeWarning) {\n      if (defaultSize == null) {\n        devWarningsRef.current.didLogMissingDefaultSizeWarning = true;\n        console.warn(`WARNING: Panel defaultSize prop recommended to avoid layout shift after server rendering`);\n      }\n    }\n  }\n  useImperativeHandle(forwardedRef, () => ({\n    collapse: () => {\n      collapsePanel(panelDataRef.current);\n    },\n    expand: minSize => {\n      expandPanel(panelDataRef.current, minSize);\n    },\n    getId() {\n      return panelId;\n    },\n    getSize() {\n      return getPanelSize(panelDataRef.current);\n    },\n    isCollapsed() {\n      return isPanelCollapsed(panelDataRef.current);\n    },\n    isExpanded() {\n      return !isPanelCollapsed(panelDataRef.current);\n    },\n    resize: size => {\n      resizePanel(panelDataRef.current, size);\n    }\n  }), [collapsePanel, expandPanel, getPanelSize, isPanelCollapsed, panelId, resizePanel]);\n  const style = getPanelStyle(panelDataRef.current, defaultSize);\n  return createElement(Type, {\n    ...rest,\n    children,\n    className: classNameFromProps,\n    id: idFromProps,\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    // CSS selectors\n    \"data-panel\": \"\",\n    \"data-panel-collapsible\": collapsible || undefined,\n    \"data-panel-group-id\": groupId,\n    \"data-panel-id\": panelId,\n    \"data-panel-size\": parseFloat(\"\" + style.flexGrow).toFixed(1)\n  });\n}\nconst Panel = forwardRef((props, ref) => createElement(PanelWithForwardedRef, {\n  ...props,\n  forwardedRef: ref\n}));\nPanelWithForwardedRef.displayName = \"Panel\";\nPanel.displayName = \"forwardRef(Panel)\";\n\nlet nonce;\nfunction getNonce() {\n  return nonce;\n}\nfunction setNonce(value) {\n  nonce = value;\n}\n\nlet currentCursorStyle = null;\nlet enabled = true;\nlet styleElement = null;\nfunction disableGlobalCursorStyles() {\n  enabled = false;\n}\nfunction enableGlobalCursorStyles() {\n  enabled = true;\n}\nfunction getCursorStyle(state, constraintFlags) {\n  if (constraintFlags) {\n    const horizontalMin = (constraintFlags & EXCEEDED_HORIZONTAL_MIN) !== 0;\n    const horizontalMax = (constraintFlags & EXCEEDED_HORIZONTAL_MAX) !== 0;\n    const verticalMin = (constraintFlags & EXCEEDED_VERTICAL_MIN) !== 0;\n    const verticalMax = (constraintFlags & EXCEEDED_VERTICAL_MAX) !== 0;\n    if (horizontalMin) {\n      if (verticalMin) {\n        return \"se-resize\";\n      } else if (verticalMax) {\n        return \"ne-resize\";\n      } else {\n        return \"e-resize\";\n      }\n    } else if (horizontalMax) {\n      if (verticalMin) {\n        return \"sw-resize\";\n      } else if (verticalMax) {\n        return \"nw-resize\";\n      } else {\n        return \"w-resize\";\n      }\n    } else if (verticalMin) {\n      return \"s-resize\";\n    } else if (verticalMax) {\n      return \"n-resize\";\n    }\n  }\n  switch (state) {\n    case \"horizontal\":\n      return \"ew-resize\";\n    case \"intersection\":\n      return \"move\";\n    case \"vertical\":\n      return \"ns-resize\";\n  }\n}\nfunction resetGlobalCursorStyle() {\n  if (styleElement !== null) {\n    document.head.removeChild(styleElement);\n    currentCursorStyle = null;\n    styleElement = null;\n  }\n}\nfunction setGlobalCursorStyle(state, constraintFlags) {\n  if (!enabled) {\n    return;\n  }\n  const style = getCursorStyle(state, constraintFlags);\n  if (currentCursorStyle === style) {\n    return;\n  }\n  currentCursorStyle = style;\n  if (styleElement === null) {\n    styleElement = document.createElement(\"style\");\n    const nonce = getNonce();\n    if (nonce) {\n      styleElement.setAttribute(\"nonce\", nonce);\n    }\n    document.head.appendChild(styleElement);\n  }\n  styleElement.innerHTML = `*{cursor: ${style}!important;}`;\n}\n\nfunction isKeyDown(event) {\n  return event.type === \"keydown\";\n}\nfunction isPointerEvent(event) {\n  return event.type.startsWith(\"pointer\");\n}\nfunction isMouseEvent(event) {\n  return event.type.startsWith(\"mouse\");\n}\n\nfunction getResizeEventCoordinates(event) {\n  if (isPointerEvent(event)) {\n    if (event.isPrimary) {\n      return {\n        x: event.clientX,\n        y: event.clientY\n      };\n    }\n  } else if (isMouseEvent(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n  return {\n    x: Infinity,\n    y: Infinity\n  };\n}\n\nfunction getInputType() {\n  if (typeof matchMedia === \"function\") {\n    return matchMedia(\"(pointer:coarse)\").matches ? \"coarse\" : \"fine\";\n  }\n}\n\nfunction intersects(rectOne, rectTwo, strict) {\n  if (strict) {\n    return rectOne.x < rectTwo.x + rectTwo.width && rectOne.x + rectOne.width > rectTwo.x && rectOne.y < rectTwo.y + rectTwo.height && rectOne.y + rectOne.height > rectTwo.y;\n  } else {\n    return rectOne.x <= rectTwo.x + rectTwo.width && rectOne.x + rectOne.width >= rectTwo.x && rectOne.y <= rectTwo.y + rectTwo.height && rectOne.y + rectOne.height >= rectTwo.y;\n  }\n}\n\n// Forked from NPM stacking-order@2.0.0\n\n/**\n * Determine which of two nodes appears in front of the other —\n * if `a` is in front, returns 1, otherwise returns -1\n * @param {HTMLElement | SVGElement} a\n * @param {HTMLElement | SVGElement} b\n */\nfunction compare(a, b) {\n  if (a === b) throw new Error(\"Cannot compare node with itself\");\n  const ancestors = {\n    a: get_ancestors(a),\n    b: get_ancestors(b)\n  };\n  let common_ancestor;\n\n  // remove shared ancestors\n  while (ancestors.a.at(-1) === ancestors.b.at(-1)) {\n    a = ancestors.a.pop();\n    b = ancestors.b.pop();\n    common_ancestor = a;\n  }\n  assert(common_ancestor, \"Stacking order can only be calculated for elements with a common ancestor\");\n  const z_indexes = {\n    a: get_z_index(find_stacking_context(ancestors.a)),\n    b: get_z_index(find_stacking_context(ancestors.b))\n  };\n  if (z_indexes.a === z_indexes.b) {\n    const children = common_ancestor.childNodes;\n    const furthest_ancestors = {\n      a: ancestors.a.at(-1),\n      b: ancestors.b.at(-1)\n    };\n    let i = children.length;\n    while (i--) {\n      const child = children[i];\n      if (child === furthest_ancestors.a) return 1;\n      if (child === furthest_ancestors.b) return -1;\n    }\n  }\n  return Math.sign(z_indexes.a - z_indexes.b);\n}\nconst props = /\\b(?:position|zIndex|opacity|transform|webkitTransform|mixBlendMode|filter|webkitFilter|isolation)\\b/;\n\n/** @param {HTMLElement | SVGElement} node */\nfunction is_flex_item(node) {\n  var _get_parent;\n  // @ts-ignore\n  const display = getComputedStyle((_get_parent = get_parent(node)) !== null && _get_parent !== void 0 ? _get_parent : node).display;\n  return display === \"flex\" || display === \"inline-flex\";\n}\n\n/** @param {HTMLElement | SVGElement} node */\nfunction creates_stacking_context(node) {\n  const style = getComputedStyle(node);\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Positioning/Understanding_z_index/The_stacking_context\n  if (style.position === \"fixed\") return true;\n  // Forked to fix upstream bug https://github.com/Rich-Harris/stacking-order/issues/3\n  // if (\n  //   (style.zIndex !== \"auto\" && style.position !== \"static\") ||\n  //   is_flex_item(node)\n  // )\n  if (style.zIndex !== \"auto\" && (style.position !== \"static\" || is_flex_item(node))) return true;\n  if (+style.opacity < 1) return true;\n  if (\"transform\" in style && style.transform !== \"none\") return true;\n  if (\"webkitTransform\" in style && style.webkitTransform !== \"none\") return true;\n  if (\"mixBlendMode\" in style && style.mixBlendMode !== \"normal\") return true;\n  if (\"filter\" in style && style.filter !== \"none\") return true;\n  if (\"webkitFilter\" in style && style.webkitFilter !== \"none\") return true;\n  if (\"isolation\" in style && style.isolation === \"isolate\") return true;\n  if (props.test(style.willChange)) return true;\n  // @ts-expect-error\n  if (style.webkitOverflowScrolling === \"touch\") return true;\n  return false;\n}\n\n/** @param {(HTMLElement| SVGElement)[]} nodes */\nfunction find_stacking_context(nodes) {\n  let i = nodes.length;\n  while (i--) {\n    const node = nodes[i];\n    assert(node, \"Missing node\");\n    if (creates_stacking_context(node)) return node;\n  }\n  return null;\n}\n\n/** @param {HTMLElement | SVGElement} node */\nfunction get_z_index(node) {\n  return node && Number(getComputedStyle(node).zIndex) || 0;\n}\n\n/** @param {HTMLElement} node */\nfunction get_ancestors(node) {\n  const ancestors = [];\n  while (node) {\n    ancestors.push(node);\n    // @ts-ignore\n    node = get_parent(node);\n  }\n  return ancestors; // [ node, ... <body>, <html>, document ]\n}\n\n/** @param {HTMLElement} node */\nfunction get_parent(node) {\n  const {\n    parentNode\n  } = node;\n  if (parentNode && parentNode instanceof ShadowRoot) {\n    return parentNode.host;\n  }\n  return parentNode;\n}\n\nconst EXCEEDED_HORIZONTAL_MIN = 0b0001;\nconst EXCEEDED_HORIZONTAL_MAX = 0b0010;\nconst EXCEEDED_VERTICAL_MIN = 0b0100;\nconst EXCEEDED_VERTICAL_MAX = 0b1000;\nconst isCoarsePointer = getInputType() === \"coarse\";\nlet intersectingHandles = [];\nlet isPointerDown = false;\nlet ownerDocumentCounts = new Map();\nlet panelConstraintFlags = new Map();\nconst registeredResizeHandlers = new Set();\nfunction registerResizeHandle(resizeHandleId, element, direction, hitAreaMargins, setResizeHandlerState) {\n  var _ownerDocumentCounts$;\n  const {\n    ownerDocument\n  } = element;\n  const data = {\n    direction,\n    element,\n    hitAreaMargins,\n    setResizeHandlerState\n  };\n  const count = (_ownerDocumentCounts$ = ownerDocumentCounts.get(ownerDocument)) !== null && _ownerDocumentCounts$ !== void 0 ? _ownerDocumentCounts$ : 0;\n  ownerDocumentCounts.set(ownerDocument, count + 1);\n  registeredResizeHandlers.add(data);\n  updateListeners();\n  return function unregisterResizeHandle() {\n    var _ownerDocumentCounts$2;\n    panelConstraintFlags.delete(resizeHandleId);\n    registeredResizeHandlers.delete(data);\n    const count = (_ownerDocumentCounts$2 = ownerDocumentCounts.get(ownerDocument)) !== null && _ownerDocumentCounts$2 !== void 0 ? _ownerDocumentCounts$2 : 1;\n    ownerDocumentCounts.set(ownerDocument, count - 1);\n    updateListeners();\n    if (count === 1) {\n      ownerDocumentCounts.delete(ownerDocument);\n    }\n\n    // If the resize handle that is currently unmounting is intersecting with the pointer,\n    // update the global pointer to account for the change\n    if (intersectingHandles.includes(data)) {\n      const index = intersectingHandles.indexOf(data);\n      if (index >= 0) {\n        intersectingHandles.splice(index, 1);\n      }\n      updateCursor();\n\n      // Also instruct the handle to stop dragging; this prevents the parent group from being left in an inconsistent state\n      // See github.com/bvaughn/react-resizable-panels/issues/402\n      setResizeHandlerState(\"up\", true, null);\n    }\n  };\n}\nfunction handlePointerDown(event) {\n  const {\n    target\n  } = event;\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n  isPointerDown = true;\n  recalculateIntersectingHandles({\n    target,\n    x,\n    y\n  });\n  updateListeners();\n  if (intersectingHandles.length > 0) {\n    updateResizeHandlerStates(\"down\", event);\n    event.preventDefault();\n    event.stopPropagation();\n  }\n}\nfunction handlePointerMove(event) {\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n\n  // Edge case (see #340)\n  // Detect when the pointer has been released outside an iframe on a different domain\n  if (isPointerDown && event.buttons === 0) {\n    isPointerDown = false;\n    updateResizeHandlerStates(\"up\", event);\n  }\n  if (!isPointerDown) {\n    const {\n      target\n    } = event;\n\n    // Recalculate intersecting handles whenever the pointer moves, except if it has already been pressed\n    // at that point, the handles may not move with the pointer (depending on constraints)\n    // but the same set of active handles should be locked until the pointer is released\n    recalculateIntersectingHandles({\n      target,\n      x,\n      y\n    });\n  }\n  updateResizeHandlerStates(\"move\", event);\n\n  // Update cursor based on return value(s) from active handles\n  updateCursor();\n  if (intersectingHandles.length > 0) {\n    event.preventDefault();\n  }\n}\nfunction handlePointerUp(event) {\n  const {\n    target\n  } = event;\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n  panelConstraintFlags.clear();\n  isPointerDown = false;\n  if (intersectingHandles.length > 0) {\n    event.preventDefault();\n  }\n  updateResizeHandlerStates(\"up\", event);\n  recalculateIntersectingHandles({\n    target,\n    x,\n    y\n  });\n  updateCursor();\n  updateListeners();\n}\nfunction recalculateIntersectingHandles({\n  target,\n  x,\n  y\n}) {\n  intersectingHandles.splice(0);\n  let targetElement = null;\n  if (target instanceof HTMLElement || target instanceof SVGElement) {\n    targetElement = target;\n  }\n  registeredResizeHandlers.forEach(data => {\n    const {\n      element: dragHandleElement,\n      hitAreaMargins\n    } = data;\n    const dragHandleRect = dragHandleElement.getBoundingClientRect();\n    const {\n      bottom,\n      left,\n      right,\n      top\n    } = dragHandleRect;\n    const margin = isCoarsePointer ? hitAreaMargins.coarse : hitAreaMargins.fine;\n    const eventIntersects = x >= left - margin && x <= right + margin && y >= top - margin && y <= bottom + margin;\n    if (eventIntersects) {\n      // TRICKY\n      // We listen for pointers events at the root in order to support hit area margins\n      // (determining when the pointer is close enough to an element to be considered a \"hit\")\n      // Clicking on an element \"above\" a handle (e.g. a modal) should prevent a hit though\n      // so at this point we need to compare stacking order of a potentially intersecting drag handle,\n      // and the element that was actually clicked/touched\n      if (targetElement !== null && document.contains(targetElement) && dragHandleElement !== targetElement && !dragHandleElement.contains(targetElement) && !targetElement.contains(dragHandleElement) &&\n      // Calculating stacking order has a cost, so we should avoid it if possible\n      // That is why we only check potentially intersecting handles,\n      // and why we skip if the event target is within the handle's DOM\n      compare(targetElement, dragHandleElement) > 0) {\n        // If the target is above the drag handle, then we also need to confirm they overlap\n        // If they are beside each other (e.g. a panel and its drag handle) then the handle is still interactive\n        //\n        // It's not enough to compare only the target\n        // The target might be a small element inside of a larger container\n        // (For example, a SPAN or a DIV inside of a larger modal dialog)\n        let currentElement = targetElement;\n        let didIntersect = false;\n        while (currentElement) {\n          if (currentElement.contains(dragHandleElement)) {\n            break;\n          } else if (intersects(currentElement.getBoundingClientRect(), dragHandleRect, true)) {\n            didIntersect = true;\n            break;\n          }\n          currentElement = currentElement.parentElement;\n        }\n        if (didIntersect) {\n          return;\n        }\n      }\n      intersectingHandles.push(data);\n    }\n  });\n}\nfunction reportConstraintsViolation(resizeHandleId, flag) {\n  panelConstraintFlags.set(resizeHandleId, flag);\n}\nfunction updateCursor() {\n  let intersectsHorizontal = false;\n  let intersectsVertical = false;\n  intersectingHandles.forEach(data => {\n    const {\n      direction\n    } = data;\n    if (direction === \"horizontal\") {\n      intersectsHorizontal = true;\n    } else {\n      intersectsVertical = true;\n    }\n  });\n  let constraintFlags = 0;\n  panelConstraintFlags.forEach(flag => {\n    constraintFlags |= flag;\n  });\n  if (intersectsHorizontal && intersectsVertical) {\n    setGlobalCursorStyle(\"intersection\", constraintFlags);\n  } else if (intersectsHorizontal) {\n    setGlobalCursorStyle(\"horizontal\", constraintFlags);\n  } else if (intersectsVertical) {\n    setGlobalCursorStyle(\"vertical\", constraintFlags);\n  } else {\n    resetGlobalCursorStyle();\n  }\n}\nfunction updateListeners() {\n  ownerDocumentCounts.forEach((_, ownerDocument) => {\n    const {\n      body\n    } = ownerDocument;\n    body.removeEventListener(\"contextmenu\", handlePointerUp);\n    body.removeEventListener(\"pointerdown\", handlePointerDown);\n    body.removeEventListener(\"pointerleave\", handlePointerMove);\n    body.removeEventListener(\"pointermove\", handlePointerMove);\n  });\n  window.removeEventListener(\"pointerup\", handlePointerUp);\n  window.removeEventListener(\"pointercancel\", handlePointerUp);\n  if (registeredResizeHandlers.size > 0) {\n    if (isPointerDown) {\n      if (intersectingHandles.length > 0) {\n        ownerDocumentCounts.forEach((count, ownerDocument) => {\n          const {\n            body\n          } = ownerDocument;\n          if (count > 0) {\n            body.addEventListener(\"contextmenu\", handlePointerUp);\n            body.addEventListener(\"pointerleave\", handlePointerMove);\n            body.addEventListener(\"pointermove\", handlePointerMove);\n          }\n        });\n      }\n      window.addEventListener(\"pointerup\", handlePointerUp);\n      window.addEventListener(\"pointercancel\", handlePointerUp);\n    } else {\n      ownerDocumentCounts.forEach((count, ownerDocument) => {\n        const {\n          body\n        } = ownerDocument;\n        if (count > 0) {\n          body.addEventListener(\"pointerdown\", handlePointerDown, {\n            capture: true\n          });\n          body.addEventListener(\"pointermove\", handlePointerMove);\n        }\n      });\n    }\n  }\n}\nfunction updateResizeHandlerStates(action, event) {\n  registeredResizeHandlers.forEach(data => {\n    const {\n      setResizeHandlerState\n    } = data;\n    const isActive = intersectingHandles.includes(data);\n    setResizeHandlerState(action, isActive, event);\n  });\n}\n\nfunction useForceUpdate() {\n  const [_, setCount] = useState(0);\n  return useCallback(() => setCount(prevCount => prevCount + 1), []);\n}\n\nfunction assert(expectedCondition, message) {\n  if (!expectedCondition) {\n    console.error(message);\n    throw Error(message);\n  }\n}\n\nconst PRECISION = 10;\n\nfunction fuzzyCompareNumbers(actual, expected, fractionDigits = PRECISION) {\n  if (actual.toFixed(fractionDigits) === expected.toFixed(fractionDigits)) {\n    return 0;\n  } else {\n    return actual > expected ? 1 : -1;\n  }\n}\nfunction fuzzyNumbersEqual$1(actual, expected, fractionDigits = PRECISION) {\n  return fuzzyCompareNumbers(actual, expected, fractionDigits) === 0;\n}\n\nfunction fuzzyNumbersEqual(actual, expected, fractionDigits) {\n  return fuzzyCompareNumbers(actual, expected, fractionDigits) === 0;\n}\n\nfunction fuzzyLayoutsEqual(actual, expected, fractionDigits) {\n  if (actual.length !== expected.length) {\n    return false;\n  }\n  for (let index = 0; index < actual.length; index++) {\n    const actualSize = actual[index];\n    const expectedSize = expected[index];\n    if (!fuzzyNumbersEqual(actualSize, expectedSize, fractionDigits)) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// Panel size must be in percentages; pixel values should be pre-converted\nfunction resizePanel({\n  panelConstraints: panelConstraintsArray,\n  panelIndex,\n  size\n}) {\n  const panelConstraints = panelConstraintsArray[panelIndex];\n  assert(panelConstraints != null, `Panel constraints not found for index ${panelIndex}`);\n  let {\n    collapsedSize = 0,\n    collapsible,\n    maxSize = 100,\n    minSize = 0\n  } = panelConstraints;\n  if (fuzzyCompareNumbers(size, minSize) < 0) {\n    if (collapsible) {\n      // Collapsible panels should snap closed or open only once they cross the halfway point between collapsed and min size.\n      const halfwayPoint = (collapsedSize + minSize) / 2;\n      if (fuzzyCompareNumbers(size, halfwayPoint) < 0) {\n        size = collapsedSize;\n      } else {\n        size = minSize;\n      }\n    } else {\n      size = minSize;\n    }\n  }\n  size = Math.min(maxSize, size);\n  size = parseFloat(size.toFixed(PRECISION));\n  return size;\n}\n\n// All units must be in percentages; pixel values should be pre-converted\nfunction adjustLayoutByDelta({\n  delta,\n  initialLayout,\n  panelConstraints: panelConstraintsArray,\n  pivotIndices,\n  prevLayout,\n  trigger\n}) {\n  if (fuzzyNumbersEqual(delta, 0)) {\n    return initialLayout;\n  }\n  const nextLayout = [...initialLayout];\n  const [firstPivotIndex, secondPivotIndex] = pivotIndices;\n  assert(firstPivotIndex != null, \"Invalid first pivot index\");\n  assert(secondPivotIndex != null, \"Invalid second pivot index\");\n  let deltaApplied = 0;\n\n  // const DEBUG = [];\n  // DEBUG.push(`adjustLayoutByDelta()`);\n  // DEBUG.push(`  initialLayout: ${initialLayout.join(\", \")}`);\n  // DEBUG.push(`  prevLayout: ${prevLayout.join(\", \")}`);\n  // DEBUG.push(`  delta: ${delta}`);\n  // DEBUG.push(`  pivotIndices: ${pivotIndices.join(\", \")}`);\n  // DEBUG.push(`  trigger: ${trigger}`);\n  // DEBUG.push(\"\");\n\n  // A resizing panel affects the panels before or after it.\n  //\n  // A negative delta means the panel(s) immediately after the resize handle should grow/expand by decreasing its offset.\n  // Other panels may also need to shrink/contract (and shift) to make room, depending on the min weights.\n  //\n  // A positive delta means the panel(s) immediately before the resize handle should \"expand\".\n  // This is accomplished by shrinking/contracting (and shifting) one or more of the panels after the resize handle.\n\n  {\n    // If this is a resize triggered by a keyboard event, our logic for expanding/collapsing is different.\n    // We no longer check the halfway threshold because this may prevent the panel from expanding at all.\n    if (trigger === \"keyboard\") {\n      {\n        // Check if we should expand a collapsed panel\n        const index = delta < 0 ? secondPivotIndex : firstPivotIndex;\n        const panelConstraints = panelConstraintsArray[index];\n        assert(panelConstraints, `Panel constraints not found for index ${index}`);\n        const {\n          collapsedSize = 0,\n          collapsible,\n          minSize = 0\n        } = panelConstraints;\n\n        // DEBUG.push(`edge case check 1: ${index}`);\n        // DEBUG.push(`  -> collapsible? ${collapsible}`);\n        if (collapsible) {\n          const prevSize = initialLayout[index];\n          assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n          if (fuzzyNumbersEqual(prevSize, collapsedSize)) {\n            const localDelta = minSize - prevSize;\n            // DEBUG.push(`  -> expand delta: ${localDelta}`);\n\n            if (fuzzyCompareNumbers(localDelta, Math.abs(delta)) > 0) {\n              delta = delta < 0 ? 0 - localDelta : localDelta;\n              // DEBUG.push(`  -> delta: ${delta}`);\n            }\n          }\n        }\n      }\n\n      {\n        // Check if we should collapse a panel at its minimum size\n        const index = delta < 0 ? firstPivotIndex : secondPivotIndex;\n        const panelConstraints = panelConstraintsArray[index];\n        assert(panelConstraints, `No panel constraints found for index ${index}`);\n        const {\n          collapsedSize = 0,\n          collapsible,\n          minSize = 0\n        } = panelConstraints;\n\n        // DEBUG.push(`edge case check 2: ${index}`);\n        // DEBUG.push(`  -> collapsible? ${collapsible}`);\n        if (collapsible) {\n          const prevSize = initialLayout[index];\n          assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n          if (fuzzyNumbersEqual(prevSize, minSize)) {\n            const localDelta = prevSize - collapsedSize;\n            // DEBUG.push(`  -> expand delta: ${localDelta}`);\n\n            if (fuzzyCompareNumbers(localDelta, Math.abs(delta)) > 0) {\n              delta = delta < 0 ? 0 - localDelta : localDelta;\n              // DEBUG.push(`  -> delta: ${delta}`);\n            }\n          }\n        }\n      }\n    }\n    // DEBUG.push(\"\");\n  }\n\n  {\n    // Pre-calculate max available delta in the opposite direction of our pivot.\n    // This will be the maximum amount we're allowed to expand/contract the panels in the primary direction.\n    // If this amount is less than the requested delta, adjust the requested delta.\n    // If this amount is greater than the requested delta, that's useful information too–\n    // as an expanding panel might change from collapsed to min size.\n\n    const increment = delta < 0 ? 1 : -1;\n    let index = delta < 0 ? secondPivotIndex : firstPivotIndex;\n    let maxAvailableDelta = 0;\n\n    // DEBUG.push(\"pre calc...\");\n    while (true) {\n      const prevSize = initialLayout[index];\n      assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n      const maxSafeSize = resizePanel({\n        panelConstraints: panelConstraintsArray,\n        panelIndex: index,\n        size: 100\n      });\n      const delta = maxSafeSize - prevSize;\n      // DEBUG.push(`  ${index}: ${prevSize} -> ${maxSafeSize}`);\n\n      maxAvailableDelta += delta;\n      index += increment;\n      if (index < 0 || index >= panelConstraintsArray.length) {\n        break;\n      }\n    }\n\n    // DEBUG.push(`  -> max available delta: ${maxAvailableDelta}`);\n    const minAbsDelta = Math.min(Math.abs(delta), Math.abs(maxAvailableDelta));\n    delta = delta < 0 ? 0 - minAbsDelta : minAbsDelta;\n    // DEBUG.push(`  -> adjusted delta: ${delta}`);\n    // DEBUG.push(\"\");\n  }\n\n  {\n    // Delta added to a panel needs to be subtracted from other panels (within the constraints that those panels allow).\n\n    const pivotIndex = delta < 0 ? firstPivotIndex : secondPivotIndex;\n    let index = pivotIndex;\n    while (index >= 0 && index < panelConstraintsArray.length) {\n      const deltaRemaining = Math.abs(delta) - Math.abs(deltaApplied);\n      const prevSize = initialLayout[index];\n      assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n      const unsafeSize = prevSize - deltaRemaining;\n      const safeSize = resizePanel({\n        panelConstraints: panelConstraintsArray,\n        panelIndex: index,\n        size: unsafeSize\n      });\n      if (!fuzzyNumbersEqual(prevSize, safeSize)) {\n        deltaApplied += prevSize - safeSize;\n        nextLayout[index] = safeSize;\n        if (deltaApplied.toPrecision(3).localeCompare(Math.abs(delta).toPrecision(3), undefined, {\n          numeric: true\n        }) >= 0) {\n          break;\n        }\n      }\n      if (delta < 0) {\n        index--;\n      } else {\n        index++;\n      }\n    }\n  }\n  // DEBUG.push(`after 1: ${nextLayout.join(\", \")}`);\n  // DEBUG.push(`  deltaApplied: ${deltaApplied}`);\n  // DEBUG.push(\"\");\n\n  // If we were unable to resize any of the panels panels, return the previous state.\n  // This will essentially bailout and ignore e.g. drags past a panel's boundaries\n  if (fuzzyLayoutsEqual(prevLayout, nextLayout)) {\n    // DEBUG.push(`bailout to previous layout: ${prevLayout.join(\", \")}`);\n    // console.log(DEBUG.join(\"\\n\"));\n\n    return prevLayout;\n  }\n  {\n    // Now distribute the applied delta to the panels in the other direction\n    const pivotIndex = delta < 0 ? secondPivotIndex : firstPivotIndex;\n    const prevSize = initialLayout[pivotIndex];\n    assert(prevSize != null, `Previous layout not found for panel index ${pivotIndex}`);\n    const unsafeSize = prevSize + deltaApplied;\n    const safeSize = resizePanel({\n      panelConstraints: panelConstraintsArray,\n      panelIndex: pivotIndex,\n      size: unsafeSize\n    });\n\n    // Adjust the pivot panel before, but only by the amount that surrounding panels were able to shrink/contract.\n    nextLayout[pivotIndex] = safeSize;\n\n    // Edge case where expanding or contracting one panel caused another one to change collapsed state\n    if (!fuzzyNumbersEqual(safeSize, unsafeSize)) {\n      let deltaRemaining = unsafeSize - safeSize;\n      const pivotIndex = delta < 0 ? secondPivotIndex : firstPivotIndex;\n      let index = pivotIndex;\n      while (index >= 0 && index < panelConstraintsArray.length) {\n        const prevSize = nextLayout[index];\n        assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n        const unsafeSize = prevSize + deltaRemaining;\n        const safeSize = resizePanel({\n          panelConstraints: panelConstraintsArray,\n          panelIndex: index,\n          size: unsafeSize\n        });\n        if (!fuzzyNumbersEqual(prevSize, safeSize)) {\n          deltaRemaining -= safeSize - prevSize;\n          nextLayout[index] = safeSize;\n        }\n        if (fuzzyNumbersEqual(deltaRemaining, 0)) {\n          break;\n        }\n        if (delta > 0) {\n          index--;\n        } else {\n          index++;\n        }\n      }\n    }\n  }\n  // DEBUG.push(`after 2: ${nextLayout.join(\", \")}`);\n  // DEBUG.push(`  deltaApplied: ${deltaApplied}`);\n  // DEBUG.push(\"\");\n\n  const totalSize = nextLayout.reduce((total, size) => size + total, 0);\n  // DEBUG.push(`total size: ${totalSize}`);\n\n  // If our new layout doesn't add up to 100%, that means the requested delta can't be applied\n  // In that case, fall back to our most recent valid layout\n  if (!fuzzyNumbersEqual(totalSize, 100)) {\n    // DEBUG.push(`bailout to previous layout: ${prevLayout.join(\", \")}`);\n    // console.log(DEBUG.join(\"\\n\"));\n\n    return prevLayout;\n  }\n\n  // console.log(DEBUG.join(\"\\n\"));\n  return nextLayout;\n}\n\nfunction getResizeHandleElementsForGroup(groupId, scope = document) {\n  return Array.from(scope.querySelectorAll(`[data-panel-resize-handle-id][data-panel-group-id=\"${groupId}\"]`));\n}\n\nfunction getResizeHandleElementIndex(groupId, id, scope = document) {\n  const handles = getResizeHandleElementsForGroup(groupId, scope);\n  const index = handles.findIndex(handle => handle.getAttribute(\"data-panel-resize-handle-id\") === id);\n  return index !== null && index !== void 0 ? index : null;\n}\n\nfunction determinePivotIndices(groupId, dragHandleId, panelGroupElement) {\n  const index = getResizeHandleElementIndex(groupId, dragHandleId, panelGroupElement);\n  return index != null ? [index, index + 1] : [-1, -1];\n}\n\nfunction getPanelGroupElement(id, rootElement = document) {\n  var _dataset;\n  //If the root element is the PanelGroup\n  if (rootElement instanceof HTMLElement && (rootElement === null || rootElement === void 0 ? void 0 : (_dataset = rootElement.dataset) === null || _dataset === void 0 ? void 0 : _dataset.panelGroupId) == id) {\n    return rootElement;\n  }\n\n  //Else query children\n  const element = rootElement.querySelector(`[data-panel-group][data-panel-group-id=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction getResizeHandleElement(id, scope = document) {\n  const element = scope.querySelector(`[data-panel-resize-handle-id=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction getResizeHandlePanelIds(groupId, handleId, panelsArray, scope = document) {\n  var _panelsArray$index$id, _panelsArray$index, _panelsArray$id, _panelsArray;\n  const handle = getResizeHandleElement(handleId, scope);\n  const handles = getResizeHandleElementsForGroup(groupId, scope);\n  const index = handle ? handles.indexOf(handle) : -1;\n  const idBefore = (_panelsArray$index$id = (_panelsArray$index = panelsArray[index]) === null || _panelsArray$index === void 0 ? void 0 : _panelsArray$index.id) !== null && _panelsArray$index$id !== void 0 ? _panelsArray$index$id : null;\n  const idAfter = (_panelsArray$id = (_panelsArray = panelsArray[index + 1]) === null || _panelsArray === void 0 ? void 0 : _panelsArray.id) !== null && _panelsArray$id !== void 0 ? _panelsArray$id : null;\n  return [idBefore, idAfter];\n}\n\n// https://www.w3.org/WAI/ARIA/apg/patterns/windowsplitter/\n\nfunction useWindowSplitterPanelGroupBehavior({\n  committedValuesRef,\n  eagerValuesRef,\n  groupId,\n  layout,\n  panelDataArray,\n  panelGroupElement,\n  setLayout\n}) {\n  useRef({\n    didWarnAboutMissingResizeHandle: false\n  });\n  useEffect(() => {\n    if (!panelGroupElement) {\n      return;\n    }\n    const eagerValues = eagerValuesRef.current;\n    assert(eagerValues, `Eager values not found`);\n    const {\n      panelDataArray\n    } = eagerValues;\n    const groupElement = getPanelGroupElement(groupId, panelGroupElement);\n    assert(groupElement != null, `No group found for id \"${groupId}\"`);\n    const handles = getResizeHandleElementsForGroup(groupId, panelGroupElement);\n    assert(handles, `No resize handles found for group id \"${groupId}\"`);\n    const cleanupFunctions = handles.map(handle => {\n      const handleId = handle.getAttribute(\"data-panel-resize-handle-id\");\n      assert(handleId, `Resize handle element has no handle id attribute`);\n      const [idBefore, idAfter] = getResizeHandlePanelIds(groupId, handleId, panelDataArray, panelGroupElement);\n      if (idBefore == null || idAfter == null) {\n        return () => {};\n      }\n      const onKeyDown = event => {\n        if (event.defaultPrevented) {\n          return;\n        }\n        switch (event.key) {\n          case \"Enter\":\n            {\n              event.preventDefault();\n              const index = panelDataArray.findIndex(panelData => panelData.id === idBefore);\n              if (index >= 0) {\n                const panelData = panelDataArray[index];\n                assert(panelData, `No panel data found for index ${index}`);\n                const size = layout[index];\n                const {\n                  collapsedSize = 0,\n                  collapsible,\n                  minSize = 0\n                } = panelData.constraints;\n                if (size != null && collapsible) {\n                  const nextLayout = adjustLayoutByDelta({\n                    delta: fuzzyNumbersEqual(size, collapsedSize) ? minSize - collapsedSize : collapsedSize - size,\n                    initialLayout: layout,\n                    panelConstraints: panelDataArray.map(panelData => panelData.constraints),\n                    pivotIndices: determinePivotIndices(groupId, handleId, panelGroupElement),\n                    prevLayout: layout,\n                    trigger: \"keyboard\"\n                  });\n                  if (layout !== nextLayout) {\n                    setLayout(nextLayout);\n                  }\n                }\n              }\n              break;\n            }\n        }\n      };\n      handle.addEventListener(\"keydown\", onKeyDown);\n      return () => {\n        handle.removeEventListener(\"keydown\", onKeyDown);\n      };\n    });\n    return () => {\n      cleanupFunctions.forEach(cleanupFunction => cleanupFunction());\n    };\n  }, [panelGroupElement, committedValuesRef, eagerValuesRef, groupId, layout, panelDataArray, setLayout]);\n}\n\nfunction areEqual(arrayA, arrayB) {\n  if (arrayA.length !== arrayB.length) {\n    return false;\n  }\n  for (let index = 0; index < arrayA.length; index++) {\n    if (arrayA[index] !== arrayB[index]) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction getResizeEventCursorPosition(direction, event) {\n  const isHorizontal = direction === \"horizontal\";\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n  return isHorizontal ? x : y;\n}\n\nfunction calculateDragOffsetPercentage(event, dragHandleId, direction, initialDragState, panelGroupElement) {\n  const isHorizontal = direction === \"horizontal\";\n  const handleElement = getResizeHandleElement(dragHandleId, panelGroupElement);\n  assert(handleElement, `No resize handle element found for id \"${dragHandleId}\"`);\n  const groupId = handleElement.getAttribute(\"data-panel-group-id\");\n  assert(groupId, `Resize handle element has no group id attribute`);\n  let {\n    initialCursorPosition\n  } = initialDragState;\n  const cursorPosition = getResizeEventCursorPosition(direction, event);\n  const groupElement = getPanelGroupElement(groupId, panelGroupElement);\n  assert(groupElement, `No group element found for id \"${groupId}\"`);\n  const groupRect = groupElement.getBoundingClientRect();\n  const groupSizeInPixels = isHorizontal ? groupRect.width : groupRect.height;\n  const offsetPixels = cursorPosition - initialCursorPosition;\n  const offsetPercentage = offsetPixels / groupSizeInPixels * 100;\n  return offsetPercentage;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/movementX\nfunction calculateDeltaPercentage(event, dragHandleId, direction, initialDragState, keyboardResizeBy, panelGroupElement) {\n  if (isKeyDown(event)) {\n    const isHorizontal = direction === \"horizontal\";\n    let delta = 0;\n    if (event.shiftKey) {\n      delta = 100;\n    } else if (keyboardResizeBy != null) {\n      delta = keyboardResizeBy;\n    } else {\n      delta = 10;\n    }\n    let movement = 0;\n    switch (event.key) {\n      case \"ArrowDown\":\n        movement = isHorizontal ? 0 : delta;\n        break;\n      case \"ArrowLeft\":\n        movement = isHorizontal ? -delta : 0;\n        break;\n      case \"ArrowRight\":\n        movement = isHorizontal ? delta : 0;\n        break;\n      case \"ArrowUp\":\n        movement = isHorizontal ? 0 : -delta;\n        break;\n      case \"End\":\n        movement = 100;\n        break;\n      case \"Home\":\n        movement = -100;\n        break;\n    }\n    return movement;\n  } else {\n    if (initialDragState == null) {\n      return 0;\n    }\n    return calculateDragOffsetPercentage(event, dragHandleId, direction, initialDragState, panelGroupElement);\n  }\n}\n\n// Layout should be pre-converted into percentages\nfunction callPanelCallbacks(panelsArray, layout, panelIdToLastNotifiedSizeMap) {\n  layout.forEach((size, index) => {\n    const panelData = panelsArray[index];\n    assert(panelData, `Panel data not found for index ${index}`);\n    const {\n      callbacks,\n      constraints,\n      id: panelId\n    } = panelData;\n    const {\n      collapsedSize = 0,\n      collapsible\n    } = constraints;\n    const lastNotifiedSize = panelIdToLastNotifiedSizeMap[panelId];\n    if (lastNotifiedSize == null || size !== lastNotifiedSize) {\n      panelIdToLastNotifiedSizeMap[panelId] = size;\n      const {\n        onCollapse,\n        onExpand,\n        onResize\n      } = callbacks;\n      if (onResize) {\n        onResize(size, lastNotifiedSize);\n      }\n      if (collapsible && (onCollapse || onExpand)) {\n        if (onExpand && (lastNotifiedSize == null || fuzzyNumbersEqual$1(lastNotifiedSize, collapsedSize)) && !fuzzyNumbersEqual$1(size, collapsedSize)) {\n          onExpand();\n        }\n        if (onCollapse && (lastNotifiedSize == null || !fuzzyNumbersEqual$1(lastNotifiedSize, collapsedSize)) && fuzzyNumbersEqual$1(size, collapsedSize)) {\n          onCollapse();\n        }\n      }\n    }\n  });\n}\n\nfunction compareLayouts(a, b) {\n  if (a.length !== b.length) {\n    return false;\n  } else {\n    for (let index = 0; index < a.length; index++) {\n      if (a[index] != b[index]) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\n\n// This method returns a number between 1 and 100 representing\n\n// the % of the group's overall space this panel should occupy.\nfunction computePanelFlexBoxStyle({\n  defaultSize,\n  dragState,\n  layout,\n  panelData,\n  panelIndex,\n  precision = 3\n}) {\n  const size = layout[panelIndex];\n  let flexGrow;\n  if (size == null) {\n    // Initial render (before panels have registered themselves)\n    // In order to support server rendering, fall back to default size if provided\n    flexGrow = defaultSize != undefined ? defaultSize.toPrecision(precision) : \"1\";\n  } else if (panelData.length === 1) {\n    // Special case: Single panel group should always fill full width/height\n    flexGrow = \"1\";\n  } else {\n    flexGrow = size.toPrecision(precision);\n  }\n  return {\n    flexBasis: 0,\n    flexGrow,\n    flexShrink: 1,\n    // Without this, Panel sizes may be unintentionally overridden by their content\n    overflow: \"hidden\",\n    // Disable pointer events inside of a panel during resize\n    // This avoid edge cases like nested iframes\n    pointerEvents: dragState !== null ? \"none\" : undefined\n  };\n}\n\nfunction debounce(callback, durationMs = 10) {\n  let timeoutId = null;\n  let callable = (...args) => {\n    if (timeoutId !== null) {\n      clearTimeout(timeoutId);\n    }\n    timeoutId = setTimeout(() => {\n      callback(...args);\n    }, durationMs);\n  };\n  return callable;\n}\n\n// PanelGroup might be rendering in a server-side environment where localStorage is not available\n// or on a browser with cookies/storage disabled.\n// In either case, this function avoids accessing localStorage until needed,\n// and avoids throwing user-visible errors.\nfunction initializeDefaultStorage(storageObject) {\n  try {\n    if (typeof localStorage !== \"undefined\") {\n      // Bypass this check for future calls\n      storageObject.getItem = name => {\n        return localStorage.getItem(name);\n      };\n      storageObject.setItem = (name, value) => {\n        localStorage.setItem(name, value);\n      };\n    } else {\n      throw new Error(\"localStorage not supported in this environment\");\n    }\n  } catch (error) {\n    console.error(error);\n    storageObject.getItem = () => null;\n    storageObject.setItem = () => {};\n  }\n}\n\nfunction getPanelGroupKey(autoSaveId) {\n  return `react-resizable-panels:${autoSaveId}`;\n}\n\n// Note that Panel ids might be user-provided (stable) or useId generated (non-deterministic)\n// so they should not be used as part of the serialization key.\n// Using the min/max size attributes should work well enough as a backup.\n// Pre-sorting by minSize allows remembering layouts even if panels are re-ordered/dragged.\nfunction getPanelKey(panels) {\n  return panels.map(panel => {\n    const {\n      constraints,\n      id,\n      idIsFromProps,\n      order\n    } = panel;\n    if (idIsFromProps) {\n      return id;\n    } else {\n      return order ? `${order}:${JSON.stringify(constraints)}` : JSON.stringify(constraints);\n    }\n  }).sort((a, b) => a.localeCompare(b)).join(\",\");\n}\nfunction loadSerializedPanelGroupState(autoSaveId, storage) {\n  try {\n    const panelGroupKey = getPanelGroupKey(autoSaveId);\n    const serialized = storage.getItem(panelGroupKey);\n    if (serialized) {\n      const parsed = JSON.parse(serialized);\n      if (typeof parsed === \"object\" && parsed != null) {\n        return parsed;\n      }\n    }\n  } catch (error) {}\n  return null;\n}\nfunction savePanelGroupState(autoSaveId, panels, panelSizesBeforeCollapse, sizes, storage) {\n  var _loadSerializedPanelG2;\n  const panelGroupKey = getPanelGroupKey(autoSaveId);\n  const panelKey = getPanelKey(panels);\n  const state = (_loadSerializedPanelG2 = loadSerializedPanelGroupState(autoSaveId, storage)) !== null && _loadSerializedPanelG2 !== void 0 ? _loadSerializedPanelG2 : {};\n  state[panelKey] = {\n    expandToSizes: Object.fromEntries(panelSizesBeforeCollapse.entries()),\n    layout: sizes\n  };\n  try {\n    storage.setItem(panelGroupKey, JSON.stringify(state));\n  } catch (error) {\n    console.error(error);\n  }\n}\n\nfunction validatePanelConstraints({\n  panelConstraints: panelConstraintsArray,\n  panelId,\n  panelIndex\n}) {\n  {\n    const warnings = [];\n    const panelConstraints = panelConstraintsArray[panelIndex];\n    assert(panelConstraints, `No panel constraints found for index ${panelIndex}`);\n    const {\n      collapsedSize = 0,\n      collapsible = false,\n      defaultSize,\n      maxSize = 100,\n      minSize = 0\n    } = panelConstraints;\n    if (minSize > maxSize) {\n      warnings.push(`min size (${minSize}%) should not be greater than max size (${maxSize}%)`);\n    }\n    if (defaultSize != null) {\n      if (defaultSize < 0) {\n        warnings.push(\"default size should not be less than 0\");\n      } else if (defaultSize < minSize && (!collapsible || defaultSize !== collapsedSize)) {\n        warnings.push(\"default size should not be less than min size\");\n      }\n      if (defaultSize > 100) {\n        warnings.push(\"default size should not be greater than 100\");\n      } else if (defaultSize > maxSize) {\n        warnings.push(\"default size should not be greater than max size\");\n      }\n    }\n    if (collapsedSize > minSize) {\n      warnings.push(\"collapsed size should not be greater than min size\");\n    }\n    if (warnings.length > 0) {\n      const name = panelId != null ? `Panel \"${panelId}\"` : \"Panel\";\n      console.warn(`${name} has an invalid configuration:\\n\\n${warnings.join(\"\\n\")}`);\n      return false;\n    }\n  }\n  return true;\n}\n\n// All units must be in percentages; pixel values should be pre-converted\nfunction validatePanelGroupLayout({\n  layout: prevLayout,\n  panelConstraints\n}) {\n  const nextLayout = [...prevLayout];\n  const nextLayoutTotalSize = nextLayout.reduce((accumulated, current) => accumulated + current, 0);\n\n  // Validate layout expectations\n  if (nextLayout.length !== panelConstraints.length) {\n    throw Error(`Invalid ${panelConstraints.length} panel layout: ${nextLayout.map(size => `${size}%`).join(\", \")}`);\n  } else if (!fuzzyNumbersEqual(nextLayoutTotalSize, 100) && nextLayout.length > 0) {\n    // This is not ideal so we should warn about it, but it may be recoverable in some cases\n    // (especially if the amount is small)\n    {\n      console.warn(`WARNING: Invalid layout total size: ${nextLayout.map(size => `${size}%`).join(\", \")}. Layout normalization will be applied.`);\n    }\n    for (let index = 0; index < panelConstraints.length; index++) {\n      const unsafeSize = nextLayout[index];\n      assert(unsafeSize != null, `No layout data found for index ${index}`);\n      const safeSize = 100 / nextLayoutTotalSize * unsafeSize;\n      nextLayout[index] = safeSize;\n    }\n  }\n  let remainingSize = 0;\n\n  // First pass: Validate the proposed layout given each panel's constraints\n  for (let index = 0; index < panelConstraints.length; index++) {\n    const unsafeSize = nextLayout[index];\n    assert(unsafeSize != null, `No layout data found for index ${index}`);\n    const safeSize = resizePanel({\n      panelConstraints,\n      panelIndex: index,\n      size: unsafeSize\n    });\n    if (unsafeSize != safeSize) {\n      remainingSize += unsafeSize - safeSize;\n      nextLayout[index] = safeSize;\n    }\n  }\n\n  // If there is additional, left over space, assign it to any panel(s) that permits it\n  // (It's not worth taking multiple additional passes to evenly distribute)\n  if (!fuzzyNumbersEqual(remainingSize, 0)) {\n    for (let index = 0; index < panelConstraints.length; index++) {\n      const prevSize = nextLayout[index];\n      assert(prevSize != null, `No layout data found for index ${index}`);\n      const unsafeSize = prevSize + remainingSize;\n      const safeSize = resizePanel({\n        panelConstraints,\n        panelIndex: index,\n        size: unsafeSize\n      });\n      if (prevSize !== safeSize) {\n        remainingSize -= safeSize - prevSize;\n        nextLayout[index] = safeSize;\n\n        // Once we've used up the remainder, bail\n        if (fuzzyNumbersEqual(remainingSize, 0)) {\n          break;\n        }\n      }\n    }\n  }\n  return nextLayout;\n}\n\nconst LOCAL_STORAGE_DEBOUNCE_INTERVAL = 100;\nconst defaultStorage = {\n  getItem: name => {\n    initializeDefaultStorage(defaultStorage);\n    return defaultStorage.getItem(name);\n  },\n  setItem: (name, value) => {\n    initializeDefaultStorage(defaultStorage);\n    defaultStorage.setItem(name, value);\n  }\n};\nconst debounceMap = {};\nfunction PanelGroupWithForwardedRef({\n  autoSaveId = null,\n  children,\n  className: classNameFromProps = \"\",\n  direction,\n  forwardedRef,\n  id: idFromProps = null,\n  onLayout = null,\n  keyboardResizeBy = null,\n  storage = defaultStorage,\n  style: styleFromProps,\n  tagName: Type = \"div\",\n  ...rest\n}) {\n  const groupId = useUniqueId(idFromProps);\n  const panelGroupElementRef = useRef(null);\n  const [dragState, setDragState] = useState(null);\n  const [layout, setLayout] = useState([]);\n  const forceUpdate = useForceUpdate();\n  const panelIdToLastNotifiedSizeMapRef = useRef({});\n  const panelSizeBeforeCollapseRef = useRef(new Map());\n  const prevDeltaRef = useRef(0);\n  const committedValuesRef = useRef({\n    autoSaveId,\n    direction,\n    dragState,\n    id: groupId,\n    keyboardResizeBy,\n    onLayout,\n    storage\n  });\n  const eagerValuesRef = useRef({\n    layout,\n    panelDataArray: [],\n    panelDataArrayChanged: false\n  });\n  const devWarningsRef = useRef({\n    didLogIdAndOrderWarning: false,\n    didLogPanelConstraintsWarning: false,\n    prevPanelIds: []\n  });\n  useImperativeHandle(forwardedRef, () => ({\n    getId: () => committedValuesRef.current.id,\n    getLayout: () => {\n      const {\n        layout\n      } = eagerValuesRef.current;\n      return layout;\n    },\n    setLayout: unsafeLayout => {\n      const {\n        onLayout\n      } = committedValuesRef.current;\n      const {\n        layout: prevLayout,\n        panelDataArray\n      } = eagerValuesRef.current;\n      const safeLayout = validatePanelGroupLayout({\n        layout: unsafeLayout,\n        panelConstraints: panelDataArray.map(panelData => panelData.constraints)\n      });\n      if (!areEqual(prevLayout, safeLayout)) {\n        setLayout(safeLayout);\n        eagerValuesRef.current.layout = safeLayout;\n        if (onLayout) {\n          onLayout(safeLayout);\n        }\n        callPanelCallbacks(panelDataArray, safeLayout, panelIdToLastNotifiedSizeMapRef.current);\n      }\n    }\n  }), []);\n  useWindowSplitterPanelGroupBehavior({\n    committedValuesRef,\n    eagerValuesRef,\n    groupId,\n    layout,\n    panelDataArray: eagerValuesRef.current.panelDataArray,\n    setLayout,\n    panelGroupElement: panelGroupElementRef.current\n  });\n  useEffect(() => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n\n    // If this panel has been configured to persist sizing information, save sizes to local storage.\n    if (autoSaveId) {\n      if (layout.length === 0 || layout.length !== panelDataArray.length) {\n        return;\n      }\n      let debouncedSave = debounceMap[autoSaveId];\n\n      // Limit the frequency of localStorage updates.\n      if (debouncedSave == null) {\n        debouncedSave = debounce(savePanelGroupState, LOCAL_STORAGE_DEBOUNCE_INTERVAL);\n        debounceMap[autoSaveId] = debouncedSave;\n      }\n\n      // Clone mutable data before passing to the debounced function,\n      // else we run the risk of saving an incorrect combination of mutable and immutable values to state.\n      const clonedPanelDataArray = [...panelDataArray];\n      const clonedPanelSizesBeforeCollapse = new Map(panelSizeBeforeCollapseRef.current);\n      debouncedSave(autoSaveId, clonedPanelDataArray, clonedPanelSizesBeforeCollapse, layout, storage);\n    }\n  }, [autoSaveId, layout, storage]);\n\n  // DEV warnings\n  useEffect(() => {\n    {\n      const {\n        panelDataArray\n      } = eagerValuesRef.current;\n      const {\n        didLogIdAndOrderWarning,\n        didLogPanelConstraintsWarning,\n        prevPanelIds\n      } = devWarningsRef.current;\n      if (!didLogIdAndOrderWarning) {\n        const panelIds = panelDataArray.map(({\n          id\n        }) => id);\n        devWarningsRef.current.prevPanelIds = panelIds;\n        const panelsHaveChanged = prevPanelIds.length > 0 && !areEqual(prevPanelIds, panelIds);\n        if (panelsHaveChanged) {\n          if (panelDataArray.find(({\n            idIsFromProps,\n            order\n          }) => !idIsFromProps || order == null)) {\n            devWarningsRef.current.didLogIdAndOrderWarning = true;\n            console.warn(`WARNING: Panel id and order props recommended when panels are dynamically rendered`);\n          }\n        }\n      }\n      if (!didLogPanelConstraintsWarning) {\n        const panelConstraints = panelDataArray.map(panelData => panelData.constraints);\n        for (let panelIndex = 0; panelIndex < panelConstraints.length; panelIndex++) {\n          const panelData = panelDataArray[panelIndex];\n          assert(panelData, `Panel data not found for index ${panelIndex}`);\n          const isValid = validatePanelConstraints({\n            panelConstraints,\n            panelId: panelData.id,\n            panelIndex\n          });\n          if (!isValid) {\n            devWarningsRef.current.didLogPanelConstraintsWarning = true;\n            break;\n          }\n        }\n      }\n    }\n  });\n\n  // External APIs are safe to memoize via committed values ref\n  const collapsePanel = useCallback(panelData => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    if (panelData.constraints.collapsible) {\n      const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n      const {\n        collapsedSize = 0,\n        panelSize,\n        pivotIndices\n      } = panelDataHelper(panelDataArray, panelData, prevLayout);\n      assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n      if (!fuzzyNumbersEqual$1(panelSize, collapsedSize)) {\n        // Store size before collapse;\n        // This is the size that gets restored if the expand() API is used.\n        panelSizeBeforeCollapseRef.current.set(panelData.id, panelSize);\n        const isLastPanel = findPanelDataIndex(panelDataArray, panelData) === panelDataArray.length - 1;\n        const delta = isLastPanel ? panelSize - collapsedSize : collapsedSize - panelSize;\n        const nextLayout = adjustLayoutByDelta({\n          delta,\n          initialLayout: prevLayout,\n          panelConstraints: panelConstraintsArray,\n          pivotIndices,\n          prevLayout,\n          trigger: \"imperative-api\"\n        });\n        if (!compareLayouts(prevLayout, nextLayout)) {\n          setLayout(nextLayout);\n          eagerValuesRef.current.layout = nextLayout;\n          if (onLayout) {\n            onLayout(nextLayout);\n          }\n          callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n        }\n      }\n    }\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const expandPanel = useCallback((panelData, minSizeOverride) => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    if (panelData.constraints.collapsible) {\n      const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n      const {\n        collapsedSize = 0,\n        panelSize = 0,\n        minSize: minSizeFromProps = 0,\n        pivotIndices\n      } = panelDataHelper(panelDataArray, panelData, prevLayout);\n      const minSize = minSizeOverride !== null && minSizeOverride !== void 0 ? minSizeOverride : minSizeFromProps;\n      if (fuzzyNumbersEqual$1(panelSize, collapsedSize)) {\n        // Restore this panel to the size it was before it was collapsed, if possible.\n        const prevPanelSize = panelSizeBeforeCollapseRef.current.get(panelData.id);\n        const baseSize = prevPanelSize != null && prevPanelSize >= minSize ? prevPanelSize : minSize;\n        const isLastPanel = findPanelDataIndex(panelDataArray, panelData) === panelDataArray.length - 1;\n        const delta = isLastPanel ? panelSize - baseSize : baseSize - panelSize;\n        const nextLayout = adjustLayoutByDelta({\n          delta,\n          initialLayout: prevLayout,\n          panelConstraints: panelConstraintsArray,\n          pivotIndices,\n          prevLayout,\n          trigger: \"imperative-api\"\n        });\n        if (!compareLayouts(prevLayout, nextLayout)) {\n          setLayout(nextLayout);\n          eagerValuesRef.current.layout = nextLayout;\n          if (onLayout) {\n            onLayout(nextLayout);\n          }\n          callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n        }\n      }\n    }\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const getPanelSize = useCallback(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      panelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    return panelSize;\n  }, []);\n\n  // This API should never read from committedValuesRef\n  const getPanelStyle = useCallback((panelData, defaultSize) => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    const panelIndex = findPanelDataIndex(panelDataArray, panelData);\n    return computePanelFlexBoxStyle({\n      defaultSize,\n      dragState,\n      layout,\n      panelData: panelDataArray,\n      panelIndex\n    });\n  }, [dragState, layout]);\n\n  // External APIs are safe to memoize via committed values ref\n  const isPanelCollapsed = useCallback(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSize = 0,\n      collapsible,\n      panelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    return collapsible === true && fuzzyNumbersEqual$1(panelSize, collapsedSize);\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const isPanelExpanded = useCallback(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSize = 0,\n      collapsible,\n      panelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    return !collapsible || fuzzyCompareNumbers(panelSize, collapsedSize) > 0;\n  }, []);\n  const registerPanel = useCallback(panelData => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    panelDataArray.push(panelData);\n    panelDataArray.sort((panelA, panelB) => {\n      const orderA = panelA.order;\n      const orderB = panelB.order;\n      if (orderA == null && orderB == null) {\n        return 0;\n      } else if (orderA == null) {\n        return -1;\n      } else if (orderB == null) {\n        return 1;\n      } else {\n        return orderA - orderB;\n      }\n    });\n    eagerValuesRef.current.panelDataArrayChanged = true;\n    forceUpdate();\n  }, [forceUpdate]);\n  const registerResizeHandle = useCallback(dragHandleId => {\n    let isRTL = false;\n    const panelGroupElement = panelGroupElementRef.current;\n    if (panelGroupElement) {\n      const style = window.getComputedStyle(panelGroupElement, null);\n      if (style.getPropertyValue(\"direction\") === \"rtl\") {\n        isRTL = true;\n      }\n    }\n    return function resizeHandler(event) {\n      event.preventDefault();\n      const panelGroupElement = panelGroupElementRef.current;\n      if (!panelGroupElement) {\n        return () => null;\n      }\n      const {\n        direction,\n        dragState,\n        id: groupId,\n        keyboardResizeBy,\n        onLayout\n      } = committedValuesRef.current;\n      const {\n        layout: prevLayout,\n        panelDataArray\n      } = eagerValuesRef.current;\n      const {\n        initialLayout\n      } = dragState !== null && dragState !== void 0 ? dragState : {};\n      const pivotIndices = determinePivotIndices(groupId, dragHandleId, panelGroupElement);\n      let delta = calculateDeltaPercentage(event, dragHandleId, direction, dragState, keyboardResizeBy, panelGroupElement);\n      const isHorizontal = direction === \"horizontal\";\n      if (isHorizontal && isRTL) {\n        delta = -delta;\n      }\n      const panelConstraints = panelDataArray.map(panelData => panelData.constraints);\n      const nextLayout = adjustLayoutByDelta({\n        delta,\n        initialLayout: initialLayout !== null && initialLayout !== void 0 ? initialLayout : prevLayout,\n        panelConstraints,\n        pivotIndices,\n        prevLayout,\n        trigger: isKeyDown(event) ? \"keyboard\" : \"mouse-or-touch\"\n      });\n      const layoutChanged = !compareLayouts(prevLayout, nextLayout);\n\n      // Only update the cursor for layout changes triggered by touch/mouse events (not keyboard)\n      // Update the cursor even if the layout hasn't changed (we may need to show an invalid cursor state)\n      if (isPointerEvent(event) || isMouseEvent(event)) {\n        // Watch for multiple subsequent deltas; this might occur for tiny cursor movements.\n        // In this case, Panel sizes might not change–\n        // but updating cursor in this scenario would cause a flicker.\n        if (prevDeltaRef.current != delta) {\n          prevDeltaRef.current = delta;\n          if (!layoutChanged && delta !== 0) {\n            // If the pointer has moved too far to resize the panel any further, note this so we can update the cursor.\n            // This mimics VS Code behavior.\n            if (isHorizontal) {\n              reportConstraintsViolation(dragHandleId, delta < 0 ? EXCEEDED_HORIZONTAL_MIN : EXCEEDED_HORIZONTAL_MAX);\n            } else {\n              reportConstraintsViolation(dragHandleId, delta < 0 ? EXCEEDED_VERTICAL_MIN : EXCEEDED_VERTICAL_MAX);\n            }\n          } else {\n            reportConstraintsViolation(dragHandleId, 0);\n          }\n        }\n      }\n      if (layoutChanged) {\n        setLayout(nextLayout);\n        eagerValuesRef.current.layout = nextLayout;\n        if (onLayout) {\n          onLayout(nextLayout);\n        }\n        callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n      }\n    };\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const resizePanel = useCallback((panelData, unsafePanelSize) => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n    const {\n      panelSize,\n      pivotIndices\n    } = panelDataHelper(panelDataArray, panelData, prevLayout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    const isLastPanel = findPanelDataIndex(panelDataArray, panelData) === panelDataArray.length - 1;\n    const delta = isLastPanel ? panelSize - unsafePanelSize : unsafePanelSize - panelSize;\n    const nextLayout = adjustLayoutByDelta({\n      delta,\n      initialLayout: prevLayout,\n      panelConstraints: panelConstraintsArray,\n      pivotIndices,\n      prevLayout,\n      trigger: \"imperative-api\"\n    });\n    if (!compareLayouts(prevLayout, nextLayout)) {\n      setLayout(nextLayout);\n      eagerValuesRef.current.layout = nextLayout;\n      if (onLayout) {\n        onLayout(nextLayout);\n      }\n      callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n    }\n  }, []);\n  const reevaluatePanelConstraints = useCallback((panelData, prevConstraints) => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSize: prevCollapsedSize = 0,\n      collapsible: prevCollapsible\n    } = prevConstraints;\n    const {\n      collapsedSize: nextCollapsedSize = 0,\n      collapsible: nextCollapsible,\n      maxSize: nextMaxSize = 100,\n      minSize: nextMinSize = 0\n    } = panelData.constraints;\n    const {\n      panelSize: prevPanelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    if (prevPanelSize == null) {\n      // It's possible that the panels in this group have changed since the last render\n      return;\n    }\n    if (prevCollapsible && nextCollapsible && fuzzyNumbersEqual$1(prevPanelSize, prevCollapsedSize)) {\n      if (!fuzzyNumbersEqual$1(prevCollapsedSize, nextCollapsedSize)) {\n        resizePanel(panelData, nextCollapsedSize);\n      }\n    } else if (prevPanelSize < nextMinSize) {\n      resizePanel(panelData, nextMinSize);\n    } else if (prevPanelSize > nextMaxSize) {\n      resizePanel(panelData, nextMaxSize);\n    }\n  }, [resizePanel]);\n\n  // TODO Multiple drag handles can be active at the same time so this API is a bit awkward now\n  const startDragging = useCallback((dragHandleId, event) => {\n    const {\n      direction\n    } = committedValuesRef.current;\n    const {\n      layout\n    } = eagerValuesRef.current;\n    if (!panelGroupElementRef.current) {\n      return;\n    }\n    const handleElement = getResizeHandleElement(dragHandleId, panelGroupElementRef.current);\n    assert(handleElement, `Drag handle element not found for id \"${dragHandleId}\"`);\n    const initialCursorPosition = getResizeEventCursorPosition(direction, event);\n    setDragState({\n      dragHandleId,\n      dragHandleRect: handleElement.getBoundingClientRect(),\n      initialCursorPosition,\n      initialLayout: layout\n    });\n  }, []);\n  const stopDragging = useCallback(() => {\n    setDragState(null);\n  }, []);\n  const unregisterPanel = useCallback(panelData => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    const index = findPanelDataIndex(panelDataArray, panelData);\n    if (index >= 0) {\n      panelDataArray.splice(index, 1);\n\n      // TRICKY\n      // When a panel is removed from the group, we should delete the most recent prev-size entry for it.\n      // If we don't do this, then a conditionally rendered panel might not call onResize when it's re-mounted.\n      // Strict effects mode makes this tricky though because all panels will be registered, unregistered, then re-registered on mount.\n      delete panelIdToLastNotifiedSizeMapRef.current[panelData.id];\n      eagerValuesRef.current.panelDataArrayChanged = true;\n      forceUpdate();\n    }\n  }, [forceUpdate]);\n  const context = useMemo(() => ({\n    collapsePanel,\n    direction,\n    dragState,\n    expandPanel,\n    getPanelSize,\n    getPanelStyle,\n    groupId,\n    isPanelCollapsed,\n    isPanelExpanded,\n    reevaluatePanelConstraints,\n    registerPanel,\n    registerResizeHandle,\n    resizePanel,\n    startDragging,\n    stopDragging,\n    unregisterPanel,\n    panelGroupElement: panelGroupElementRef.current\n  }), [collapsePanel, dragState, direction, expandPanel, getPanelSize, getPanelStyle, groupId, isPanelCollapsed, isPanelExpanded, reevaluatePanelConstraints, registerPanel, registerResizeHandle, resizePanel, startDragging, stopDragging, unregisterPanel]);\n  const style = {\n    display: \"flex\",\n    flexDirection: direction === \"horizontal\" ? \"row\" : \"column\",\n    height: \"100%\",\n    overflow: \"hidden\",\n    width: \"100%\"\n  };\n  return createElement(PanelGroupContext.Provider, {\n    value: context\n  }, createElement(Type, {\n    ...rest,\n    children,\n    className: classNameFromProps,\n    id: idFromProps,\n    ref: panelGroupElementRef,\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    // CSS selectors\n    \"data-panel-group\": \"\",\n    \"data-panel-group-direction\": direction,\n    \"data-panel-group-id\": groupId\n  }));\n}\nconst PanelGroup = forwardRef((props, ref) => createElement(PanelGroupWithForwardedRef, {\n  ...props,\n  forwardedRef: ref\n}));\nPanelGroupWithForwardedRef.displayName = \"PanelGroup\";\nPanelGroup.displayName = \"forwardRef(PanelGroup)\";\nfunction findPanelDataIndex(panelDataArray, panelData) {\n  return panelDataArray.findIndex(prevPanelData => prevPanelData === panelData || prevPanelData.id === panelData.id);\n}\nfunction panelDataHelper(panelDataArray, panelData, layout) {\n  const panelIndex = findPanelDataIndex(panelDataArray, panelData);\n  const isLastPanel = panelIndex === panelDataArray.length - 1;\n  const pivotIndices = isLastPanel ? [panelIndex - 1, panelIndex] : [panelIndex, panelIndex + 1];\n  const panelSize = layout[panelIndex];\n  return {\n    ...panelData.constraints,\n    panelSize,\n    pivotIndices\n  };\n}\n\n// https://www.w3.org/WAI/ARIA/apg/patterns/windowsplitter/\n\nfunction useWindowSplitterResizeHandlerBehavior({\n  disabled,\n  handleId,\n  resizeHandler,\n  panelGroupElement\n}) {\n  useEffect(() => {\n    if (disabled || resizeHandler == null || panelGroupElement == null) {\n      return;\n    }\n    const handleElement = getResizeHandleElement(handleId, panelGroupElement);\n    if (handleElement == null) {\n      return;\n    }\n    const onKeyDown = event => {\n      if (event.defaultPrevented) {\n        return;\n      }\n      switch (event.key) {\n        case \"ArrowDown\":\n        case \"ArrowLeft\":\n        case \"ArrowRight\":\n        case \"ArrowUp\":\n        case \"End\":\n        case \"Home\":\n          {\n            event.preventDefault();\n            resizeHandler(event);\n            break;\n          }\n        case \"F6\":\n          {\n            event.preventDefault();\n            const groupId = handleElement.getAttribute(\"data-panel-group-id\");\n            assert(groupId, `No group element found for id \"${groupId}\"`);\n            const handles = getResizeHandleElementsForGroup(groupId, panelGroupElement);\n            const index = getResizeHandleElementIndex(groupId, handleId, panelGroupElement);\n            assert(index !== null, `No resize element found for id \"${handleId}\"`);\n            const nextIndex = event.shiftKey ? index > 0 ? index - 1 : handles.length - 1 : index + 1 < handles.length ? index + 1 : 0;\n            const nextHandle = handles[nextIndex];\n            nextHandle.focus();\n            break;\n          }\n      }\n    };\n    handleElement.addEventListener(\"keydown\", onKeyDown);\n    return () => {\n      handleElement.removeEventListener(\"keydown\", onKeyDown);\n    };\n  }, [panelGroupElement, disabled, handleId, resizeHandler]);\n}\n\nfunction PanelResizeHandle({\n  children = null,\n  className: classNameFromProps = \"\",\n  disabled = false,\n  hitAreaMargins,\n  id: idFromProps,\n  onBlur,\n  onDragging,\n  onFocus,\n  style: styleFromProps = {},\n  tabIndex = 0,\n  tagName: Type = \"div\",\n  ...rest\n}) {\n  var _hitAreaMargins$coars, _hitAreaMargins$fine;\n  const elementRef = useRef(null);\n\n  // Use a ref to guard against users passing inline props\n  const callbacksRef = useRef({\n    onDragging\n  });\n  useEffect(() => {\n    callbacksRef.current.onDragging = onDragging;\n  });\n  const panelGroupContext = useContext(PanelGroupContext);\n  if (panelGroupContext === null) {\n    throw Error(`PanelResizeHandle components must be rendered within a PanelGroup container`);\n  }\n  const {\n    direction,\n    groupId,\n    registerResizeHandle: registerResizeHandleWithParentGroup,\n    startDragging,\n    stopDragging,\n    panelGroupElement\n  } = panelGroupContext;\n  const resizeHandleId = useUniqueId(idFromProps);\n  const [state, setState] = useState(\"inactive\");\n  const [isFocused, setIsFocused] = useState(false);\n  const [resizeHandler, setResizeHandler] = useState(null);\n  const committedValuesRef = useRef({\n    state\n  });\n  useEffect(() => {\n    if (disabled) {\n      setResizeHandler(null);\n    } else {\n      const resizeHandler = registerResizeHandleWithParentGroup(resizeHandleId);\n      setResizeHandler(() => resizeHandler);\n    }\n  }, [disabled, resizeHandleId, registerResizeHandleWithParentGroup]);\n\n  // Extract hit area margins before passing them to the effect's dependency array\n  // so that inline object values won't trigger re-renders\n  const coarseHitAreaMargins = (_hitAreaMargins$coars = hitAreaMargins === null || hitAreaMargins === void 0 ? void 0 : hitAreaMargins.coarse) !== null && _hitAreaMargins$coars !== void 0 ? _hitAreaMargins$coars : 15;\n  const fineHitAreaMargins = (_hitAreaMargins$fine = hitAreaMargins === null || hitAreaMargins === void 0 ? void 0 : hitAreaMargins.fine) !== null && _hitAreaMargins$fine !== void 0 ? _hitAreaMargins$fine : 5;\n  useEffect(() => {\n    if (disabled || resizeHandler == null) {\n      return;\n    }\n    const element = elementRef.current;\n    assert(element, \"Element ref not attached\");\n    const setResizeHandlerState = (action, isActive, event) => {\n      if (isActive) {\n        switch (action) {\n          case \"down\":\n            {\n              setState(\"drag\");\n              assert(event, 'Expected event to be defined for \"down\" action');\n              startDragging(resizeHandleId, event);\n              const {\n                onDragging\n              } = callbacksRef.current;\n              if (onDragging) {\n                onDragging(true);\n              }\n              break;\n            }\n          case \"move\":\n            {\n              const {\n                state\n              } = committedValuesRef.current;\n              if (state !== \"drag\") {\n                setState(\"hover\");\n              }\n              assert(event, 'Expected event to be defined for \"move\" action');\n              resizeHandler(event);\n              break;\n            }\n          case \"up\":\n            {\n              setState(\"hover\");\n              stopDragging();\n              const {\n                onDragging\n              } = callbacksRef.current;\n              if (onDragging) {\n                onDragging(false);\n              }\n              break;\n            }\n        }\n      } else {\n        setState(\"inactive\");\n      }\n    };\n    return registerResizeHandle(resizeHandleId, element, direction, {\n      coarse: coarseHitAreaMargins,\n      fine: fineHitAreaMargins\n    }, setResizeHandlerState);\n  }, [coarseHitAreaMargins, direction, disabled, fineHitAreaMargins, registerResizeHandleWithParentGroup, resizeHandleId, resizeHandler, startDragging, stopDragging]);\n  useWindowSplitterResizeHandlerBehavior({\n    disabled,\n    handleId: resizeHandleId,\n    resizeHandler,\n    panelGroupElement\n  });\n  const style = {\n    touchAction: \"none\",\n    userSelect: \"none\"\n  };\n  return createElement(Type, {\n    ...rest,\n    children,\n    className: classNameFromProps,\n    id: idFromProps,\n    onBlur: () => {\n      setIsFocused(false);\n      onBlur === null || onBlur === void 0 ? void 0 : onBlur();\n    },\n    onFocus: () => {\n      setIsFocused(true);\n      onFocus === null || onFocus === void 0 ? void 0 : onFocus();\n    },\n    ref: elementRef,\n    role: \"separator\",\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    tabIndex,\n    // CSS selectors\n    \"data-panel-group-direction\": direction,\n    \"data-panel-group-id\": groupId,\n    \"data-resize-handle\": \"\",\n    \"data-resize-handle-active\": state === \"drag\" ? \"pointer\" : isFocused ? \"keyboard\" : undefined,\n    \"data-resize-handle-state\": state,\n    \"data-panel-resize-handle-enabled\": !disabled,\n    \"data-panel-resize-handle-id\": resizeHandleId\n  });\n}\nPanelResizeHandle.displayName = \"PanelResizeHandle\";\n\nfunction getPanelElement(id, scope = document) {\n  const element = scope.querySelector(`[data-panel-id=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction getPanelElementsForGroup(groupId, scope = document) {\n  return Array.from(scope.querySelectorAll(`[data-panel][data-panel-group-id=\"${groupId}\"]`));\n}\n\nfunction getIntersectingRectangle(rectOne, rectTwo, strict) {\n  if (!intersects(rectOne, rectTwo, strict)) {\n    return {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n  }\n  return {\n    x: Math.max(rectOne.x, rectTwo.x),\n    y: Math.max(rectOne.y, rectTwo.y),\n    width: Math.min(rectOne.x + rectOne.width, rectTwo.x + rectTwo.width) - Math.max(rectOne.x, rectTwo.x),\n    height: Math.min(rectOne.y + rectOne.height, rectTwo.y + rectTwo.height) - Math.max(rectOne.y, rectTwo.y)\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-resizable-panels/dist/react-resizable-panels.development.node.esm.js\n");

/***/ })

};
;