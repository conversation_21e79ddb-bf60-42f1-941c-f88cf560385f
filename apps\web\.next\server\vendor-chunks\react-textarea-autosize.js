"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-textarea-autosize";
exports.ids = ["vendor-chunks/react-textarea-autosize"];
exports.modules = {

/***/ "(ssr)/../../node_modules/react-textarea-autosize/dist/react-textarea-autosize.development.esm.js":
/*!**************************************************************************************************!*\
  !*** ../../node_modules/react-textarea-autosize/dist/react-textarea-autosize.development.esm.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ index)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/../../node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutPropertiesLoose */ \"(ssr)/../../node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var use_composed_ref__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! use-composed-ref */ \"(ssr)/../../node_modules/use-composed-ref/dist/use-composed-ref.esm.js\");\n\n\n\n\n\nvar noop = function noop() {};\n\nvar _excluded = [\"cacheMeasurements\", \"maxRows\", \"minRows\", \"onChange\", \"onHeightChange\"];\nvar TextareaAutosize = function TextareaAutosize(_ref, userRef) {\n  _ref.cacheMeasurements;\n    _ref.maxRows;\n    _ref.minRows;\n    var _ref$onChange = _ref.onChange,\n    onChange = _ref$onChange === void 0 ? noop : _ref$onChange;\n    _ref.onHeightChange;\n    var props = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_ref, _excluded);\n  if (props.style) {\n    if ('maxHeight' in props.style) {\n      throw new Error('Using `style.maxHeight` for <TextareaAutosize/> is not supported. Please use `maxRows`.');\n    }\n    if ('minHeight' in props.style) {\n      throw new Error('Using `style.minHeight` for <TextareaAutosize/> is not supported. Please use `minRows`.');\n    }\n  }\n  props.value !== undefined;\n  var libRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n  var ref = (0,use_composed_ref__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(libRef, userRef);\n  react__WEBPACK_IMPORTED_MODULE_2__.useRef(0);\n  react__WEBPACK_IMPORTED_MODULE_2__.useRef();\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"textarea\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n    onChange: onChange,\n    ref: ref\n  }));\n};\nvar index = /* #__PURE__ */react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(TextareaAutosize);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-textarea-autosize/dist/react-textarea-autosize.development.esm.js\n");

/***/ })

};
;