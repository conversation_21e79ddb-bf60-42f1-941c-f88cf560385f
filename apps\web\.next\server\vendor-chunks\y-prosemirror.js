"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/y-prosemirror";
exports.ids = ["vendor-chunks/y-prosemirror"];
exports.modules = {

/***/ "(ssr)/../../node_modules/y-prosemirror/src/lib.js":
/*!***************************************************!*\
  !*** ../../node_modules/y-prosemirror/src/lib.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   absolutePositionToRelativePosition: () => (/* binding */ absolutePositionToRelativePosition),\n/* harmony export */   initProseMirrorDoc: () => (/* binding */ initProseMirrorDoc),\n/* harmony export */   prosemirrorJSONToYDoc: () => (/* binding */ prosemirrorJSONToYDoc),\n/* harmony export */   prosemirrorJSONToYXmlFragment: () => (/* binding */ prosemirrorJSONToYXmlFragment),\n/* harmony export */   prosemirrorToYDoc: () => (/* binding */ prosemirrorToYDoc),\n/* harmony export */   prosemirrorToYXmlFragment: () => (/* binding */ prosemirrorToYXmlFragment),\n/* harmony export */   relativePositionToAbsolutePosition: () => (/* binding */ relativePositionToAbsolutePosition),\n/* harmony export */   setMeta: () => (/* binding */ setMeta),\n/* harmony export */   yDocToProsemirror: () => (/* binding */ yDocToProsemirror),\n/* harmony export */   yDocToProsemirrorJSON: () => (/* binding */ yDocToProsemirrorJSON),\n/* harmony export */   yXmlFragmentToProseMirrorFragment: () => (/* binding */ yXmlFragmentToProseMirrorFragment),\n/* harmony export */   yXmlFragmentToProseMirrorRootNode: () => (/* binding */ yXmlFragmentToProseMirrorRootNode),\n/* harmony export */   yXmlFragmentToProsemirror: () => (/* binding */ yXmlFragmentToProsemirror),\n/* harmony export */   yXmlFragmentToProsemirrorJSON: () => (/* binding */ yXmlFragmentToProsemirrorJSON)\n/* harmony export */ });\n/* harmony import */ var _plugins_sync_plugin_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./plugins/sync-plugin.js */ \"(ssr)/../../node_modules/y-prosemirror/src/plugins/sync-plugin.js\");\n/* harmony import */ var _plugins_keys_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./plugins/keys.js */ \"(ssr)/../../node_modules/y-prosemirror/src/plugins/keys.js\");\n/* harmony import */ var yjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! yjs */ \"(ssr)/../../node_modules/yjs/dist/yjs.mjs\");\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/../../node_modules/prosemirror-model/dist/index.js\");\n/* harmony import */ var lib0_error__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lib0/error */ \"(ssr)/../../node_modules/lib0/error.js\");\n/* harmony import */ var lib0_map__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lib0/map */ \"(ssr)/../../node_modules/lib0/map.js\");\n/* harmony import */ var lib0_eventloop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lib0/eventloop */ \"(ssr)/../../node_modules/lib0/eventloop.js\");\n // eslint-disable-line\n\n\n // eslint-disable-line\n // eslint-disable-line\n\n\n\n\n/**\n * Either a node if type is YXmlElement or an Array of text nodes if YXmlText\n * @typedef {Map<Y.AbstractType, Node | Array<Node>>} ProsemirrorMapping\n */\n\n/**\n * Is null if no timeout is in progress.\n * Is defined if a timeout is in progress.\n * Maps from view\n * @type {Map<EditorView, Map<any, any>>|null}\n */\nlet viewsToUpdate = null\n\nconst updateMetas = () => {\n  const ups = /** @type {Map<EditorView, Map<any, any>>} */ (viewsToUpdate)\n  viewsToUpdate = null\n  ups.forEach((metas, view) => {\n    const tr = view.state.tr\n    const syncState = _plugins_keys_js__WEBPACK_IMPORTED_MODULE_0__.ySyncPluginKey.getState(view.state)\n    if (syncState && syncState.binding && !syncState.binding.isDestroyed) {\n      metas.forEach((val, key) => {\n        tr.setMeta(key, val)\n      })\n      view.dispatch(tr)\n    }\n  })\n}\n\nconst setMeta = (view, key, value) => {\n  if (!viewsToUpdate) {\n    viewsToUpdate = new Map()\n    lib0_eventloop__WEBPACK_IMPORTED_MODULE_1__.timeout(0, updateMetas)\n  }\n  lib0_map__WEBPACK_IMPORTED_MODULE_2__.setIfUndefined(viewsToUpdate, view, lib0_map__WEBPACK_IMPORTED_MODULE_2__.create).set(key, value)\n}\n\n/**\n * Transforms a Prosemirror based absolute position to a Yjs Cursor (relative position in the Yjs model).\n *\n * @param {number} pos\n * @param {Y.XmlFragment} type\n * @param {ProsemirrorMapping} mapping\n * @return {any} relative position\n */\nconst absolutePositionToRelativePosition = (pos, type, mapping) => {\n  if (pos === 0) {\n    return yjs__WEBPACK_IMPORTED_MODULE_3__.createRelativePositionFromTypeIndex(type, 0, -1)\n  }\n  /**\n   * @type {any}\n   */\n  let n = type._first === null ? null : /** @type {Y.ContentType} */ (type._first.content).type\n  while (n !== null && type !== n) {\n    if (n instanceof yjs__WEBPACK_IMPORTED_MODULE_3__.XmlText) {\n      if (n._length >= pos) {\n        return yjs__WEBPACK_IMPORTED_MODULE_3__.createRelativePositionFromTypeIndex(n, pos, -1)\n      } else {\n        pos -= n._length\n      }\n      if (n._item !== null && n._item.next !== null) {\n        n = /** @type {Y.ContentType} */ (n._item.next.content).type\n      } else {\n        do {\n          n = n._item === null ? null : n._item.parent\n          pos--\n        } while (n !== type && n !== null && n._item !== null && n._item.next === null)\n        if (n !== null && n !== type) {\n          // @ts-gnore we know that n.next !== null because of above loop conditition\n          n = n._item === null ? null : /** @type {Y.ContentType} */ (/** @type Y.Item */ (n._item.next).content).type\n        }\n      }\n    } else {\n      const pNodeSize = /** @type {any} */ (mapping.get(n) || { nodeSize: 0 }).nodeSize\n      if (n._first !== null && pos < pNodeSize) {\n        n = /** @type {Y.ContentType} */ (n._first.content).type\n        pos--\n      } else {\n        if (pos === 1 && n._length === 0 && pNodeSize > 1) {\n          // edge case, should end in this paragraph\n          return new yjs__WEBPACK_IMPORTED_MODULE_3__.RelativePosition(n._item === null ? null : n._item.id, n._item === null ? yjs__WEBPACK_IMPORTED_MODULE_3__.findRootTypeKey(n) : null, null)\n        }\n        pos -= pNodeSize\n        if (n._item !== null && n._item.next !== null) {\n          n = /** @type {Y.ContentType} */ (n._item.next.content).type\n        } else {\n          if (pos === 0) {\n            // set to end of n.parent\n            n = n._item === null ? n : n._item.parent\n            return new yjs__WEBPACK_IMPORTED_MODULE_3__.RelativePosition(n._item === null ? null : n._item.id, n._item === null ? yjs__WEBPACK_IMPORTED_MODULE_3__.findRootTypeKey(n) : null, null)\n          }\n          do {\n            n = /** @type {Y.Item} */ (n._item).parent\n            pos--\n          } while (n !== type && /** @type {Y.Item} */ (n._item).next === null)\n          // if n is null at this point, we have an unexpected case\n          if (n !== type) {\n            // We know that n._item.next is defined because of above loop condition\n            n = /** @type {Y.ContentType} */ (/** @type {Y.Item} */ (/** @type {Y.Item} */ (n._item).next).content).type\n          }\n        }\n      }\n    }\n    if (n === null) {\n      throw lib0_error__WEBPACK_IMPORTED_MODULE_4__.unexpectedCase()\n    }\n    if (pos === 0 && n.constructor !== yjs__WEBPACK_IMPORTED_MODULE_3__.XmlText && n !== type) { // TODO: set to <= 0\n      return createRelativePosition(n._item.parent, n._item)\n    }\n  }\n  return yjs__WEBPACK_IMPORTED_MODULE_3__.createRelativePositionFromTypeIndex(type, type._length, -1)\n}\n\nconst createRelativePosition = (type, item) => {\n  let typeid = null\n  let tname = null\n  if (type._item === null) {\n    tname = yjs__WEBPACK_IMPORTED_MODULE_3__.findRootTypeKey(type)\n  } else {\n    typeid = yjs__WEBPACK_IMPORTED_MODULE_3__.createID(type._item.id.client, type._item.id.clock)\n  }\n  return new yjs__WEBPACK_IMPORTED_MODULE_3__.RelativePosition(typeid, tname, item.id)\n}\n\n/**\n * @param {Y.Doc} y\n * @param {Y.XmlFragment} documentType Top level type that is bound to pView\n * @param {any} relPos Encoded Yjs based relative position\n * @param {ProsemirrorMapping} mapping\n * @return {null|number}\n */\nconst relativePositionToAbsolutePosition = (y, documentType, relPos, mapping) => {\n  const decodedPos = yjs__WEBPACK_IMPORTED_MODULE_3__.createAbsolutePositionFromRelativePosition(relPos, y)\n  if (decodedPos === null || (decodedPos.type !== documentType && !yjs__WEBPACK_IMPORTED_MODULE_3__.isParentOf(documentType, decodedPos.type._item))) {\n    return null\n  }\n  let type = decodedPos.type\n  let pos = 0\n  if (type.constructor === yjs__WEBPACK_IMPORTED_MODULE_3__.XmlText) {\n    pos = decodedPos.index\n  } else if (type._item === null || !type._item.deleted) {\n    let n = type._first\n    let i = 0\n    while (i < type._length && i < decodedPos.index && n !== null) {\n      if (!n.deleted) {\n        const t = /** @type {Y.ContentType} */ (n.content).type\n        i++\n        if (t instanceof yjs__WEBPACK_IMPORTED_MODULE_3__.XmlText) {\n          pos += t._length\n        } else {\n          pos += /** @type {any} */ (mapping.get(t)).nodeSize\n        }\n      }\n      n = /** @type {Y.Item} */ (n.right)\n    }\n    pos += 1 // increase because we go out of n\n  }\n  while (type !== documentType && type._item !== null) {\n    // @ts-ignore\n    const parent = type._item.parent\n    // @ts-ignore\n    if (parent._item === null || !parent._item.deleted) {\n      pos += 1 // the start tag\n      let n = /** @type {Y.AbstractType} */ (parent)._first\n      // now iterate until we found type\n      while (n !== null) {\n        const contentType = /** @type {Y.ContentType} */ (n.content).type\n        if (contentType === type) {\n          break\n        }\n        if (!n.deleted) {\n          if (contentType instanceof yjs__WEBPACK_IMPORTED_MODULE_3__.XmlText) {\n            pos += contentType._length\n          } else {\n            pos += /** @type {any} */ (mapping.get(contentType)).nodeSize\n          }\n        }\n        n = n.right\n      }\n    }\n    type = /** @type {Y.AbstractType} */ (parent)\n  }\n  return pos - 1 // we don't count the most outer tag, because it is a fragment\n}\n\n/**\n * Utility function for converting an Y.Fragment to a ProseMirror fragment.\n *\n * @param {Y.XmlFragment} yXmlFragment\n * @param {Schema} schema\n */\nconst yXmlFragmentToProseMirrorFragment = (yXmlFragment, schema) => {\n  const fragmentContent = yXmlFragment.toArray().map((t) =>\n    (0,_plugins_sync_plugin_js__WEBPACK_IMPORTED_MODULE_5__.createNodeFromYElement)(\n      /** @type {Y.XmlElement} */ (t),\n      schema,\n      new Map()\n    )\n  ).filter((n) => n !== null)\n  return prosemirror_model__WEBPACK_IMPORTED_MODULE_6__.Fragment.fromArray(fragmentContent)\n}\n\n/**\n * Utility function for converting an Y.Fragment to a ProseMirror node.\n *\n * @param {Y.XmlFragment} yXmlFragment\n * @param {Schema} schema\n */\nconst yXmlFragmentToProseMirrorRootNode = (yXmlFragment, schema) =>\n  schema.topNodeType.create(null, yXmlFragmentToProseMirrorFragment(yXmlFragment, schema))\n\n/**\n * The initial ProseMirror content should be supplied by Yjs. This function transforms a Y.Fragment\n * to a ProseMirror Doc node and creates a mapping that is used by the sync plugin.\n *\n * @param {Y.XmlFragment} yXmlFragment\n * @param {Schema} schema\n */\nconst initProseMirrorDoc = (yXmlFragment, schema) => {\n  /**\n   * @type {ProsemirrorMapping}\n   */\n  const mapping = new Map()\n  const fragmentContent = yXmlFragment.toArray().map((t) =>\n    (0,_plugins_sync_plugin_js__WEBPACK_IMPORTED_MODULE_5__.createNodeFromYElement)(\n      /** @type {Y.XmlElement} */ (t),\n      schema,\n      mapping\n    )\n  ).filter((n) => n !== null)\n  const doc = schema.topNodeType.create(null, prosemirror_model__WEBPACK_IMPORTED_MODULE_6__.Fragment.fromArray(fragmentContent))\n  return { doc, mapping }\n}\n\n/**\n * Utility method to convert a Prosemirror Doc Node into a Y.Doc.\n *\n * This can be used when importing existing content to Y.Doc for the first time,\n * note that this should not be used to rehydrate a Y.Doc from a database once\n * collaboration has begun as all history will be lost\n *\n * @param {Node} doc\n * @param {string} xmlFragment\n * @return {Y.Doc}\n */\nfunction prosemirrorToYDoc (doc, xmlFragment = 'prosemirror') {\n  const ydoc = new yjs__WEBPACK_IMPORTED_MODULE_3__.Doc()\n  const type = /** @type {Y.XmlFragment} */ (ydoc.get(xmlFragment, yjs__WEBPACK_IMPORTED_MODULE_3__.XmlFragment))\n  if (!type.doc) {\n    return ydoc\n  }\n\n  prosemirrorToYXmlFragment(doc, type)\n  return type.doc\n}\n\n/**\n * Utility method to update an empty Y.XmlFragment with content from a Prosemirror Doc Node.\n *\n * This can be used when importing existing content to Y.Doc for the first time,\n * note that this should not be used to rehydrate a Y.Doc from a database once\n * collaboration has begun as all history will be lost\n *\n * Note: The Y.XmlFragment does not need to be part of a Y.Doc document at the time that this\n * method is called, but it must be added before any other operations are performed on it.\n *\n * @param {Node} doc prosemirror document.\n * @param {Y.XmlFragment} [xmlFragment] If supplied, an xml fragment to be\n *   populated from the prosemirror state; otherwise a new XmlFragment will be created.\n * @return {Y.XmlFragment}\n */\nfunction prosemirrorToYXmlFragment (doc, xmlFragment) {\n  const type = xmlFragment || new yjs__WEBPACK_IMPORTED_MODULE_3__.XmlFragment()\n  const ydoc = type.doc ? type.doc : { transact: (transaction) => transaction(undefined) }\n  ;(0,_plugins_sync_plugin_js__WEBPACK_IMPORTED_MODULE_5__.updateYFragment)(ydoc, type, doc, new Map())\n  return type\n}\n\n/**\n * Utility method to convert Prosemirror compatible JSON into a Y.Doc.\n *\n * This can be used when importing existing content to Y.Doc for the first time,\n * note that this should not be used to rehydrate a Y.Doc from a database once\n * collaboration has begun as all history will be lost\n *\n * @param {Schema} schema\n * @param {any} state\n * @param {string} xmlFragment\n * @return {Y.Doc}\n */\nfunction prosemirrorJSONToYDoc (schema, state, xmlFragment = 'prosemirror') {\n  const doc = prosemirror_model__WEBPACK_IMPORTED_MODULE_6__.Node.fromJSON(schema, state)\n  return prosemirrorToYDoc(doc, xmlFragment)\n}\n\n/**\n * Utility method to convert Prosemirror compatible JSON to a Y.XmlFragment\n *\n * This can be used when importing existing content to Y.Doc for the first time,\n * note that this should not be used to rehydrate a Y.Doc from a database once\n * collaboration has begun as all history will be lost\n *\n * @param {Schema} schema\n * @param {any} state\n * @param {Y.XmlFragment} [xmlFragment] If supplied, an xml fragment to be\n *   populated from the prosemirror state; otherwise a new XmlFragment will be created.\n * @return {Y.XmlFragment}\n */\nfunction prosemirrorJSONToYXmlFragment (schema, state, xmlFragment) {\n  const doc = prosemirror_model__WEBPACK_IMPORTED_MODULE_6__.Node.fromJSON(schema, state)\n  return prosemirrorToYXmlFragment(doc, xmlFragment)\n}\n\n/**\n * @deprecated Use `yXmlFragmentToProseMirrorRootNode` instead\n *\n * Utility method to convert a Y.Doc to a Prosemirror Doc node.\n *\n * @param {Schema} schema\n * @param {Y.Doc} ydoc\n * @return {Node}\n */\nfunction yDocToProsemirror (schema, ydoc) {\n  const state = yDocToProsemirrorJSON(ydoc)\n  return prosemirror_model__WEBPACK_IMPORTED_MODULE_6__.Node.fromJSON(schema, state)\n}\n\n/**\n *\n * @deprecated Use `yXmlFragmentToProseMirrorRootNode` instead\n *\n * Utility method to convert a Y.XmlFragment to a Prosemirror Doc node.\n *\n * @param {Schema} schema\n * @param {Y.XmlFragment} xmlFragment\n * @return {Node}\n */\nfunction yXmlFragmentToProsemirror (schema, xmlFragment) {\n  const state = yXmlFragmentToProsemirrorJSON(xmlFragment)\n  return prosemirror_model__WEBPACK_IMPORTED_MODULE_6__.Node.fromJSON(schema, state)\n}\n\n/**\n *\n * @deprecated Use `yXmlFragmentToProseMirrorRootNode` instead\n *\n * Utility method to convert a Y.Doc to Prosemirror compatible JSON.\n *\n * @param {Y.Doc} ydoc\n * @param {string} xmlFragment\n * @return {Record<string, any>}\n */\nfunction yDocToProsemirrorJSON (\n  ydoc,\n  xmlFragment = 'prosemirror'\n) {\n  return yXmlFragmentToProsemirrorJSON(ydoc.getXmlFragment(xmlFragment))\n}\n\n/**\n * @deprecated Use `yXmlFragmentToProseMirrorRootNode` instead\n *\n * Utility method to convert a Y.Doc to Prosemirror compatible JSON.\n *\n * @param {Y.XmlFragment} xmlFragment The fragment, which must be part of a Y.Doc.\n * @return {Record<string, any>}\n */\nfunction yXmlFragmentToProsemirrorJSON (xmlFragment) {\n  const items = xmlFragment.toArray()\n\n  function serialize (item) {\n    /**\n     * @type {Object} NodeObject\n     * @property {string} NodeObject.type\n     * @property {Record<string, string>=} NodeObject.attrs\n     * @property {Array<NodeObject>=} NodeObject.content\n     */\n    let response\n\n    // TODO: Must be a better way to detect text nodes than this\n    if (!item.nodeName) {\n      const delta = item.toDelta()\n      response = delta.map((d) => {\n        const text = {\n          type: 'text',\n          text: d.insert\n        }\n\n        if (d.attributes) {\n          text.marks = Object.keys(d.attributes).map((type) => {\n            const attrs = d.attributes[type]\n            const mark = {\n              type\n            }\n\n            if (Object.keys(attrs)) {\n              mark.attrs = attrs\n            }\n\n            return mark\n          })\n        }\n        return text\n      })\n    } else {\n      response = {\n        type: item.nodeName\n      }\n\n      const attrs = item.getAttributes()\n      if (Object.keys(attrs).length) {\n        response.attrs = attrs\n      }\n\n      const children = item.toArray()\n      if (children.length) {\n        response.content = children.map(serialize).flat()\n      }\n    }\n\n    return response\n  }\n\n  return {\n    type: 'doc',\n    content: items.map(serialize)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/y-prosemirror/src/lib.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/y-prosemirror/src/plugins/cursor-plugin.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/y-prosemirror/src/plugins/cursor-plugin.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDecorations: () => (/* binding */ createDecorations),\n/* harmony export */   defaultAwarenessStateFilter: () => (/* binding */ defaultAwarenessStateFilter),\n/* harmony export */   defaultCursorBuilder: () => (/* binding */ defaultCursorBuilder),\n/* harmony export */   defaultSelectionBuilder: () => (/* binding */ defaultSelectionBuilder),\n/* harmony export */   yCursorPlugin: () => (/* binding */ yCursorPlugin)\n/* harmony export */ });\n/* harmony import */ var yjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! yjs */ \"(ssr)/../../node_modules/yjs/dist/yjs.mjs\");\n/* harmony import */ var prosemirror_view__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prosemirror-view */ \"(ssr)/../../node_modules/prosemirror-view/dist/index.js\");\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/../../node_modules/prosemirror-state/dist/index.js\");\n/* harmony import */ var y_protocols_awareness__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! y-protocols/awareness */ \"(ssr)/../../node_modules/y-protocols/awareness.js\");\n/* harmony import */ var _lib_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib.js */ \"(ssr)/../../node_modules/y-prosemirror/src/lib.js\");\n/* harmony import */ var _keys_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./keys.js */ \"(ssr)/../../node_modules/y-prosemirror/src/plugins/keys.js\");\n/* harmony import */ var lib0_math__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lib0/math */ \"(ssr)/../../node_modules/lib0/math.js\");\n\n // eslint-disable-line\n // eslint-disable-line\n // eslint-disable-line\n\n\n\n\n\n/**\n * Default awareness state filter\n *\n * @param {number} currentClientId current client id\n * @param {number} userClientId user client id\n * @param {any} _user user data\n * @return {boolean}\n */\nconst defaultAwarenessStateFilter = (currentClientId, userClientId, _user) => currentClientId !== userClientId\n\n/**\n * Default generator for a cursor element\n *\n * @param {any} user user data\n * @return {HTMLElement}\n */\nconst defaultCursorBuilder = (user) => {\n  const cursor = document.createElement('span')\n  cursor.classList.add('ProseMirror-yjs-cursor')\n  cursor.setAttribute('style', `border-color: ${user.color}`)\n  const userDiv = document.createElement('div')\n  userDiv.setAttribute('style', `background-color: ${user.color}`)\n  userDiv.insertBefore(document.createTextNode(user.name), null)\n  const nonbreakingSpace1 = document.createTextNode('\\u2060')\n  const nonbreakingSpace2 = document.createTextNode('\\u2060')\n  cursor.insertBefore(nonbreakingSpace1, null)\n  cursor.insertBefore(userDiv, null)\n  cursor.insertBefore(nonbreakingSpace2, null)\n  return cursor\n}\n\n/**\n * Default generator for the selection attributes\n *\n * @param {any} user user data\n * @return {import('prosemirror-view').DecorationAttrs}\n */\nconst defaultSelectionBuilder = (user) => {\n  return {\n    style: `background-color: ${user.color}70`,\n    class: 'ProseMirror-yjs-selection'\n  }\n}\n\nconst rxValidColor = /^#[0-9a-fA-F]{6}$/\n\n/**\n * @param {any} state\n * @param {Awareness} awareness\n * @param {function(number, number, any):boolean} awarenessFilter\n * @param {function({ name: string, color: string }):Element} createCursor\n * @param {function({ name: string, color: string }):import('prosemirror-view').DecorationAttrs} createSelection\n * @return {any} DecorationSet\n */\nconst createDecorations = (\n  state,\n  awareness,\n  awarenessFilter,\n  createCursor,\n  createSelection\n) => {\n  const ystate = _keys_js__WEBPACK_IMPORTED_MODULE_1__.ySyncPluginKey.getState(state)\n  const y = ystate.doc\n  const decorations = []\n  if (\n    ystate.snapshot != null || ystate.prevSnapshot != null ||\n    ystate.binding.mapping.size === 0\n  ) {\n    // do not render cursors while snapshot is active\n    return prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.DecorationSet.create(state.doc, [])\n  }\n  awareness.getStates().forEach((aw, clientId) => {\n    if (!awarenessFilter(y.clientID, clientId, aw)) {\n      return\n    }\n\n    if (aw.cursor != null) {\n      const user = aw.user || {}\n      if (user.color == null) {\n        user.color = '#ffa500'\n      } else if (!rxValidColor.test(user.color)) {\n        // We only support 6-digit RGB colors in y-prosemirror\n        console.warn('A user uses an unsupported color format', user)\n      }\n      if (user.name == null) {\n        user.name = `User: ${clientId}`\n      }\n      let anchor = (0,_lib_js__WEBPACK_IMPORTED_MODULE_3__.relativePositionToAbsolutePosition)(\n        y,\n        ystate.type,\n        yjs__WEBPACK_IMPORTED_MODULE_4__.createRelativePositionFromJSON(aw.cursor.anchor),\n        ystate.binding.mapping\n      )\n      let head = (0,_lib_js__WEBPACK_IMPORTED_MODULE_3__.relativePositionToAbsolutePosition)(\n        y,\n        ystate.type,\n        yjs__WEBPACK_IMPORTED_MODULE_4__.createRelativePositionFromJSON(aw.cursor.head),\n        ystate.binding.mapping\n      )\n      if (anchor !== null && head !== null) {\n        const maxsize = lib0_math__WEBPACK_IMPORTED_MODULE_5__.max(state.doc.content.size - 1, 0)\n        anchor = lib0_math__WEBPACK_IMPORTED_MODULE_5__.min(anchor, maxsize)\n        head = lib0_math__WEBPACK_IMPORTED_MODULE_5__.min(head, maxsize)\n        decorations.push(\n          prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.widget(head, () => createCursor(user), {\n            key: clientId + '',\n            side: 10\n          })\n        )\n        const from = lib0_math__WEBPACK_IMPORTED_MODULE_5__.min(anchor, head)\n        const to = lib0_math__WEBPACK_IMPORTED_MODULE_5__.max(anchor, head)\n        decorations.push(\n          prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.inline(from, to, createSelection(user), {\n            inclusiveEnd: true,\n            inclusiveStart: false\n          })\n        )\n      }\n    }\n  })\n  return prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.DecorationSet.create(state.doc, decorations)\n}\n\n/**\n * A prosemirror plugin that listens to awareness information on Yjs.\n * This requires that a `prosemirrorPlugin` is also bound to the prosemirror.\n *\n * @public\n * @param {Awareness} awareness\n * @param {object} opts\n * @param {function(any, any, any):boolean} [opts.awarenessStateFilter]\n * @param {function(any):HTMLElement} [opts.cursorBuilder]\n * @param {function(any):import('prosemirror-view').DecorationAttrs} [opts.selectionBuilder]\n * @param {function(any):any} [opts.getSelection]\n * @param {string} [cursorStateField] By default all editor bindings use the awareness 'cursor' field to propagate cursor information.\n * @return {any}\n */\nconst yCursorPlugin = (\n  awareness,\n  {\n    awarenessStateFilter = defaultAwarenessStateFilter,\n    cursorBuilder = defaultCursorBuilder,\n    selectionBuilder = defaultSelectionBuilder,\n    getSelection = (state) => state.selection\n  } = {},\n  cursorStateField = 'cursor'\n) =>\n  new prosemirror_state__WEBPACK_IMPORTED_MODULE_6__.Plugin({\n    key: _keys_js__WEBPACK_IMPORTED_MODULE_1__.yCursorPluginKey,\n    state: {\n      init (_, state) {\n        return createDecorations(\n          state,\n          awareness,\n          awarenessStateFilter,\n          cursorBuilder,\n          selectionBuilder\n        )\n      },\n      apply (tr, prevState, _oldState, newState) {\n        const ystate = _keys_js__WEBPACK_IMPORTED_MODULE_1__.ySyncPluginKey.getState(newState)\n        const yCursorState = tr.getMeta(_keys_js__WEBPACK_IMPORTED_MODULE_1__.yCursorPluginKey)\n        if (\n          (ystate && ystate.isChangeOrigin) ||\n          (yCursorState && yCursorState.awarenessUpdated)\n        ) {\n          return createDecorations(\n            newState,\n            awareness,\n            awarenessStateFilter,\n            cursorBuilder,\n            selectionBuilder\n          )\n        }\n        return prevState.map(tr.mapping, tr.doc)\n      }\n    },\n    props: {\n      decorations: (state) => {\n        return _keys_js__WEBPACK_IMPORTED_MODULE_1__.yCursorPluginKey.getState(state)\n      }\n    },\n    view: (view) => {\n      const awarenessListener = () => {\n        // @ts-ignore\n        if (view.docView) {\n          (0,_lib_js__WEBPACK_IMPORTED_MODULE_3__.setMeta)(view, _keys_js__WEBPACK_IMPORTED_MODULE_1__.yCursorPluginKey, { awarenessUpdated: true })\n        }\n      }\n      const updateCursorInfo = () => {\n        const ystate = _keys_js__WEBPACK_IMPORTED_MODULE_1__.ySyncPluginKey.getState(view.state)\n        // @note We make implicit checks when checking for the cursor property\n        const current = awareness.getLocalState() || {}\n        if (view.hasFocus()) {\n          const selection = getSelection(view.state)\n          /**\n           * @type {Y.RelativePosition}\n           */\n          const anchor = (0,_lib_js__WEBPACK_IMPORTED_MODULE_3__.absolutePositionToRelativePosition)(\n            selection.anchor,\n            ystate.type,\n            ystate.binding.mapping\n          )\n          /**\n           * @type {Y.RelativePosition}\n           */\n          const head = (0,_lib_js__WEBPACK_IMPORTED_MODULE_3__.absolutePositionToRelativePosition)(\n            selection.head,\n            ystate.type,\n            ystate.binding.mapping\n          )\n          if (\n            current.cursor == null ||\n            !yjs__WEBPACK_IMPORTED_MODULE_4__.compareRelativePositions(\n              yjs__WEBPACK_IMPORTED_MODULE_4__.createRelativePositionFromJSON(current.cursor.anchor),\n              anchor\n            ) ||\n            !yjs__WEBPACK_IMPORTED_MODULE_4__.compareRelativePositions(\n              yjs__WEBPACK_IMPORTED_MODULE_4__.createRelativePositionFromJSON(current.cursor.head),\n              head\n            )\n          ) {\n            awareness.setLocalStateField(cursorStateField, {\n              anchor,\n              head\n            })\n          }\n        } else if (\n          current.cursor != null &&\n          (0,_lib_js__WEBPACK_IMPORTED_MODULE_3__.relativePositionToAbsolutePosition)(\n            ystate.doc,\n            ystate.type,\n            yjs__WEBPACK_IMPORTED_MODULE_4__.createRelativePositionFromJSON(current.cursor.anchor),\n            ystate.binding.mapping\n          ) !== null\n        ) {\n          // delete cursor information if current cursor information is owned by this editor binding\n          awareness.setLocalStateField(cursorStateField, null)\n        }\n      }\n      awareness.on('change', awarenessListener)\n      view.dom.addEventListener('focusin', updateCursorInfo)\n      view.dom.addEventListener('focusout', updateCursorInfo)\n      return {\n        update: updateCursorInfo,\n        destroy: () => {\n          view.dom.removeEventListener('focusin', updateCursorInfo)\n          view.dom.removeEventListener('focusout', updateCursorInfo)\n          awareness.off('change', awarenessListener)\n          awareness.setLocalStateField(cursorStateField, null)\n        }\n      }\n    }\n  })\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/y-prosemirror/src/plugins/cursor-plugin.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/y-prosemirror/src/plugins/keys.js":
/*!************************************************************!*\
  !*** ../../node_modules/y-prosemirror/src/plugins/keys.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   yCursorPluginKey: () => (/* binding */ yCursorPluginKey),\n/* harmony export */   ySyncPluginKey: () => (/* binding */ ySyncPluginKey),\n/* harmony export */   yUndoPluginKey: () => (/* binding */ yUndoPluginKey)\n/* harmony export */ });\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/../../node_modules/prosemirror-state/dist/index.js\");\n // eslint-disable-line\n\n/**\n * The unique prosemirror plugin key for syncPlugin\n *\n * @public\n */\nconst ySyncPluginKey = new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey('y-sync')\n\n/**\n * The unique prosemirror plugin key for undoPlugin\n *\n * @public\n */\nconst yUndoPluginKey = new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey('y-undo')\n\n/**\n * The unique prosemirror plugin key for cursorPlugin\n *\n * @public\n */\nconst yCursorPluginKey = new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey('yjs-cursor')\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3ktcHJvc2VtaXJyb3Ivc3JjL3BsdWdpbnMva2V5cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTZDOztBQUU3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ08sMkJBQTJCLHdEQUFTOztBQUUzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ08sMkJBQTJCLHdEQUFTOztBQUUzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ08sNkJBQTZCLHdEQUFTIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy95LXByb3NlbWlycm9yL3NyYy9wbHVnaW5zL2tleXMuanM/ZjI1YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQbHVnaW5LZXkgfSBmcm9tICdwcm9zZW1pcnJvci1zdGF0ZScgLy8gZXNsaW50LWRpc2FibGUtbGluZVxuXG4vKipcbiAqIFRoZSB1bmlxdWUgcHJvc2VtaXJyb3IgcGx1Z2luIGtleSBmb3Igc3luY1BsdWdpblxuICpcbiAqIEBwdWJsaWNcbiAqL1xuZXhwb3J0IGNvbnN0IHlTeW5jUGx1Z2luS2V5ID0gbmV3IFBsdWdpbktleSgneS1zeW5jJylcblxuLyoqXG4gKiBUaGUgdW5pcXVlIHByb3NlbWlycm9yIHBsdWdpbiBrZXkgZm9yIHVuZG9QbHVnaW5cbiAqXG4gKiBAcHVibGljXG4gKi9cbmV4cG9ydCBjb25zdCB5VW5kb1BsdWdpbktleSA9IG5ldyBQbHVnaW5LZXkoJ3ktdW5kbycpXG5cbi8qKlxuICogVGhlIHVuaXF1ZSBwcm9zZW1pcnJvciBwbHVnaW4ga2V5IGZvciBjdXJzb3JQbHVnaW5cbiAqXG4gKiBAcHVibGljXG4gKi9cbmV4cG9ydCBjb25zdCB5Q3Vyc29yUGx1Z2luS2V5ID0gbmV3IFBsdWdpbktleSgneWpzLWN1cnNvcicpXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/y-prosemirror/src/plugins/keys.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/y-prosemirror/src/plugins/sync-plugin.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/y-prosemirror/src/plugins/sync-plugin.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProsemirrorBinding: () => (/* binding */ ProsemirrorBinding),\n/* harmony export */   createNodeFromYElement: () => (/* binding */ createNodeFromYElement),\n/* harmony export */   getRelativeSelection: () => (/* binding */ getRelativeSelection),\n/* harmony export */   isVisible: () => (/* binding */ isVisible),\n/* harmony export */   updateYFragment: () => (/* binding */ updateYFragment),\n/* harmony export */   ySyncPlugin: () => (/* binding */ ySyncPlugin)\n/* harmony export */ });\n/* harmony import */ var lib0_mutex__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lib0/mutex */ \"(ssr)/../../node_modules/lib0/mutex.js\");\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/../../node_modules/prosemirror-model/dist/index.js\");\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/../../node_modules/prosemirror-state/dist/index.js\");\n/* harmony import */ var lib0_math__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! lib0/math */ \"(ssr)/../../node_modules/lib0/math.js\");\n/* harmony import */ var lib0_object__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! lib0/object */ \"(ssr)/../../node_modules/lib0/object.js\");\n/* harmony import */ var lib0_set__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lib0/set */ \"(ssr)/../../node_modules/lib0/set.js\");\n/* harmony import */ var lib0_diff__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! lib0/diff */ \"(ssr)/../../node_modules/lib0/diff.js\");\n/* harmony import */ var lib0_error__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! lib0/error */ \"(ssr)/../../node_modules/lib0/error.js\");\n/* harmony import */ var _keys_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./keys.js */ \"(ssr)/../../node_modules/y-prosemirror/src/plugins/keys.js\");\n/* harmony import */ var yjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! yjs */ \"(ssr)/../../node_modules/yjs/dist/yjs.mjs\");\n/* harmony import */ var _lib_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../lib.js */ \"(ssr)/../../node_modules/y-prosemirror/src/lib.js\");\n/* harmony import */ var lib0_random__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lib0/random */ \"(ssr)/../../node_modules/lib0/random.js\");\n/* harmony import */ var lib0_environment__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lib0/environment */ \"(ssr)/../../node_modules/lib0/environment.js\");\n/* harmony import */ var lib0_dom__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! lib0/dom */ \"(ssr)/../../node_modules/lib0/dom.js\");\n/* harmony import */ var lib0_eventloop__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lib0/eventloop */ \"(ssr)/../../node_modules/lib0/eventloop.js\");\n/**\n * @module bindings/prosemirror\n */\n\n\n\n // eslint-disable-line\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * @param {Y.Item} item\n * @param {Y.Snapshot} [snapshot]\n */\nconst isVisible = (item, snapshot) =>\n  snapshot === undefined\n    ? !item.deleted\n    : (snapshot.sv.has(item.id.client) && /** @type {number} */\n      (snapshot.sv.get(item.id.client)) > item.id.clock &&\n      !yjs__WEBPACK_IMPORTED_MODULE_0__.isDeleted(snapshot.ds, item.id))\n\n/**\n * Either a node if type is YXmlElement or an Array of text nodes if YXmlText\n * @typedef {Map<Y.AbstractType<any>, PModel.Node | Array<PModel.Node>>} ProsemirrorMapping\n */\n\n/**\n * @typedef {Object} ColorDef\n * @property {string} ColorDef.light\n * @property {string} ColorDef.dark\n */\n\n/**\n * @typedef {Object} YSyncOpts\n * @property {Array<ColorDef>} [YSyncOpts.colors]\n * @property {Map<string,ColorDef>} [YSyncOpts.colorMapping]\n * @property {Y.PermanentUserData|null} [YSyncOpts.permanentUserData]\n * @property {ProsemirrorMapping} [YSyncOpts.mapping]\n * @property {function} [YSyncOpts.onFirstRender] Fired when the content from Yjs is initially rendered to ProseMirror\n */\n\n/**\n * @type {Array<ColorDef>}\n */\nconst defaultColors = [{ light: '#ecd44433', dark: '#ecd444' }]\n\n/**\n * @param {Map<string,ColorDef>} colorMapping\n * @param {Array<ColorDef>} colors\n * @param {string} user\n * @return {ColorDef}\n */\nconst getUserColor = (colorMapping, colors, user) => {\n  // @todo do not hit the same color twice if possible\n  if (!colorMapping.has(user)) {\n    if (colorMapping.size < colors.length) {\n      const usedColors = lib0_set__WEBPACK_IMPORTED_MODULE_1__.create()\n      colorMapping.forEach((color) => usedColors.add(color))\n      colors = colors.filter((color) => !usedColors.has(color))\n    }\n    colorMapping.set(user, lib0_random__WEBPACK_IMPORTED_MODULE_2__.oneOf(colors))\n  }\n  return /** @type {ColorDef} */ (colorMapping.get(user))\n}\n\n/**\n * This plugin listens to changes in prosemirror view and keeps yXmlState and view in sync.\n *\n * This plugin also keeps references to the type and the shared document so other plugins can access it.\n * @param {Y.XmlFragment} yXmlFragment\n * @param {YSyncOpts} opts\n * @return {any} Returns a prosemirror plugin that binds to this type\n */\nconst ySyncPlugin = (yXmlFragment, {\n  colors = defaultColors,\n  colorMapping = new Map(),\n  permanentUserData = null,\n  onFirstRender = () => {},\n  mapping\n} = {}) => {\n  let initialContentChanged = false\n  const binding = new ProsemirrorBinding(yXmlFragment, mapping)\n  const plugin = new prosemirror_state__WEBPACK_IMPORTED_MODULE_3__.Plugin({\n    props: {\n      editable: (state) => {\n        const syncState = _keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey.getState(state)\n        return syncState.snapshot == null && syncState.prevSnapshot == null\n      }\n    },\n    key: _keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey,\n    state: {\n      /**\n       * @returns {any}\n       */\n      init: (_initargs, _state) => {\n        return {\n          type: yXmlFragment,\n          doc: yXmlFragment.doc,\n          binding,\n          snapshot: null,\n          prevSnapshot: null,\n          isChangeOrigin: false,\n          isUndoRedoOperation: false,\n          addToHistory: true,\n          colors,\n          colorMapping,\n          permanentUserData\n        }\n      },\n      apply: (tr, pluginState) => {\n        const change = tr.getMeta(_keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey)\n        if (change !== undefined) {\n          pluginState = Object.assign({}, pluginState)\n          for (const key in change) {\n            pluginState[key] = change[key]\n          }\n        }\n        pluginState.addToHistory = tr.getMeta('addToHistory') !== false\n        // always set isChangeOrigin. If undefined, this is not change origin.\n        pluginState.isChangeOrigin = change !== undefined &&\n          !!change.isChangeOrigin\n        pluginState.isUndoRedoOperation = change !== undefined && !!change.isChangeOrigin && !!change.isUndoRedoOperation\n        if (binding.prosemirrorView !== null) {\n          if (\n            change !== undefined &&\n            (change.snapshot != null || change.prevSnapshot != null)\n          ) {\n            // snapshot changed, rerender next\n            lib0_eventloop__WEBPACK_IMPORTED_MODULE_5__.timeout(0, () => {\n              if (binding.prosemirrorView == null) {\n                return\n              }\n              if (change.restore == null) {\n                binding._renderSnapshot(\n                  change.snapshot,\n                  change.prevSnapshot,\n                  pluginState\n                )\n              } else {\n                binding._renderSnapshot(\n                  change.snapshot,\n                  change.snapshot,\n                  pluginState\n                )\n                // reset to current prosemirror state\n                delete pluginState.restore\n                delete pluginState.snapshot\n                delete pluginState.prevSnapshot\n                binding.mux(() => {\n                  binding._prosemirrorChanged(\n                    binding.prosemirrorView.state.doc\n                  )\n                })\n              }\n            })\n          }\n        }\n        return pluginState\n      }\n    },\n    view: (view) => {\n      binding.initView(view)\n      if (mapping == null) {\n        // force rerender to update the bindings mapping\n        binding._forceRerender()\n      }\n      onFirstRender()\n      return {\n        update: () => {\n          const pluginState = plugin.getState(view.state)\n          if (\n            pluginState.snapshot == null && pluginState.prevSnapshot == null\n          ) {\n            if (\n              // If the content doesn't change initially, we don't render anything to Yjs\n              // If the content was cleared by a user action, we want to catch the change and\n              // represent it in Yjs\n              initialContentChanged ||\n              view.state.doc.content.findDiffStart(\n                view.state.doc.type.createAndFill().content\n              ) !== null\n            ) {\n              initialContentChanged = true\n              if (\n                pluginState.addToHistory === false &&\n                !pluginState.isChangeOrigin\n              ) {\n                const yUndoPluginState = _keys_js__WEBPACK_IMPORTED_MODULE_4__.yUndoPluginKey.getState(view.state)\n                /**\n                 * @type {Y.UndoManager}\n                 */\n                const um = yUndoPluginState && yUndoPluginState.undoManager\n                if (um) {\n                  um.stopCapturing()\n                }\n              }\n              binding.mux(() => {\n                /** @type {Y.Doc} */ (pluginState.doc).transact((tr) => {\n                  tr.meta.set('addToHistory', pluginState.addToHistory)\n                  binding._prosemirrorChanged(view.state.doc)\n                }, _keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey)\n              })\n            }\n          }\n        },\n        destroy: () => {\n          binding.destroy()\n        }\n      }\n    }\n  })\n  return plugin\n}\n\n/**\n * @param {any} tr\n * @param {any} relSel\n * @param {ProsemirrorBinding} binding\n */\nconst restoreRelativeSelection = (tr, relSel, binding) => {\n  if (relSel !== null && relSel.anchor !== null && relSel.head !== null) {\n    const anchor = (0,_lib_js__WEBPACK_IMPORTED_MODULE_6__.relativePositionToAbsolutePosition)(\n      binding.doc,\n      binding.type,\n      relSel.anchor,\n      binding.mapping\n    )\n    const head = (0,_lib_js__WEBPACK_IMPORTED_MODULE_6__.relativePositionToAbsolutePosition)(\n      binding.doc,\n      binding.type,\n      relSel.head,\n      binding.mapping\n    )\n    if (anchor !== null && head !== null) {\n      tr = tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_3__.TextSelection.create(tr.doc, anchor, head))\n    }\n  }\n}\n\nconst getRelativeSelection = (pmbinding, state) => ({\n  anchor: (0,_lib_js__WEBPACK_IMPORTED_MODULE_6__.absolutePositionToRelativePosition)(\n    state.selection.anchor,\n    pmbinding.type,\n    pmbinding.mapping\n  ),\n  head: (0,_lib_js__WEBPACK_IMPORTED_MODULE_6__.absolutePositionToRelativePosition)(\n    state.selection.head,\n    pmbinding.type,\n    pmbinding.mapping\n  )\n})\n\n/**\n * Binding for prosemirror.\n *\n * @protected\n */\nclass ProsemirrorBinding {\n  /**\n   * @param {Y.XmlFragment} yXmlFragment The bind source\n   * @param {ProsemirrorMapping} mapping\n   */\n  constructor (yXmlFragment, mapping = new Map()) {\n    this.type = yXmlFragment\n    /**\n     * this will be set once the view is created\n     * @type {any}\n     */\n    this.prosemirrorView = null\n    this.mux = (0,lib0_mutex__WEBPACK_IMPORTED_MODULE_7__.createMutex)()\n    this.mapping = mapping\n    this._observeFunction = this._typeChanged.bind(this)\n    /**\n     * @type {Y.Doc}\n     */\n    // @ts-ignore\n    this.doc = yXmlFragment.doc\n    /**\n     * current selection as relative positions in the Yjs model\n     */\n    this.beforeTransactionSelection = null\n    this.beforeAllTransactions = () => {\n      if (this.beforeTransactionSelection === null && this.prosemirrorView != null) {\n        this.beforeTransactionSelection = getRelativeSelection(\n          this,\n          this.prosemirrorView.state\n        )\n      }\n    }\n    this.afterAllTransactions = () => {\n      this.beforeTransactionSelection = null\n    }\n    this._domSelectionInView = null\n  }\n\n  /**\n   * Create a transaction for changing the prosemirror state.\n   *\n   * @returns\n   */\n  get _tr () {\n    return this.prosemirrorView.state.tr.setMeta('addToHistory', false)\n  }\n\n  _isLocalCursorInView () {\n    if (!this.prosemirrorView.hasFocus()) return false\n    if (lib0_environment__WEBPACK_IMPORTED_MODULE_8__.isBrowser && this._domSelectionInView === null) {\n      // Calculate the domSelectionInView and clear by next tick after all events are finished\n      lib0_eventloop__WEBPACK_IMPORTED_MODULE_5__.timeout(0, () => {\n        this._domSelectionInView = null\n      })\n      this._domSelectionInView = this._isDomSelectionInView()\n    }\n    return this._domSelectionInView\n  }\n\n  _isDomSelectionInView () {\n    const selection = this.prosemirrorView._root.getSelection()\n\n    const range = this.prosemirrorView._root.createRange()\n    range.setStart(selection.anchorNode, selection.anchorOffset)\n    range.setEnd(selection.focusNode, selection.focusOffset)\n\n    // This is a workaround for an edgecase where getBoundingClientRect will\n    // return zero values if the selection is collapsed at the start of a newline\n    // see reference here: https://stackoverflow.com/a/59780954\n    const rects = range.getClientRects()\n    if (rects.length === 0) {\n      // probably buggy newline behavior, explicitly select the node contents\n      if (range.startContainer && range.collapsed) {\n        range.selectNodeContents(range.startContainer)\n      }\n    }\n\n    const bounding = range.getBoundingClientRect()\n    const documentElement = lib0_dom__WEBPACK_IMPORTED_MODULE_9__.doc.documentElement\n\n    return bounding.bottom >= 0 && bounding.right >= 0 &&\n      bounding.left <=\n        (window.innerWidth || documentElement.clientWidth || 0) &&\n      bounding.top <= (window.innerHeight || documentElement.clientHeight || 0)\n  }\n\n  /**\n   * @param {Y.Snapshot} snapshot\n   * @param {Y.Snapshot} prevSnapshot\n   */\n  renderSnapshot (snapshot, prevSnapshot) {\n    if (!prevSnapshot) {\n      prevSnapshot = yjs__WEBPACK_IMPORTED_MODULE_0__.createSnapshot(yjs__WEBPACK_IMPORTED_MODULE_0__.createDeleteSet(), new Map())\n    }\n    this.prosemirrorView.dispatch(\n      this._tr.setMeta(_keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey, { snapshot, prevSnapshot })\n    )\n  }\n\n  unrenderSnapshot () {\n    this.mapping.clear()\n    this.mux(() => {\n      const fragmentContent = this.type.toArray().map((t) =>\n        createNodeFromYElement(\n          /** @type {Y.XmlElement} */ (t),\n          this.prosemirrorView.state.schema,\n          this.mapping\n        )\n      ).filter((n) => n !== null)\n      // @ts-ignore\n      const tr = this._tr.replace(\n        0,\n        this.prosemirrorView.state.doc.content.size,\n        new prosemirror_model__WEBPACK_IMPORTED_MODULE_10__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_10__.Fragment.from(fragmentContent), 0, 0)\n      )\n      tr.setMeta(_keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey, { snapshot: null, prevSnapshot: null })\n      this.prosemirrorView.dispatch(tr)\n    })\n  }\n\n  _forceRerender () {\n    this.mapping.clear()\n    this.mux(() => {\n      // If this is a forced rerender, this might neither happen as a pm change nor within a Yjs\n      // transaction. Then the \"before selection\" doesn't exist. In this case, we need to create a\n      // relative position before replacing content. Fixes #126\n      const sel = this.beforeTransactionSelection !== null ? null : this.prosemirrorView.state.selection\n      const fragmentContent = this.type.toArray().map((t) =>\n        createNodeFromYElement(\n          /** @type {Y.XmlElement} */ (t),\n          this.prosemirrorView.state.schema,\n          this.mapping\n        )\n      ).filter((n) => n !== null)\n      // @ts-ignore\n      const tr = this._tr.replace(\n        0,\n        this.prosemirrorView.state.doc.content.size,\n        new prosemirror_model__WEBPACK_IMPORTED_MODULE_10__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_10__.Fragment.from(fragmentContent), 0, 0)\n      )\n      if (sel) {\n        tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_3__.TextSelection.create(tr.doc, sel.anchor, sel.head))\n      }\n      this.prosemirrorView.dispatch(\n        tr.setMeta(_keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey, { isChangeOrigin: true, binding: this })\n      )\n    })\n  }\n\n  /**\n   * @param {Y.Snapshot|Uint8Array} snapshot\n   * @param {Y.Snapshot|Uint8Array} prevSnapshot\n   * @param {Object} pluginState\n   */\n  _renderSnapshot (snapshot, prevSnapshot, pluginState) {\n    /**\n     * The document that contains the full history of this document.\n     * @type {Y.Doc}\n     */\n    let historyDoc = this.doc\n    if (!snapshot) {\n      snapshot = yjs__WEBPACK_IMPORTED_MODULE_0__.snapshot(this.doc)\n    }\n    if (snapshot instanceof Uint8Array || prevSnapshot instanceof Uint8Array) {\n      if (!(snapshot instanceof Uint8Array) || !(prevSnapshot instanceof Uint8Array)) {\n        // expected both snapshots to be v2 updates\n        lib0_error__WEBPACK_IMPORTED_MODULE_11__.unexpectedCase()\n      }\n      historyDoc = new yjs__WEBPACK_IMPORTED_MODULE_0__.Doc({ gc: false })\n      yjs__WEBPACK_IMPORTED_MODULE_0__.applyUpdateV2(historyDoc, prevSnapshot)\n      prevSnapshot = yjs__WEBPACK_IMPORTED_MODULE_0__.snapshot(historyDoc)\n      yjs__WEBPACK_IMPORTED_MODULE_0__.applyUpdateV2(historyDoc, snapshot)\n      snapshot = yjs__WEBPACK_IMPORTED_MODULE_0__.snapshot(historyDoc)\n    }\n    // clear mapping because we are going to rerender\n    this.mapping.clear()\n    this.mux(() => {\n      historyDoc.transact((transaction) => {\n        // before rendering, we are going to sanitize ops and split deleted ops\n        // if they were deleted by seperate users.\n        const pud = pluginState.permanentUserData\n        if (pud) {\n          pud.dss.forEach((ds) => {\n            yjs__WEBPACK_IMPORTED_MODULE_0__.iterateDeletedStructs(transaction, ds, (_item) => {})\n          })\n        }\n        /**\n         * @param {'removed'|'added'} type\n         * @param {Y.ID} id\n         */\n        const computeYChange = (type, id) => {\n          const user = type === 'added'\n            ? pud.getUserByClientId(id.client)\n            : pud.getUserByDeletedId(id)\n          return {\n            user,\n            type,\n            color: getUserColor(\n              pluginState.colorMapping,\n              pluginState.colors,\n              user\n            )\n          }\n        }\n        // Create document fragment and render\n        const fragmentContent = yjs__WEBPACK_IMPORTED_MODULE_0__.typeListToArraySnapshot(\n          this.type,\n          new yjs__WEBPACK_IMPORTED_MODULE_0__.Snapshot(prevSnapshot.ds, snapshot.sv)\n        ).map((t) => {\n          if (\n            !t._item.deleted || isVisible(t._item, snapshot) ||\n            isVisible(t._item, prevSnapshot)\n          ) {\n            return createNodeFromYElement(\n              t,\n              this.prosemirrorView.state.schema,\n              new Map(),\n              snapshot,\n              prevSnapshot,\n              computeYChange\n            )\n          } else {\n            // No need to render elements that are not visible by either snapshot.\n            // If a client adds and deletes content in the same snapshot the element is not visible by either snapshot.\n            return null\n          }\n        }).filter((n) => n !== null)\n        // @ts-ignore\n        const tr = this._tr.replace(\n          0,\n          this.prosemirrorView.state.doc.content.size,\n          new prosemirror_model__WEBPACK_IMPORTED_MODULE_10__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_10__.Fragment.from(fragmentContent), 0, 0)\n        )\n        this.prosemirrorView.dispatch(\n          tr.setMeta(_keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey, { isChangeOrigin: true })\n        )\n      }, _keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey)\n    })\n  }\n\n  /**\n   * @param {Array<Y.YEvent<any>>} events\n   * @param {Y.Transaction} transaction\n   */\n  _typeChanged (events, transaction) {\n    if (this.prosemirrorView == null) return\n    const syncState = _keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey.getState(this.prosemirrorView.state)\n    if (\n      events.length === 0 || syncState.snapshot != null ||\n      syncState.prevSnapshot != null\n    ) {\n      // drop out if snapshot is active\n      this.renderSnapshot(syncState.snapshot, syncState.prevSnapshot)\n      return\n    }\n    this.mux(() => {\n      /**\n       * @param {any} _\n       * @param {Y.AbstractType<any>} type\n       */\n      const delType = (_, type) => this.mapping.delete(type)\n      yjs__WEBPACK_IMPORTED_MODULE_0__.iterateDeletedStructs(\n        transaction,\n        transaction.deleteSet,\n        (struct) => {\n          if (struct.constructor === yjs__WEBPACK_IMPORTED_MODULE_0__.Item) {\n            const type = /** @type {Y.ContentType} */ (/** @type {Y.Item} */ (struct).content).type\n            type && this.mapping.delete(type)\n          }\n        }\n      )\n      transaction.changed.forEach(delType)\n      transaction.changedParentTypes.forEach(delType)\n      const fragmentContent = this.type.toArray().map((t) =>\n        createNodeIfNotExists(\n          /** @type {Y.XmlElement | Y.XmlHook} */ (t),\n          this.prosemirrorView.state.schema,\n          this.mapping\n        )\n      ).filter((n) => n !== null)\n      // @ts-ignore\n      let tr = this._tr.replace(\n        0,\n        this.prosemirrorView.state.doc.content.size,\n        new prosemirror_model__WEBPACK_IMPORTED_MODULE_10__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_10__.Fragment.from(fragmentContent), 0, 0)\n      )\n      restoreRelativeSelection(tr, this.beforeTransactionSelection, this)\n      tr = tr.setMeta(_keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey, { isChangeOrigin: true, isUndoRedoOperation: transaction.origin instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.UndoManager })\n      if (\n        this.beforeTransactionSelection !== null && this._isLocalCursorInView()\n      ) {\n        tr.scrollIntoView()\n      }\n      this.prosemirrorView.dispatch(tr)\n    })\n  }\n\n  _prosemirrorChanged (doc) {\n    this.doc.transact(() => {\n      updateYFragment(this.doc, this.type, doc, this.mapping)\n      this.beforeTransactionSelection = getRelativeSelection(\n        this,\n        this.prosemirrorView.state\n      )\n    }, _keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey)\n  }\n\n  /**\n   * View is ready to listen to changes. Register observers.\n   * @param {any} prosemirrorView\n   */\n  initView (prosemirrorView) {\n    if (this.prosemirrorView != null) this.destroy()\n    this.prosemirrorView = prosemirrorView\n    this.doc.on('beforeAllTransactions', this.beforeAllTransactions)\n    this.doc.on('afterAllTransactions', this.afterAllTransactions)\n    this.type.observeDeep(this._observeFunction)\n  }\n\n  destroy () {\n    if (this.prosemirrorView == null) return\n    this.prosemirrorView = null\n    this.type.unobserveDeep(this._observeFunction)\n    this.doc.off('beforeAllTransactions', this.beforeAllTransactions)\n    this.doc.off('afterAllTransactions', this.afterAllTransactions)\n  }\n}\n\n/**\n * @private\n * @param {Y.XmlElement | Y.XmlHook} el\n * @param {PModel.Schema} schema\n * @param {ProsemirrorMapping} mapping\n * @param {Y.Snapshot} [snapshot]\n * @param {Y.Snapshot} [prevSnapshot]\n * @param {function('removed' | 'added', Y.ID):any} [computeYChange]\n * @return {PModel.Node | null}\n */\nconst createNodeIfNotExists = (\n  el,\n  schema,\n  mapping,\n  snapshot,\n  prevSnapshot,\n  computeYChange\n) => {\n  const node = /** @type {PModel.Node} */ (mapping.get(el))\n  if (node === undefined) {\n    if (el instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.XmlElement) {\n      return createNodeFromYElement(\n        el,\n        schema,\n        mapping,\n        snapshot,\n        prevSnapshot,\n        computeYChange\n      )\n    } else {\n      throw lib0_error__WEBPACK_IMPORTED_MODULE_11__.methodUnimplemented() // we are currently not handling hooks\n    }\n  }\n  return node\n}\n\n/**\n * @private\n * @param {Y.XmlElement} el\n * @param {any} schema\n * @param {ProsemirrorMapping} mapping\n * @param {Y.Snapshot} [snapshot]\n * @param {Y.Snapshot} [prevSnapshot]\n * @param {function('removed' | 'added', Y.ID):any} [computeYChange]\n * @return {PModel.Node | null} Returns node if node could be created. Otherwise it deletes the yjs type and returns null\n */\nconst createNodeFromYElement = (\n  el,\n  schema,\n  mapping,\n  snapshot,\n  prevSnapshot,\n  computeYChange\n) => {\n  const children = []\n  const createChildren = (type) => {\n    if (type.constructor === yjs__WEBPACK_IMPORTED_MODULE_0__.XmlElement) {\n      const n = createNodeIfNotExists(\n        type,\n        schema,\n        mapping,\n        snapshot,\n        prevSnapshot,\n        computeYChange\n      )\n      if (n !== null) {\n        children.push(n)\n      }\n    } else {\n      // If the next ytext exists and was created by us, move the content to the current ytext.\n      // This is a fix for #160 -- duplication of characters when two Y.Text exist next to each\n      // other.\n      const nextytext = type._item.right?.content.type\n      if (nextytext instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.Text && !nextytext._item.deleted && nextytext._item.id.client === nextytext.doc.clientID) {\n        type.applyDelta([\n          { retain: type.length },\n          ...nextytext.toDelta()\n        ])\n        nextytext.doc.transact(tr => {\n          nextytext._item.delete(tr)\n        })\n      }\n      // now create the prosemirror text nodes\n      const ns = createTextNodesFromYText(\n        type,\n        schema,\n        mapping,\n        snapshot,\n        prevSnapshot,\n        computeYChange\n      )\n      if (ns !== null) {\n        ns.forEach((textchild) => {\n          if (textchild !== null) {\n            children.push(textchild)\n          }\n        })\n      }\n    }\n  }\n  if (snapshot === undefined || prevSnapshot === undefined) {\n    el.toArray().forEach(createChildren)\n  } else {\n    yjs__WEBPACK_IMPORTED_MODULE_0__.typeListToArraySnapshot(el, new yjs__WEBPACK_IMPORTED_MODULE_0__.Snapshot(prevSnapshot.ds, snapshot.sv))\n      .forEach(createChildren)\n  }\n  try {\n    const attrs = el.getAttributes(snapshot)\n    if (snapshot !== undefined) {\n      if (!isVisible(/** @type {Y.Item} */ (el._item), snapshot)) {\n        attrs.ychange = computeYChange\n          ? computeYChange('removed', /** @type {Y.Item} */ (el._item).id)\n          : { type: 'removed' }\n      } else if (!isVisible(/** @type {Y.Item} */ (el._item), prevSnapshot)) {\n        attrs.ychange = computeYChange\n          ? computeYChange('added', /** @type {Y.Item} */ (el._item).id)\n          : { type: 'added' }\n      }\n    }\n    const node = schema.node(el.nodeName, attrs, children)\n    mapping.set(el, node)\n    return node\n  } catch (e) {\n    // an error occured while creating the node. This is probably a result of a concurrent action.\n    /** @type {Y.Doc} */ (el.doc).transact((transaction) => {\n      /** @type {Y.Item} */ (el._item).delete(transaction)\n    }, _keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey)\n    mapping.delete(el)\n    return null\n  }\n}\n\n/**\n * @private\n * @param {Y.XmlText} text\n * @param {any} schema\n * @param {ProsemirrorMapping} _mapping\n * @param {Y.Snapshot} [snapshot]\n * @param {Y.Snapshot} [prevSnapshot]\n * @param {function('removed' | 'added', Y.ID):any} [computeYChange]\n * @return {Array<PModel.Node>|null}\n */\nconst createTextNodesFromYText = (\n  text,\n  schema,\n  _mapping,\n  snapshot,\n  prevSnapshot,\n  computeYChange\n) => {\n  const nodes = []\n  const deltas = text.toDelta(snapshot, prevSnapshot, computeYChange)\n  try {\n    for (let i = 0; i < deltas.length; i++) {\n      const delta = deltas[i]\n      const marks = []\n      for (const markName in delta.attributes) {\n        marks.push(schema.mark(markName, delta.attributes[markName]))\n      }\n      nodes.push(schema.text(delta.insert, marks))\n    }\n  } catch (e) {\n    // an error occured while creating the node. This is probably a result of a concurrent action.\n    /** @type {Y.Doc} */ (text.doc).transact((transaction) => {\n      /** @type {Y.Item} */ (text._item).delete(transaction)\n    }, _keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey)\n    return null\n  }\n  // @ts-ignore\n  return nodes\n}\n\n/**\n * @private\n * @param {Array<any>} nodes prosemirror node\n * @param {ProsemirrorMapping} mapping\n * @return {Y.XmlText}\n */\nconst createTypeFromTextNodes = (nodes, mapping) => {\n  const type = new yjs__WEBPACK_IMPORTED_MODULE_0__.XmlText()\n  const delta = nodes.map((node) => ({\n    // @ts-ignore\n    insert: node.text,\n    attributes: marksToAttributes(node.marks)\n  }))\n  type.applyDelta(delta)\n  mapping.set(type, nodes)\n  return type\n}\n\n/**\n * @private\n * @param {any} node prosemirror node\n * @param {ProsemirrorMapping} mapping\n * @return {Y.XmlElement}\n */\nconst createTypeFromElementNode = (node, mapping) => {\n  const type = new yjs__WEBPACK_IMPORTED_MODULE_0__.XmlElement(node.type.name)\n  for (const key in node.attrs) {\n    const val = node.attrs[key]\n    if (val !== null && key !== 'ychange') {\n      type.setAttribute(key, val)\n    }\n  }\n  type.insert(\n    0,\n    normalizePNodeContent(node).map((n) =>\n      createTypeFromTextOrElementNode(n, mapping)\n    )\n  )\n  mapping.set(type, node)\n  return type\n}\n\n/**\n * @private\n * @param {PModel.Node|Array<PModel.Node>} node prosemirror text node\n * @param {ProsemirrorMapping} mapping\n * @return {Y.XmlElement|Y.XmlText}\n */\nconst createTypeFromTextOrElementNode = (node, mapping) =>\n  node instanceof Array\n    ? createTypeFromTextNodes(node, mapping)\n    : createTypeFromElementNode(node, mapping)\n\nconst isObject = (val) => typeof val === 'object' && val !== null\n\nconst equalAttrs = (pattrs, yattrs) => {\n  const keys = Object.keys(pattrs).filter((key) => pattrs[key] !== null)\n  let eq =\n    keys.length ===\n      Object.keys(yattrs).filter((key) => yattrs[key] !== null).length\n  for (let i = 0; i < keys.length && eq; i++) {\n    const key = keys[i]\n    const l = pattrs[key]\n    const r = yattrs[key]\n    eq = key === 'ychange' || l === r ||\n      (isObject(l) && isObject(r) && equalAttrs(l, r))\n  }\n  return eq\n}\n\n/**\n * @typedef {Array<Array<PModel.Node>|PModel.Node>} NormalizedPNodeContent\n */\n\n/**\n * @param {any} pnode\n * @return {NormalizedPNodeContent}\n */\nconst normalizePNodeContent = (pnode) => {\n  const c = pnode.content.content\n  const res = []\n  for (let i = 0; i < c.length; i++) {\n    const n = c[i]\n    if (n.isText) {\n      const textNodes = []\n      for (let tnode = c[i]; i < c.length && tnode.isText; tnode = c[++i]) {\n        textNodes.push(tnode)\n      }\n      i--\n      res.push(textNodes)\n    } else {\n      res.push(n)\n    }\n  }\n  return res\n}\n\n/**\n * @param {Y.XmlText} ytext\n * @param {Array<any>} ptexts\n */\nconst equalYTextPText = (ytext, ptexts) => {\n  const delta = ytext.toDelta()\n  return delta.length === ptexts.length &&\n    delta.every((d, i) =>\n      d.insert === /** @type {any} */ (ptexts[i]).text &&\n      lib0_object__WEBPACK_IMPORTED_MODULE_12__.keys(d.attributes || {}).length === ptexts[i].marks.length &&\n      ptexts[i].marks.every((mark) =>\n        equalAttrs(d.attributes[mark.type.name] || {}, mark.attrs)\n      )\n    )\n}\n\n/**\n * @param {Y.XmlElement|Y.XmlText|Y.XmlHook} ytype\n * @param {any|Array<any>} pnode\n */\nconst equalYTypePNode = (ytype, pnode) => {\n  if (\n    ytype instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.XmlElement && !(pnode instanceof Array) &&\n    matchNodeName(ytype, pnode)\n  ) {\n    const normalizedContent = normalizePNodeContent(pnode)\n    return ytype._length === normalizedContent.length &&\n      equalAttrs(ytype.getAttributes(), pnode.attrs) &&\n      ytype.toArray().every((ychild, i) =>\n        equalYTypePNode(ychild, normalizedContent[i])\n      )\n  }\n  return ytype instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.XmlText && pnode instanceof Array &&\n    equalYTextPText(ytype, pnode)\n}\n\n/**\n * @param {PModel.Node | Array<PModel.Node> | undefined} mapped\n * @param {PModel.Node | Array<PModel.Node>} pcontent\n */\nconst mappedIdentity = (mapped, pcontent) =>\n  mapped === pcontent ||\n  (mapped instanceof Array && pcontent instanceof Array &&\n    mapped.length === pcontent.length && mapped.every((a, i) =>\n    pcontent[i] === a\n  ))\n\n/**\n * @param {Y.XmlElement} ytype\n * @param {PModel.Node} pnode\n * @param {ProsemirrorMapping} mapping\n * @return {{ foundMappedChild: boolean, equalityFactor: number }}\n */\nconst computeChildEqualityFactor = (ytype, pnode, mapping) => {\n  const yChildren = ytype.toArray()\n  const pChildren = normalizePNodeContent(pnode)\n  const pChildCnt = pChildren.length\n  const yChildCnt = yChildren.length\n  const minCnt = lib0_math__WEBPACK_IMPORTED_MODULE_13__.min(yChildCnt, pChildCnt)\n  let left = 0\n  let right = 0\n  let foundMappedChild = false\n  for (; left < minCnt; left++) {\n    const leftY = yChildren[left]\n    const leftP = pChildren[left]\n    if (mappedIdentity(mapping.get(leftY), leftP)) {\n      foundMappedChild = true // definite (good) match!\n    } else if (!equalYTypePNode(leftY, leftP)) {\n      break\n    }\n  }\n  for (; left + right < minCnt; right++) {\n    const rightY = yChildren[yChildCnt - right - 1]\n    const rightP = pChildren[pChildCnt - right - 1]\n    if (mappedIdentity(mapping.get(rightY), rightP)) {\n      foundMappedChild = true\n    } else if (!equalYTypePNode(rightY, rightP)) {\n      break\n    }\n  }\n  return {\n    equalityFactor: left + right,\n    foundMappedChild\n  }\n}\n\nconst ytextTrans = (ytext) => {\n  let str = ''\n  /**\n   * @type {Y.Item|null}\n   */\n  let n = ytext._start\n  const nAttrs = {}\n  while (n !== null) {\n    if (!n.deleted) {\n      if (n.countable && n.content instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.ContentString) {\n        str += n.content.str\n      } else if (n.content instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.ContentFormat) {\n        nAttrs[n.content.key] = null\n      }\n    }\n    n = n.right\n  }\n  return {\n    str,\n    nAttrs\n  }\n}\n\n/**\n * @todo test this more\n *\n * @param {Y.Text} ytext\n * @param {Array<any>} ptexts\n * @param {ProsemirrorMapping} mapping\n */\nconst updateYText = (ytext, ptexts, mapping) => {\n  mapping.set(ytext, ptexts)\n  const { nAttrs, str } = ytextTrans(ytext)\n  const content = ptexts.map((p) => ({\n    insert: /** @type {any} */ (p).text,\n    attributes: Object.assign({}, nAttrs, marksToAttributes(p.marks))\n  }))\n  const { insert, remove, index } = (0,lib0_diff__WEBPACK_IMPORTED_MODULE_14__.simpleDiff)(\n    str,\n    content.map((c) => c.insert).join('')\n  )\n  ytext.delete(index, remove)\n  ytext.insert(index, insert)\n  ytext.applyDelta(\n    content.map((c) => ({ retain: c.insert.length, attributes: c.attributes }))\n  )\n}\n\nconst marksToAttributes = (marks) => {\n  const pattrs = {}\n  marks.forEach((mark) => {\n    if (mark.type.name !== 'ychange') {\n      pattrs[mark.type.name] = mark.attrs\n    }\n  })\n  return pattrs\n}\n\n/**\n * Update a yDom node by syncing the current content of the prosemirror node.\n *\n * This is a y-prosemirror internal feature that you can use at your own risk.\n *\n * @private\n * @unstable\n *\n * @param {{transact: Function}} y\n * @param {Y.XmlFragment} yDomFragment\n * @param {any} pNode\n * @param {ProsemirrorMapping} mapping\n */\nconst updateYFragment = (y, yDomFragment, pNode, mapping) => {\n  if (\n    yDomFragment instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.XmlElement &&\n    yDomFragment.nodeName !== pNode.type.name\n  ) {\n    throw new Error('node name mismatch!')\n  }\n  mapping.set(yDomFragment, pNode)\n  // update attributes\n  if (yDomFragment instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.XmlElement) {\n    const yDomAttrs = yDomFragment.getAttributes()\n    const pAttrs = pNode.attrs\n    for (const key in pAttrs) {\n      if (pAttrs[key] !== null) {\n        if (yDomAttrs[key] !== pAttrs[key] && key !== 'ychange') {\n          yDomFragment.setAttribute(key, pAttrs[key])\n        }\n      } else {\n        yDomFragment.removeAttribute(key)\n      }\n    }\n    // remove all keys that are no longer in pAttrs\n    for (const key in yDomAttrs) {\n      if (pAttrs[key] === undefined) {\n        yDomFragment.removeAttribute(key)\n      }\n    }\n  }\n  // update children\n  const pChildren = normalizePNodeContent(pNode)\n  const pChildCnt = pChildren.length\n  const yChildren = yDomFragment.toArray()\n  const yChildCnt = yChildren.length\n  const minCnt = lib0_math__WEBPACK_IMPORTED_MODULE_13__.min(pChildCnt, yChildCnt)\n  let left = 0\n  let right = 0\n  // find number of matching elements from left\n  for (; left < minCnt; left++) {\n    const leftY = yChildren[left]\n    const leftP = pChildren[left]\n    if (!mappedIdentity(mapping.get(leftY), leftP)) {\n      if (equalYTypePNode(leftY, leftP)) {\n        // update mapping\n        mapping.set(leftY, leftP)\n      } else {\n        break\n      }\n    }\n  }\n  // find number of matching elements from right\n  for (; right + left + 1 < minCnt; right++) {\n    const rightY = yChildren[yChildCnt - right - 1]\n    const rightP = pChildren[pChildCnt - right - 1]\n    if (!mappedIdentity(mapping.get(rightY), rightP)) {\n      if (equalYTypePNode(rightY, rightP)) {\n        // update mapping\n        mapping.set(rightY, rightP)\n      } else {\n        break\n      }\n    }\n  }\n  y.transact(() => {\n    // try to compare and update\n    while (yChildCnt - left - right > 0 && pChildCnt - left - right > 0) {\n      const leftY = yChildren[left]\n      const leftP = pChildren[left]\n      const rightY = yChildren[yChildCnt - right - 1]\n      const rightP = pChildren[pChildCnt - right - 1]\n      if (leftY instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.XmlText && leftP instanceof Array) {\n        if (!equalYTextPText(leftY, leftP)) {\n          updateYText(leftY, leftP, mapping)\n        }\n        left += 1\n      } else {\n        let updateLeft = leftY instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.XmlElement &&\n          matchNodeName(leftY, leftP)\n        let updateRight = rightY instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.XmlElement &&\n          matchNodeName(rightY, rightP)\n        if (updateLeft && updateRight) {\n          // decide which which element to update\n          const equalityLeft = computeChildEqualityFactor(\n            /** @type {Y.XmlElement} */ (leftY),\n            /** @type {PModel.Node} */ (leftP),\n            mapping\n          )\n          const equalityRight = computeChildEqualityFactor(\n            /** @type {Y.XmlElement} */ (rightY),\n            /** @type {PModel.Node} */ (rightP),\n            mapping\n          )\n          if (\n            equalityLeft.foundMappedChild && !equalityRight.foundMappedChild\n          ) {\n            updateRight = false\n          } else if (\n            !equalityLeft.foundMappedChild && equalityRight.foundMappedChild\n          ) {\n            updateLeft = false\n          } else if (\n            equalityLeft.equalityFactor < equalityRight.equalityFactor\n          ) {\n            updateLeft = false\n          } else {\n            updateRight = false\n          }\n        }\n        if (updateLeft) {\n          updateYFragment(\n            y,\n            /** @type {Y.XmlFragment} */ (leftY),\n            /** @type {PModel.Node} */ (leftP),\n            mapping\n          )\n          left += 1\n        } else if (updateRight) {\n          updateYFragment(\n            y,\n            /** @type {Y.XmlFragment} */ (rightY),\n            /** @type {PModel.Node} */ (rightP),\n            mapping\n          )\n          right += 1\n        } else {\n          mapping.delete(yDomFragment.get(left))\n          yDomFragment.delete(left, 1)\n          yDomFragment.insert(left, [\n            createTypeFromTextOrElementNode(leftP, mapping)\n          ])\n          left += 1\n        }\n      }\n    }\n    const yDelLen = yChildCnt - left - right\n    if (\n      yChildCnt === 1 && pChildCnt === 0 && yChildren[0] instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.XmlText\n    ) {\n      mapping.delete(yChildren[0])\n      // Edge case handling https://github.com/yjs/y-prosemirror/issues/108\n      // Only delete the content of the Y.Text to retain remote changes on the same Y.Text object\n      yChildren[0].delete(0, yChildren[0].length)\n    } else if (yDelLen > 0) {\n      yDomFragment.slice(left, left + yDelLen).forEach(type => mapping.delete(type))\n      yDomFragment.delete(left, yDelLen)\n    }\n    if (left + right < pChildCnt) {\n      const ins = []\n      for (let i = left; i < pChildCnt - right; i++) {\n        ins.push(createTypeFromTextOrElementNode(pChildren[i], mapping))\n      }\n      yDomFragment.insert(left, ins)\n    }\n  }, _keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey)\n}\n\n/**\n * @function\n * @param {Y.XmlElement} yElement\n * @param {any} pNode Prosemirror Node\n */\nconst matchNodeName = (yElement, pNode) =>\n  !(pNode instanceof Array) && yElement.nodeName === pNode.type.name\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/y-prosemirror/src/plugins/sync-plugin.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/y-prosemirror/src/plugins/undo-plugin.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/y-prosemirror/src/plugins/undo-plugin.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultDeleteFilter: () => (/* binding */ defaultDeleteFilter),\n/* harmony export */   defaultProtectedNodes: () => (/* binding */ defaultProtectedNodes),\n/* harmony export */   redo: () => (/* binding */ redo),\n/* harmony export */   undo: () => (/* binding */ undo),\n/* harmony export */   yUndoPlugin: () => (/* binding */ yUndoPlugin)\n/* harmony export */ });\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/../../node_modules/prosemirror-state/dist/index.js\");\n/* harmony import */ var _sync_plugin_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./sync-plugin.js */ \"(ssr)/../../node_modules/y-prosemirror/src/plugins/sync-plugin.js\");\n/* harmony import */ var yjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! yjs */ \"(ssr)/../../node_modules/yjs/dist/yjs.mjs\");\n/* harmony import */ var _keys_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./keys.js */ \"(ssr)/../../node_modules/y-prosemirror/src/plugins/keys.js\");\n // eslint-disable-line\n\n\n\n\n\nconst undo = state => {\n  const undoManager = _keys_js__WEBPACK_IMPORTED_MODULE_0__.yUndoPluginKey.getState(state).undoManager\n  if (undoManager != null) {\n    undoManager.undo()\n    return true\n  }\n}\n\nconst redo = state => {\n  const undoManager = _keys_js__WEBPACK_IMPORTED_MODULE_0__.yUndoPluginKey.getState(state).undoManager\n  if (undoManager != null) {\n    undoManager.redo()\n    return true\n  }\n}\n\nconst defaultProtectedNodes = new Set(['paragraph'])\n\nconst defaultDeleteFilter = (item, protectedNodes) => !(item instanceof yjs__WEBPACK_IMPORTED_MODULE_1__.Item) ||\n!(item.content instanceof yjs__WEBPACK_IMPORTED_MODULE_1__.ContentType) ||\n!(item.content.type instanceof yjs__WEBPACK_IMPORTED_MODULE_1__.Text ||\n  (item.content.type instanceof yjs__WEBPACK_IMPORTED_MODULE_1__.XmlElement && protectedNodes.has(item.content.type.nodeName))) ||\nitem.content.type._length === 0\n\nconst yUndoPlugin = ({ protectedNodes = defaultProtectedNodes, trackedOrigins = [], undoManager = null } = {}) => new prosemirror_state__WEBPACK_IMPORTED_MODULE_2__.Plugin({\n  key: _keys_js__WEBPACK_IMPORTED_MODULE_0__.yUndoPluginKey,\n  state: {\n    init: (initargs, state) => {\n      // TODO: check if plugin order matches and fix\n      const ystate = _keys_js__WEBPACK_IMPORTED_MODULE_0__.ySyncPluginKey.getState(state)\n      const _undoManager = undoManager || new yjs__WEBPACK_IMPORTED_MODULE_1__.UndoManager(ystate.type, {\n        trackedOrigins: new Set([_keys_js__WEBPACK_IMPORTED_MODULE_0__.ySyncPluginKey].concat(trackedOrigins)),\n        deleteFilter: (item) => defaultDeleteFilter(item, protectedNodes),\n        captureTransaction: tr => tr.meta.get('addToHistory') !== false\n      })\n      return {\n        undoManager: _undoManager,\n        prevSel: null,\n        hasUndoOps: _undoManager.undoStack.length > 0,\n        hasRedoOps: _undoManager.redoStack.length > 0\n      }\n    },\n    /**\n     * @returns {any}\n     */\n    apply: (tr, val, oldState, state) => {\n      const binding = _keys_js__WEBPACK_IMPORTED_MODULE_0__.ySyncPluginKey.getState(state).binding\n      const undoManager = val.undoManager\n      const hasUndoOps = undoManager.undoStack.length > 0\n      const hasRedoOps = undoManager.redoStack.length > 0\n      if (binding) {\n        return {\n          undoManager,\n          prevSel: (0,_sync_plugin_js__WEBPACK_IMPORTED_MODULE_3__.getRelativeSelection)(binding, oldState),\n          hasUndoOps,\n          hasRedoOps\n        }\n      } else {\n        if (hasUndoOps !== val.hasUndoOps || hasRedoOps !== val.hasRedoOps) {\n          return Object.assign({}, val, {\n            hasUndoOps: undoManager.undoStack.length > 0,\n            hasRedoOps: undoManager.redoStack.length > 0\n          })\n        } else { // nothing changed\n          return val\n        }\n      }\n    }\n  },\n  view: view => {\n    const ystate = _keys_js__WEBPACK_IMPORTED_MODULE_0__.ySyncPluginKey.getState(view.state)\n    const undoManager = _keys_js__WEBPACK_IMPORTED_MODULE_0__.yUndoPluginKey.getState(view.state).undoManager\n    undoManager.on('stack-item-added', ({ stackItem }) => {\n      const binding = ystate.binding\n      if (binding) {\n        stackItem.meta.set(binding, _keys_js__WEBPACK_IMPORTED_MODULE_0__.yUndoPluginKey.getState(view.state).prevSel)\n      }\n    })\n    undoManager.on('stack-item-popped', ({ stackItem }) => {\n      const binding = ystate.binding\n      if (binding) {\n        binding.beforeTransactionSelection = stackItem.meta.get(binding) || binding.beforeTransactionSelection\n      }\n    })\n    return {\n      destroy: () => {\n        undoManager.destroy()\n      }\n    }\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/y-prosemirror/src/plugins/undo-plugin.js\n");

/***/ })

};
;