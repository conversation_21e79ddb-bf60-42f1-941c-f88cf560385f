"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_rehype-remark_index_js"],{

/***/ "(app-pages-browser)/../../node_modules/hast-util-has-property/lib/index.js":
/*!**************************************************************!*\
  !*** ../../node_modules/hast-util-has-property/lib/index.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasProperty: function() { return /* binding */ hasProperty; }\n/* harmony export */ });\n/**\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Content} Content\n */\n\n/**\n * @typedef {Root | Content} Node\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * Check if `node`is an element and has a `field` property.\n *\n * @param {unknown} node\n *   Thing to check (typically `Element`).\n * @param {unknown} field\n *   Field name to check (typically `string`).\n * @returns {boolean}\n *   Whether `node` is an element that has a `field` property.\n */\nfunction hasProperty(node, field) {\n  const value =\n    typeof field === 'string' &&\n    isNode(node) &&\n    node.type === 'element' &&\n    node.properties &&\n    own.call(node.properties, field) &&\n    node.properties[field]\n\n  return value !== null && value !== undefined && value !== false\n}\n\n/**\n * @param {unknown} value\n * @returns {value is Node}\n */\nfunction isNode(value) {\n  return Boolean(value && typeof value === 'object' && 'type' in value)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLWhhcy1wcm9wZXJ0eS9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSxxQkFBcUI7QUFDbEMsYUFBYSx3QkFBd0I7QUFDckM7O0FBRUE7QUFDQSxhQUFhLGdCQUFnQjtBQUM3Qjs7QUFFQSxjQUFjOztBQUVkO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC1oYXMtcHJvcGVydHkvbGliL2luZGV4LmpzPzk4NTEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuUm9vdH0gUm9vdFxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkNvbnRlbnR9IENvbnRlbnRcbiAqL1xuXG4vKipcbiAqIEB0eXBlZGVmIHtSb290IHwgQ29udGVudH0gTm9kZVxuICovXG5cbmNvbnN0IG93biA9IHt9Lmhhc093blByb3BlcnR5XG5cbi8qKlxuICogQ2hlY2sgaWYgYG5vZGVgaXMgYW4gZWxlbWVudCBhbmQgaGFzIGEgYGZpZWxkYCBwcm9wZXJ0eS5cbiAqXG4gKiBAcGFyYW0ge3Vua25vd259IG5vZGVcbiAqICAgVGhpbmcgdG8gY2hlY2sgKHR5cGljYWxseSBgRWxlbWVudGApLlxuICogQHBhcmFtIHt1bmtub3dufSBmaWVsZFxuICogICBGaWVsZCBuYW1lIHRvIGNoZWNrICh0eXBpY2FsbHkgYHN0cmluZ2ApLlxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKiAgIFdoZXRoZXIgYG5vZGVgIGlzIGFuIGVsZW1lbnQgdGhhdCBoYXMgYSBgZmllbGRgIHByb3BlcnR5LlxuICovXG5leHBvcnQgZnVuY3Rpb24gaGFzUHJvcGVydHkobm9kZSwgZmllbGQpIHtcbiAgY29uc3QgdmFsdWUgPVxuICAgIHR5cGVvZiBmaWVsZCA9PT0gJ3N0cmluZycgJiZcbiAgICBpc05vZGUobm9kZSkgJiZcbiAgICBub2RlLnR5cGUgPT09ICdlbGVtZW50JyAmJlxuICAgIG5vZGUucHJvcGVydGllcyAmJlxuICAgIG93bi5jYWxsKG5vZGUucHJvcGVydGllcywgZmllbGQpICYmXG4gICAgbm9kZS5wcm9wZXJ0aWVzW2ZpZWxkXVxuXG4gIHJldHVybiB2YWx1ZSAhPT0gbnVsbCAmJiB2YWx1ZSAhPT0gdW5kZWZpbmVkICYmIHZhbHVlICE9PSBmYWxzZVxufVxuXG4vKipcbiAqIEBwYXJhbSB7dW5rbm93bn0gdmFsdWVcbiAqIEByZXR1cm5zIHt2YWx1ZSBpcyBOb2RlfVxuICovXG5mdW5jdGlvbiBpc05vZGUodmFsdWUpIHtcbiAgcmV0dXJuIEJvb2xlYW4odmFsdWUgJiYgdHlwZW9mIHZhbHVlID09PSAnb2JqZWN0JyAmJiAndHlwZScgaW4gdmFsdWUpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-has-property/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-is-body-ok-link/index.js":
/*!*************************************************************!*\
  !*** ../../node_modules/hast-util-is-body-ok-link/index.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBodyOkLink: function() { return /* binding */ isBodyOkLink; }\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(app-pages-browser)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var hast_util_has_property__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-has-property */ \"(app-pages-browser)/../../node_modules/hast-util-has-property/lib/index.js\");\n/**\n * @fileoverview\n *   Check if a `link` element is “Body OK”.\n * @longdescription\n *   ## Use\n *\n *   ```js\n *   import {h} from 'hastscript'\n *   import {isBodyOkLink} from 'hast-util-is-body-ok-link'\n *\n *   isBodyOkLink(h('link', {itemProp: 'foo'})) //=> true\n *   isBodyOkLink(h('link', {rel: ['stylesheet'], href: 'index.css'})) //=> true\n *   isBodyOkLink(h('link', {rel: ['author'], href: 'index.css'})) //=> false\n *   ```\n *\n *   ## API\n *\n *   ### `isBodyOkLink(node)`\n *\n *   * Return `true` for `link` elements with an `itemProp`\n *   * Return `true` for `link` elements with a `rel` list where one or more\n *     entries are `pingback`, `prefetch`, or `stylesheet`.\n */\n\n\n\n\nconst list = new Set(['pingback', 'prefetch', 'stylesheet'])\n\n/**\n * @typedef {import('hast').Root} Root\n * @typedef {Root|Root['children'][number]} Node\n */\n\n/**\n * Check if a `link` element is “Body OK”.\n *\n * @param {Node} node\n * @returns {boolean}\n */\nfunction isBodyOkLink(node) {\n  if (!(0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.isElement)(node, 'link')) {\n    return false\n  }\n\n  if ((0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_1__.hasProperty)(node, 'itemProp')) {\n    return true\n  }\n\n  const props = node.properties || {}\n  const rel = props.rel || []\n  let index = -1\n\n  if (!Array.isArray(rel) || rel.length === 0) {\n    return false\n  }\n\n  while (++index < rel.length) {\n    if (!list.has(String(rel[index]))) {\n      return false\n    }\n  }\n\n  return true\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-is-body-ok-link/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-is-element/index.js":
/*!********************************************************!*\
  !*** ../../node_modules/hast-util-is-element/index.js ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertElement: function() { return /* binding */ convertElement; },\n/* harmony export */   isElement: function() { return /* binding */ isElement; }\n/* harmony export */ });\n/**\n * @typedef {import('unist').Parent} Parent\n * @typedef {import('hast').Element} Element\n */\n\n/**\n * @typedef {null | undefined | string | TestFunctionAnything | Array<string | TestFunctionAnything>} Test\n *   Check for an arbitrary element, unaware of TypeScript inferral.\n *\n * @callback TestFunctionAnything\n *   Check if an element passes a test, unaware of TypeScript inferral.\n * @param {Element} element\n *   An element.\n * @param {number | null | undefined} [index]\n *   The element’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The element’s parent.\n * @returns {boolean | void}\n *   Whether this element passes the test.\n */\n\n/**\n * @template {Element} T\n *   Element type.\n * @typedef {T['tagName'] | TestFunctionPredicate<T> | Array<T['tagName'] | TestFunctionPredicate<T>>} PredicateTest\n *   Check for an element that can be inferred by TypeScript.\n */\n\n/**\n * Check if an element passes a certain node test.\n *\n * @template {Element} T\n *   Element type.\n * @callback TestFunctionPredicate\n *   Complex test function for an element that can be inferred by TypeScript.\n * @param {Element} element\n *   An element.\n * @param {number | null | undefined} [index]\n *   The element’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The element’s parent.\n * @returns {element is T}\n *   Whether this element passes the test.\n */\n\n/**\n * @callback AssertAnything\n *   Check that an arbitrary value is an element, unaware of TypeScript inferral.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is an element and passes a test.\n */\n\n/**\n * Check if a node is an element and passes a certain node test\n *\n * @template {Element} T\n *   Element type.\n * @callback AssertPredicate\n *   Check that an arbitrary value is a specific element, aware of TypeScript.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {node is T}\n *   Whether this is an element and passes a test.\n */\n\n/**\n * Check if `node` is an `Element` and whether it passes the given test.\n *\n * @param node\n *   Thing to check, typically `Node`.\n * @param test\n *   A check for a specific element.\n * @param index\n *   The node’s position in its parent.\n * @param parent\n *   The node’s parent.\n * @returns\n *   Whether `node` is an element and passes a test.\n */\nconst isElement =\n  /**\n   * @type {(\n   *   (() => false) &\n   *   (<T extends Element = Element>(node: unknown, test?: PredicateTest<T>, index?: number, parent?: Parent, context?: unknown) => node is T) &\n   *   ((node: unknown, test: Test, index?: number, parent?: Parent, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [node]\n     * @param {Test | undefined} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parent | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function (node, test, index, parent, context) {\n      const check = convertElement(test)\n\n      if (\n        index !== undefined &&\n        index !== null &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite index for child node')\n      }\n\n      if (\n        parent !== undefined &&\n        parent !== null &&\n        (!parent.type || !parent.children)\n      ) {\n        throw new Error('Expected parent node')\n      }\n\n      // @ts-expect-error Looks like a node.\n      if (!node || !node.type || typeof node.type !== 'string') {\n        return false\n      }\n\n      if (\n        (parent === undefined || parent === null) !==\n        (index === undefined || index === null)\n      ) {\n        throw new Error('Expected both parent and index')\n      }\n\n      return check.call(context, node, index, parent)\n    }\n  )\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param test\n *   *  When nullish, checks if `node` is an `Element`.\n *   *  When `string`, works like passing `(element) => element.tagName === test`.\n *   *  When `function` checks if function passed the element is true.\n *   *  When `array`, checks any one of the subtests pass.\n * @returns\n *   An assertion.\n */\nconst convertElement =\n  /**\n   * @type {(\n   *   (<T extends Element>(test: T['tagName'] | TestFunctionPredicate<T>) => AssertPredicate<T>) &\n   *   ((test?: Test) => AssertAnything)\n   * )}\n   */\n  (\n    /**\n     * @param {Test | null | undefined} [test]\n     * @returns {AssertAnything}\n     */\n    function (test) {\n      if (test === undefined || test === null) {\n        return element\n      }\n\n      if (typeof test === 'string') {\n        return tagNameFactory(test)\n      }\n\n      if (typeof test === 'object') {\n        return anyFactory(test)\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      throw new Error('Expected function, string, or array as test')\n    }\n  )\n\n/**\n * Handle multiple tests.\n *\n * @param {Array<string | TestFunctionAnything>} tests\n * @returns {AssertAnything}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<AssertAnything>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convertElement(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].call(this, ...parameters)) {\n        return true\n      }\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn a string into a test for an element with a certain tag name.\n *\n * @param {string} check\n * @returns {AssertAnything}\n */\nfunction tagNameFactory(check) {\n  return tagName\n\n  /**\n   * @param {unknown} node\n   * @returns {boolean}\n   */\n  function tagName(node) {\n    return element(node) && node.tagName === check\n  }\n}\n\n/**\n * Turn a custom test into a test for an element that passes that test.\n *\n * @param {TestFunctionAnything} check\n * @returns {AssertAnything}\n */\nfunction castFactory(check) {\n  return assertion\n\n  /**\n   * @this {unknown}\n   * @param {unknown} node\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function assertion(node, ...parameters) {\n    // @ts-expect-error: fine.\n    return element(node) && Boolean(check.call(this, node, ...parameters))\n  }\n}\n\n/**\n * Make sure something is an element.\n *\n * @param {unknown} node\n * @returns {node is Element}\n */\nfunction element(node) {\n  return Boolean(\n    node &&\n      typeof node === 'object' &&\n      // @ts-expect-error Looks like a node.\n      node.type === 'element' &&\n      // @ts-expect-error Looks like an element.\n      typeof node.tagName === 'string'\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLWlzLWVsZW1lbnQvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEsd0JBQXdCO0FBQ3JDLGFBQWEsd0JBQXdCO0FBQ3JDOztBQUVBO0FBQ0EsYUFBYSx5RkFBeUY7QUFDdEc7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxXQUFXLDJCQUEyQjtBQUN0QztBQUNBLFdBQVcsMkJBQTJCO0FBQ3RDO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7O0FBRUE7QUFDQSxjQUFjLFNBQVM7QUFDdkI7QUFDQSxhQUFhLDBGQUEwRjtBQUN2RztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsU0FBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxXQUFXLDJCQUEyQjtBQUN0QztBQUNBLFdBQVcsMkJBQTJCO0FBQ3RDO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCO0FBQ0EsV0FBVywyQkFBMkI7QUFDdEM7QUFDQSxXQUFXLDJCQUEyQjtBQUN0QztBQUNBLGFBQWE7QUFDYjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsU0FBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxXQUFXLDJCQUEyQjtBQUN0QztBQUNBLFdBQVcsMkJBQTJCO0FBQ3RDO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFNBQVM7QUFDeEIsZUFBZSxrQkFBa0I7QUFDakMsZUFBZSwyQkFBMkI7QUFDMUMsZUFBZSwyQkFBMkI7QUFDMUMsZUFBZSxTQUFTO0FBQ3hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLHlCQUF5QjtBQUN4QyxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxzQ0FBc0M7QUFDakQsYUFBYTtBQUNiO0FBQ0E7QUFDQSxhQUFhLHVCQUF1QjtBQUNwQztBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBLFlBQVk7QUFDWixhQUFhLGdCQUFnQjtBQUM3QixlQUFlO0FBQ2Y7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQTtBQUNBOztBQUVBO0FBQ0EsYUFBYSxTQUFTO0FBQ3RCLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsc0JBQXNCO0FBQ2pDLGFBQWE7QUFDYjtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osYUFBYSxTQUFTO0FBQ3RCLGFBQWEsZ0JBQWdCO0FBQzdCLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLWlzLWVsZW1lbnQvaW5kZXguanM/ZDYyMSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ3VuaXN0JykuUGFyZW50fSBQYXJlbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKi9cblxuLyoqXG4gKiBAdHlwZWRlZiB7bnVsbCB8IHVuZGVmaW5lZCB8IHN0cmluZyB8IFRlc3RGdW5jdGlvbkFueXRoaW5nIHwgQXJyYXk8c3RyaW5nIHwgVGVzdEZ1bmN0aW9uQW55dGhpbmc+fSBUZXN0XG4gKiAgIENoZWNrIGZvciBhbiBhcmJpdHJhcnkgZWxlbWVudCwgdW5hd2FyZSBvZiBUeXBlU2NyaXB0IGluZmVycmFsLlxuICpcbiAqIEBjYWxsYmFjayBUZXN0RnVuY3Rpb25Bbnl0aGluZ1xuICogICBDaGVjayBpZiBhbiBlbGVtZW50IHBhc3NlcyBhIHRlc3QsIHVuYXdhcmUgb2YgVHlwZVNjcmlwdCBpbmZlcnJhbC5cbiAqIEBwYXJhbSB7RWxlbWVudH0gZWxlbWVudFxuICogICBBbiBlbGVtZW50LlxuICogQHBhcmFtIHtudW1iZXIgfCBudWxsIHwgdW5kZWZpbmVkfSBbaW5kZXhdXG4gKiAgIFRoZSBlbGVtZW504oCZcyBwb3NpdGlvbiBpbiBpdHMgcGFyZW50LlxuICogQHBhcmFtIHtQYXJlbnQgfCBudWxsIHwgdW5kZWZpbmVkfSBbcGFyZW50XVxuICogICBUaGUgZWxlbWVudOKAmXMgcGFyZW50LlxuICogQHJldHVybnMge2Jvb2xlYW4gfCB2b2lkfVxuICogICBXaGV0aGVyIHRoaXMgZWxlbWVudCBwYXNzZXMgdGhlIHRlc3QuXG4gKi9cblxuLyoqXG4gKiBAdGVtcGxhdGUge0VsZW1lbnR9IFRcbiAqICAgRWxlbWVudCB0eXBlLlxuICogQHR5cGVkZWYge1RbJ3RhZ05hbWUnXSB8IFRlc3RGdW5jdGlvblByZWRpY2F0ZTxUPiB8IEFycmF5PFRbJ3RhZ05hbWUnXSB8IFRlc3RGdW5jdGlvblByZWRpY2F0ZTxUPj59IFByZWRpY2F0ZVRlc3RcbiAqICAgQ2hlY2sgZm9yIGFuIGVsZW1lbnQgdGhhdCBjYW4gYmUgaW5mZXJyZWQgYnkgVHlwZVNjcmlwdC5cbiAqL1xuXG4vKipcbiAqIENoZWNrIGlmIGFuIGVsZW1lbnQgcGFzc2VzIGEgY2VydGFpbiBub2RlIHRlc3QuXG4gKlxuICogQHRlbXBsYXRlIHtFbGVtZW50fSBUXG4gKiAgIEVsZW1lbnQgdHlwZS5cbiAqIEBjYWxsYmFjayBUZXN0RnVuY3Rpb25QcmVkaWNhdGVcbiAqICAgQ29tcGxleCB0ZXN0IGZ1bmN0aW9uIGZvciBhbiBlbGVtZW50IHRoYXQgY2FuIGJlIGluZmVycmVkIGJ5IFR5cGVTY3JpcHQuXG4gKiBAcGFyYW0ge0VsZW1lbnR9IGVsZW1lbnRcbiAqICAgQW4gZWxlbWVudC5cbiAqIEBwYXJhbSB7bnVtYmVyIHwgbnVsbCB8IHVuZGVmaW5lZH0gW2luZGV4XVxuICogICBUaGUgZWxlbWVudOKAmXMgcG9zaXRpb24gaW4gaXRzIHBhcmVudC5cbiAqIEBwYXJhbSB7UGFyZW50IHwgbnVsbCB8IHVuZGVmaW5lZH0gW3BhcmVudF1cbiAqICAgVGhlIGVsZW1lbnTigJlzIHBhcmVudC5cbiAqIEByZXR1cm5zIHtlbGVtZW50IGlzIFR9XG4gKiAgIFdoZXRoZXIgdGhpcyBlbGVtZW50IHBhc3NlcyB0aGUgdGVzdC5cbiAqL1xuXG4vKipcbiAqIEBjYWxsYmFjayBBc3NlcnRBbnl0aGluZ1xuICogICBDaGVjayB0aGF0IGFuIGFyYml0cmFyeSB2YWx1ZSBpcyBhbiBlbGVtZW50LCB1bmF3YXJlIG9mIFR5cGVTY3JpcHQgaW5mZXJyYWwuXG4gKiBAcGFyYW0ge3Vua25vd259IFtub2RlXVxuICogICBBbnl0aGluZyAodHlwaWNhbGx5IGEgbm9kZSkuXG4gKiBAcGFyYW0ge251bWJlciB8IG51bGwgfCB1bmRlZmluZWR9IFtpbmRleF1cbiAqICAgVGhlIG5vZGXigJlzIHBvc2l0aW9uIGluIGl0cyBwYXJlbnQuXG4gKiBAcGFyYW0ge1BhcmVudCB8IG51bGwgfCB1bmRlZmluZWR9IFtwYXJlbnRdXG4gKiAgIFRoZSBub2Rl4oCZcyBwYXJlbnQuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqICAgV2hldGhlciB0aGlzIGlzIGFuIGVsZW1lbnQgYW5kIHBhc3NlcyBhIHRlc3QuXG4gKi9cblxuLyoqXG4gKiBDaGVjayBpZiBhIG5vZGUgaXMgYW4gZWxlbWVudCBhbmQgcGFzc2VzIGEgY2VydGFpbiBub2RlIHRlc3RcbiAqXG4gKiBAdGVtcGxhdGUge0VsZW1lbnR9IFRcbiAqICAgRWxlbWVudCB0eXBlLlxuICogQGNhbGxiYWNrIEFzc2VydFByZWRpY2F0ZVxuICogICBDaGVjayB0aGF0IGFuIGFyYml0cmFyeSB2YWx1ZSBpcyBhIHNwZWNpZmljIGVsZW1lbnQsIGF3YXJlIG9mIFR5cGVTY3JpcHQuXG4gKiBAcGFyYW0ge3Vua25vd259IFtub2RlXVxuICogICBBbnl0aGluZyAodHlwaWNhbGx5IGEgbm9kZSkuXG4gKiBAcGFyYW0ge251bWJlciB8IG51bGwgfCB1bmRlZmluZWR9IFtpbmRleF1cbiAqICAgVGhlIG5vZGXigJlzIHBvc2l0aW9uIGluIGl0cyBwYXJlbnQuXG4gKiBAcGFyYW0ge1BhcmVudCB8IG51bGwgfCB1bmRlZmluZWR9IFtwYXJlbnRdXG4gKiAgIFRoZSBub2Rl4oCZcyBwYXJlbnQuXG4gKiBAcmV0dXJucyB7bm9kZSBpcyBUfVxuICogICBXaGV0aGVyIHRoaXMgaXMgYW4gZWxlbWVudCBhbmQgcGFzc2VzIGEgdGVzdC5cbiAqL1xuXG4vKipcbiAqIENoZWNrIGlmIGBub2RlYCBpcyBhbiBgRWxlbWVudGAgYW5kIHdoZXRoZXIgaXQgcGFzc2VzIHRoZSBnaXZlbiB0ZXN0LlxuICpcbiAqIEBwYXJhbSBub2RlXG4gKiAgIFRoaW5nIHRvIGNoZWNrLCB0eXBpY2FsbHkgYE5vZGVgLlxuICogQHBhcmFtIHRlc3RcbiAqICAgQSBjaGVjayBmb3IgYSBzcGVjaWZpYyBlbGVtZW50LlxuICogQHBhcmFtIGluZGV4XG4gKiAgIFRoZSBub2Rl4oCZcyBwb3NpdGlvbiBpbiBpdHMgcGFyZW50LlxuICogQHBhcmFtIHBhcmVudFxuICogICBUaGUgbm9kZeKAmXMgcGFyZW50LlxuICogQHJldHVybnNcbiAqICAgV2hldGhlciBgbm9kZWAgaXMgYW4gZWxlbWVudCBhbmQgcGFzc2VzIGEgdGVzdC5cbiAqL1xuZXhwb3J0IGNvbnN0IGlzRWxlbWVudCA9XG4gIC8qKlxuICAgKiBAdHlwZSB7KFxuICAgKiAgICgoKSA9PiBmYWxzZSkgJlxuICAgKiAgICg8VCBleHRlbmRzIEVsZW1lbnQgPSBFbGVtZW50Pihub2RlOiB1bmtub3duLCB0ZXN0PzogUHJlZGljYXRlVGVzdDxUPiwgaW5kZXg/OiBudW1iZXIsIHBhcmVudD86IFBhcmVudCwgY29udGV4dD86IHVua25vd24pID0+IG5vZGUgaXMgVCkgJlxuICAgKiAgICgobm9kZTogdW5rbm93biwgdGVzdDogVGVzdCwgaW5kZXg/OiBudW1iZXIsIHBhcmVudD86IFBhcmVudCwgY29udGV4dD86IHVua25vd24pID0+IGJvb2xlYW4pXG4gICAqICl9XG4gICAqL1xuICAoXG4gICAgLyoqXG4gICAgICogQHBhcmFtIHt1bmtub3dufSBbbm9kZV1cbiAgICAgKiBAcGFyYW0ge1Rlc3QgfCB1bmRlZmluZWR9IFt0ZXN0XVxuICAgICAqIEBwYXJhbSB7bnVtYmVyIHwgbnVsbCB8IHVuZGVmaW5lZH0gW2luZGV4XVxuICAgICAqIEBwYXJhbSB7UGFyZW50IHwgbnVsbCB8IHVuZGVmaW5lZH0gW3BhcmVudF1cbiAgICAgKiBAcGFyYW0ge3Vua25vd259IFtjb250ZXh0XVxuICAgICAqIEByZXR1cm5zIHtib29sZWFufVxuICAgICAqL1xuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBtYXgtcGFyYW1zXG4gICAgZnVuY3Rpb24gKG5vZGUsIHRlc3QsIGluZGV4LCBwYXJlbnQsIGNvbnRleHQpIHtcbiAgICAgIGNvbnN0IGNoZWNrID0gY29udmVydEVsZW1lbnQodGVzdClcblxuICAgICAgaWYgKFxuICAgICAgICBpbmRleCAhPT0gdW5kZWZpbmVkICYmXG4gICAgICAgIGluZGV4ICE9PSBudWxsICYmXG4gICAgICAgICh0eXBlb2YgaW5kZXggIT09ICdudW1iZXInIHx8XG4gICAgICAgICAgaW5kZXggPCAwIHx8XG4gICAgICAgICAgaW5kZXggPT09IE51bWJlci5QT1NJVElWRV9JTkZJTklUWSlcbiAgICAgICkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0V4cGVjdGVkIHBvc2l0aXZlIGZpbml0ZSBpbmRleCBmb3IgY2hpbGQgbm9kZScpXG4gICAgICB9XG5cbiAgICAgIGlmIChcbiAgICAgICAgcGFyZW50ICE9PSB1bmRlZmluZWQgJiZcbiAgICAgICAgcGFyZW50ICE9PSBudWxsICYmXG4gICAgICAgICghcGFyZW50LnR5cGUgfHwgIXBhcmVudC5jaGlsZHJlbilcbiAgICAgICkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0V4cGVjdGVkIHBhcmVudCBub2RlJylcbiAgICAgIH1cblxuICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvciBMb29rcyBsaWtlIGEgbm9kZS5cbiAgICAgIGlmICghbm9kZSB8fCAhbm9kZS50eXBlIHx8IHR5cGVvZiBub2RlLnR5cGUgIT09ICdzdHJpbmcnKSB7XG4gICAgICAgIHJldHVybiBmYWxzZVxuICAgICAgfVxuXG4gICAgICBpZiAoXG4gICAgICAgIChwYXJlbnQgPT09IHVuZGVmaW5lZCB8fCBwYXJlbnQgPT09IG51bGwpICE9PVxuICAgICAgICAoaW5kZXggPT09IHVuZGVmaW5lZCB8fCBpbmRleCA9PT0gbnVsbClcbiAgICAgICkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0V4cGVjdGVkIGJvdGggcGFyZW50IGFuZCBpbmRleCcpXG4gICAgICB9XG5cbiAgICAgIHJldHVybiBjaGVjay5jYWxsKGNvbnRleHQsIG5vZGUsIGluZGV4LCBwYXJlbnQpXG4gICAgfVxuICApXG5cbi8qKlxuICogR2VuZXJhdGUgYW4gYXNzZXJ0aW9uIGZyb20gYSB0ZXN0LlxuICpcbiAqIFVzZWZ1bCBpZiB5b3XigJlyZSBnb2luZyB0byB0ZXN0IG1hbnkgbm9kZXMsIGZvciBleGFtcGxlIHdoZW4gY3JlYXRpbmcgYVxuICogdXRpbGl0eSB3aGVyZSBzb21ldGhpbmcgZWxzZSBwYXNzZXMgYSBjb21wYXRpYmxlIHRlc3QuXG4gKlxuICogVGhlIGNyZWF0ZWQgZnVuY3Rpb24gaXMgYSBiaXQgZmFzdGVyIGJlY2F1c2UgaXQgZXhwZWN0cyB2YWxpZCBpbnB1dCBvbmx5OlxuICogYSBgbm9kZWAsIGBpbmRleGAsIGFuZCBgcGFyZW50YC5cbiAqXG4gKiBAcGFyYW0gdGVzdFxuICogICAqICBXaGVuIG51bGxpc2gsIGNoZWNrcyBpZiBgbm9kZWAgaXMgYW4gYEVsZW1lbnRgLlxuICogICAqICBXaGVuIGBzdHJpbmdgLCB3b3JrcyBsaWtlIHBhc3NpbmcgYChlbGVtZW50KSA9PiBlbGVtZW50LnRhZ05hbWUgPT09IHRlc3RgLlxuICogICAqICBXaGVuIGBmdW5jdGlvbmAgY2hlY2tzIGlmIGZ1bmN0aW9uIHBhc3NlZCB0aGUgZWxlbWVudCBpcyB0cnVlLlxuICogICAqICBXaGVuIGBhcnJheWAsIGNoZWNrcyBhbnkgb25lIG9mIHRoZSBzdWJ0ZXN0cyBwYXNzLlxuICogQHJldHVybnNcbiAqICAgQW4gYXNzZXJ0aW9uLlxuICovXG5leHBvcnQgY29uc3QgY29udmVydEVsZW1lbnQgPVxuICAvKipcbiAgICogQHR5cGUgeyhcbiAgICogICAoPFQgZXh0ZW5kcyBFbGVtZW50Pih0ZXN0OiBUWyd0YWdOYW1lJ10gfCBUZXN0RnVuY3Rpb25QcmVkaWNhdGU8VD4pID0+IEFzc2VydFByZWRpY2F0ZTxUPikgJlxuICAgKiAgICgodGVzdD86IFRlc3QpID0+IEFzc2VydEFueXRoaW5nKVxuICAgKiApfVxuICAgKi9cbiAgKFxuICAgIC8qKlxuICAgICAqIEBwYXJhbSB7VGVzdCB8IG51bGwgfCB1bmRlZmluZWR9IFt0ZXN0XVxuICAgICAqIEByZXR1cm5zIHtBc3NlcnRBbnl0aGluZ31cbiAgICAgKi9cbiAgICBmdW5jdGlvbiAodGVzdCkge1xuICAgICAgaWYgKHRlc3QgPT09IHVuZGVmaW5lZCB8fCB0ZXN0ID09PSBudWxsKSB7XG4gICAgICAgIHJldHVybiBlbGVtZW50XG4gICAgICB9XG5cbiAgICAgIGlmICh0eXBlb2YgdGVzdCA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgcmV0dXJuIHRhZ05hbWVGYWN0b3J5KHRlc3QpXG4gICAgICB9XG5cbiAgICAgIGlmICh0eXBlb2YgdGVzdCA9PT0gJ29iamVjdCcpIHtcbiAgICAgICAgcmV0dXJuIGFueUZhY3RvcnkodGVzdClcbiAgICAgIH1cblxuICAgICAgaWYgKHR5cGVvZiB0ZXN0ID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgIHJldHVybiBjYXN0RmFjdG9yeSh0ZXN0KVxuICAgICAgfVxuXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0V4cGVjdGVkIGZ1bmN0aW9uLCBzdHJpbmcsIG9yIGFycmF5IGFzIHRlc3QnKVxuICAgIH1cbiAgKVxuXG4vKipcbiAqIEhhbmRsZSBtdWx0aXBsZSB0ZXN0cy5cbiAqXG4gKiBAcGFyYW0ge0FycmF5PHN0cmluZyB8IFRlc3RGdW5jdGlvbkFueXRoaW5nPn0gdGVzdHNcbiAqIEByZXR1cm5zIHtBc3NlcnRBbnl0aGluZ31cbiAqL1xuZnVuY3Rpb24gYW55RmFjdG9yeSh0ZXN0cykge1xuICAvKiogQHR5cGUge0FycmF5PEFzc2VydEFueXRoaW5nPn0gKi9cbiAgY29uc3QgY2hlY2tzID0gW11cbiAgbGV0IGluZGV4ID0gLTFcblxuICB3aGlsZSAoKytpbmRleCA8IHRlc3RzLmxlbmd0aCkge1xuICAgIGNoZWNrc1tpbmRleF0gPSBjb252ZXJ0RWxlbWVudCh0ZXN0c1tpbmRleF0pXG4gIH1cblxuICByZXR1cm4gY2FzdEZhY3RvcnkoYW55KVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7dW5rbm93bn1cbiAgICogQHBhcmFtIHtBcnJheTx1bmtub3duPn0gcGFyYW1ldGVyc1xuICAgKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAgICovXG4gIGZ1bmN0aW9uIGFueSguLi5wYXJhbWV0ZXJzKSB7XG4gICAgbGV0IGluZGV4ID0gLTFcblxuICAgIHdoaWxlICgrK2luZGV4IDwgY2hlY2tzLmxlbmd0aCkge1xuICAgICAgaWYgKGNoZWNrc1tpbmRleF0uY2FsbCh0aGlzLCAuLi5wYXJhbWV0ZXJzKSkge1xuICAgICAgICByZXR1cm4gdHJ1ZVxuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiBmYWxzZVxuICB9XG59XG5cbi8qKlxuICogVHVybiBhIHN0cmluZyBpbnRvIGEgdGVzdCBmb3IgYW4gZWxlbWVudCB3aXRoIGEgY2VydGFpbiB0YWcgbmFtZS5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gY2hlY2tcbiAqIEByZXR1cm5zIHtBc3NlcnRBbnl0aGluZ31cbiAqL1xuZnVuY3Rpb24gdGFnTmFtZUZhY3RvcnkoY2hlY2spIHtcbiAgcmV0dXJuIHRhZ05hbWVcblxuICAvKipcbiAgICogQHBhcmFtIHt1bmtub3dufSBub2RlXG4gICAqIEByZXR1cm5zIHtib29sZWFufVxuICAgKi9cbiAgZnVuY3Rpb24gdGFnTmFtZShub2RlKSB7XG4gICAgcmV0dXJuIGVsZW1lbnQobm9kZSkgJiYgbm9kZS50YWdOYW1lID09PSBjaGVja1xuICB9XG59XG5cbi8qKlxuICogVHVybiBhIGN1c3RvbSB0ZXN0IGludG8gYSB0ZXN0IGZvciBhbiBlbGVtZW50IHRoYXQgcGFzc2VzIHRoYXQgdGVzdC5cbiAqXG4gKiBAcGFyYW0ge1Rlc3RGdW5jdGlvbkFueXRoaW5nfSBjaGVja1xuICogQHJldHVybnMge0Fzc2VydEFueXRoaW5nfVxuICovXG5mdW5jdGlvbiBjYXN0RmFjdG9yeShjaGVjaykge1xuICByZXR1cm4gYXNzZXJ0aW9uXG5cbiAgLyoqXG4gICAqIEB0aGlzIHt1bmtub3dufVxuICAgKiBAcGFyYW0ge3Vua25vd259IG5vZGVcbiAgICogQHBhcmFtIHtBcnJheTx1bmtub3duPn0gcGFyYW1ldGVyc1xuICAgKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAgICovXG4gIGZ1bmN0aW9uIGFzc2VydGlvbihub2RlLCAuLi5wYXJhbWV0ZXJzKSB7XG4gICAgLy8gQHRzLWV4cGVjdC1lcnJvcjogZmluZS5cbiAgICByZXR1cm4gZWxlbWVudChub2RlKSAmJiBCb29sZWFuKGNoZWNrLmNhbGwodGhpcywgbm9kZSwgLi4ucGFyYW1ldGVycykpXG4gIH1cbn1cblxuLyoqXG4gKiBNYWtlIHN1cmUgc29tZXRoaW5nIGlzIGFuIGVsZW1lbnQuXG4gKlxuICogQHBhcmFtIHt1bmtub3dufSBub2RlXG4gKiBAcmV0dXJucyB7bm9kZSBpcyBFbGVtZW50fVxuICovXG5mdW5jdGlvbiBlbGVtZW50KG5vZGUpIHtcbiAgcmV0dXJuIEJvb2xlYW4oXG4gICAgbm9kZSAmJlxuICAgICAgdHlwZW9mIG5vZGUgPT09ICdvYmplY3QnICYmXG4gICAgICAvLyBAdHMtZXhwZWN0LWVycm9yIExvb2tzIGxpa2UgYSBub2RlLlxuICAgICAgbm9kZS50eXBlID09PSAnZWxlbWVudCcgJiZcbiAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgTG9va3MgbGlrZSBhbiBlbGVtZW50LlxuICAgICAgdHlwZW9mIG5vZGUudGFnTmFtZSA9PT0gJ3N0cmluZydcbiAgKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-is-element/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-phrasing/lib/index.js":
/*!**********************************************************!*\
  !*** ../../node_modules/hast-util-phrasing/lib/index.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   phrasing: function() { return /* binding */ phrasing; }\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(app-pages-browser)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var hast_util_has_property__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-has-property */ \"(app-pages-browser)/../../node_modules/hast-util-has-property/lib/index.js\");\n/* harmony import */ var hast_util_embedded__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-embedded */ \"(app-pages-browser)/../../node_modules/hast-util-phrasing/node_modules/hast-util-embedded/lib/index.js\");\n/* harmony import */ var hast_util_is_body_ok_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-is-body-ok-link */ \"(app-pages-browser)/../../node_modules/hast-util-is-body-ok-link/index.js\");\n/**\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Content} Content\n */\n\n/**\n * @typedef {Root | Content} Node\n */\n\n\n\n\n\n\nconst basic = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  'a',\n  'abbr',\n  // `area` is in fact only phrasing if it is inside a `map` element.\n  // However, since `area`s are required to be inside a `map` element, and it’s\n  // a rather involved check, it’s ignored here for now.\n  'area',\n  'b',\n  'bdi',\n  'bdo',\n  'br',\n  'button',\n  'cite',\n  'code',\n  'data',\n  'datalist',\n  'del',\n  'dfn',\n  'em',\n  'i',\n  'input',\n  'ins',\n  'kbd',\n  'keygen',\n  'label',\n  'map',\n  'mark',\n  'meter',\n  'noscript',\n  'output',\n  'progress',\n  'q',\n  'ruby',\n  's',\n  'samp',\n  'script',\n  'select',\n  'small',\n  'span',\n  'strong',\n  'sub',\n  'sup',\n  'template',\n  'textarea',\n  'time',\n  'u',\n  'var',\n  'wbr'\n])\n\nconst meta = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('meta')\n\n/**\n * Check if the given value is *phrasing* content.\n *\n * @param {unknown} value\n *   Thing to check, typically `Node`.\n * @returns {boolean}\n *   Whether `value` is phrasing content.\n */\nfunction phrasing(value) {\n  return Boolean(\n    node(value) &&\n      (value.type === 'text' ||\n        basic(value) ||\n        (0,hast_util_embedded__WEBPACK_IMPORTED_MODULE_1__.embedded)(value) ||\n        (0,hast_util_is_body_ok_link__WEBPACK_IMPORTED_MODULE_2__.isBodyOkLink)(value) ||\n        (meta(value) && (0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_3__.hasProperty)(value, 'itemProp')))\n  )\n}\n\n/**\n * @param {unknown} value\n * @returns {value is Node}\n */\nfunction node(value) {\n  // @ts-expect-error: looks like an object.\n  return value && typeof value === 'object' && 'type' in value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXBocmFzaW5nL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQ0EsYUFBYSxxQkFBcUI7QUFDbEMsYUFBYSx3QkFBd0I7QUFDckM7O0FBRUE7QUFDQSxhQUFhLGdCQUFnQjtBQUM3Qjs7QUFFbUQ7QUFDRDtBQUNQO0FBQ1c7O0FBRXRELGNBQWMsb0VBQWM7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGFBQWEsb0VBQWM7O0FBRTNCO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsNERBQVE7QUFDaEIsUUFBUSx1RUFBWTtBQUNwQix3QkFBd0IsbUVBQVc7QUFDbkM7QUFDQTs7QUFFQTtBQUNBLFdBQVcsU0FBUztBQUNwQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC1waHJhc2luZy9saWIvaW5kZXguanM/MDdjYyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5Sb290fSBSb290XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuQ29udGVudH0gQ29udGVudFxuICovXG5cbi8qKlxuICogQHR5cGVkZWYge1Jvb3QgfCBDb250ZW50fSBOb2RlXG4gKi9cblxuaW1wb3J0IHtjb252ZXJ0RWxlbWVudH0gZnJvbSAnaGFzdC11dGlsLWlzLWVsZW1lbnQnXG5pbXBvcnQge2hhc1Byb3BlcnR5fSBmcm9tICdoYXN0LXV0aWwtaGFzLXByb3BlcnR5J1xuaW1wb3J0IHtlbWJlZGRlZH0gZnJvbSAnaGFzdC11dGlsLWVtYmVkZGVkJ1xuaW1wb3J0IHtpc0JvZHlPa0xpbmt9IGZyb20gJ2hhc3QtdXRpbC1pcy1ib2R5LW9rLWxpbmsnXG5cbmNvbnN0IGJhc2ljID0gY29udmVydEVsZW1lbnQoW1xuICAnYScsXG4gICdhYmJyJyxcbiAgLy8gYGFyZWFgIGlzIGluIGZhY3Qgb25seSBwaHJhc2luZyBpZiBpdCBpcyBpbnNpZGUgYSBgbWFwYCBlbGVtZW50LlxuICAvLyBIb3dldmVyLCBzaW5jZSBgYXJlYWBzIGFyZSByZXF1aXJlZCB0byBiZSBpbnNpZGUgYSBgbWFwYCBlbGVtZW50LCBhbmQgaXTigJlzXG4gIC8vIGEgcmF0aGVyIGludm9sdmVkIGNoZWNrLCBpdOKAmXMgaWdub3JlZCBoZXJlIGZvciBub3cuXG4gICdhcmVhJyxcbiAgJ2InLFxuICAnYmRpJyxcbiAgJ2JkbycsXG4gICdicicsXG4gICdidXR0b24nLFxuICAnY2l0ZScsXG4gICdjb2RlJyxcbiAgJ2RhdGEnLFxuICAnZGF0YWxpc3QnLFxuICAnZGVsJyxcbiAgJ2RmbicsXG4gICdlbScsXG4gICdpJyxcbiAgJ2lucHV0JyxcbiAgJ2lucycsXG4gICdrYmQnLFxuICAna2V5Z2VuJyxcbiAgJ2xhYmVsJyxcbiAgJ21hcCcsXG4gICdtYXJrJyxcbiAgJ21ldGVyJyxcbiAgJ25vc2NyaXB0JyxcbiAgJ291dHB1dCcsXG4gICdwcm9ncmVzcycsXG4gICdxJyxcbiAgJ3J1YnknLFxuICAncycsXG4gICdzYW1wJyxcbiAgJ3NjcmlwdCcsXG4gICdzZWxlY3QnLFxuICAnc21hbGwnLFxuICAnc3BhbicsXG4gICdzdHJvbmcnLFxuICAnc3ViJyxcbiAgJ3N1cCcsXG4gICd0ZW1wbGF0ZScsXG4gICd0ZXh0YXJlYScsXG4gICd0aW1lJyxcbiAgJ3UnLFxuICAndmFyJyxcbiAgJ3dicidcbl0pXG5cbmNvbnN0IG1ldGEgPSBjb252ZXJ0RWxlbWVudCgnbWV0YScpXG5cbi8qKlxuICogQ2hlY2sgaWYgdGhlIGdpdmVuIHZhbHVlIGlzICpwaHJhc2luZyogY29udGVudC5cbiAqXG4gKiBAcGFyYW0ge3Vua25vd259IHZhbHVlXG4gKiAgIFRoaW5nIHRvIGNoZWNrLCB0eXBpY2FsbHkgYE5vZGVgLlxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKiAgIFdoZXRoZXIgYHZhbHVlYCBpcyBwaHJhc2luZyBjb250ZW50LlxuICovXG5leHBvcnQgZnVuY3Rpb24gcGhyYXNpbmcodmFsdWUpIHtcbiAgcmV0dXJuIEJvb2xlYW4oXG4gICAgbm9kZSh2YWx1ZSkgJiZcbiAgICAgICh2YWx1ZS50eXBlID09PSAndGV4dCcgfHxcbiAgICAgICAgYmFzaWModmFsdWUpIHx8XG4gICAgICAgIGVtYmVkZGVkKHZhbHVlKSB8fFxuICAgICAgICBpc0JvZHlPa0xpbmsodmFsdWUpIHx8XG4gICAgICAgIChtZXRhKHZhbHVlKSAmJiBoYXNQcm9wZXJ0eSh2YWx1ZSwgJ2l0ZW1Qcm9wJykpKVxuICApXG59XG5cbi8qKlxuICogQHBhcmFtIHt1bmtub3dufSB2YWx1ZVxuICogQHJldHVybnMge3ZhbHVlIGlzIE5vZGV9XG4gKi9cbmZ1bmN0aW9uIG5vZGUodmFsdWUpIHtcbiAgLy8gQHRzLWV4cGVjdC1lcnJvcjogbG9va3MgbGlrZSBhbiBvYmplY3QuXG4gIHJldHVybiB2YWx1ZSAmJiB0eXBlb2YgdmFsdWUgPT09ICdvYmplY3QnICYmICd0eXBlJyBpbiB2YWx1ZVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-phrasing/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-phrasing/node_modules/hast-util-embedded/lib/index.js":
/*!******************************************************************************************!*\
  !*** ../../node_modules/hast-util-phrasing/node_modules/hast-util-embedded/lib/index.js ***!
  \******************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   embedded: function() { return /* binding */ embedded; }\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(app-pages-browser)/../../node_modules/hast-util-is-element/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n */\n\n\n\n/**\n * Check if a node is a *embedded content*.\n *\n * @type {import('hast-util-is-element').AssertPredicate<Element & {tagName: 'audio' | 'canvas' | 'embed' | 'iframe' | 'img' | 'math' | 'object' | 'picture' | 'svg' | 'video'}>}\n * @param value\n *   Thing to check (typically `Node`).\n * @returns\n *   Whether `value` is an element considered embedded content.\n *\n *   The elements `audio`, `canvas`, `embed`, `iframe`, `img`, `math`,\n *   `object`, `picture`, `svg`, and `video` are embedded content.\n */\n// @ts-expect-error Sure, the assertion matches.\nconst embedded = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  'audio',\n  'canvas',\n  'embed',\n  'iframe',\n  'img',\n  'math',\n  'object',\n  'picture',\n  'svg',\n  'video'\n])\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXBocmFzaW5nL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtZW1iZWRkZWQvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLHdCQUF3QjtBQUNyQzs7QUFFbUQ7O0FBRW5EO0FBQ0E7QUFDQTtBQUNBLFVBQVUsMERBQTBELDJHQUEyRztBQUMvSztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTyxpQkFBaUIsb0VBQWM7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC1waHJhc2luZy9ub2RlX21vZHVsZXMvaGFzdC11dGlsLWVtYmVkZGVkL2xpYi9pbmRleC5qcz8wODNlIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqL1xuXG5pbXBvcnQge2NvbnZlcnRFbGVtZW50fSBmcm9tICdoYXN0LXV0aWwtaXMtZWxlbWVudCdcblxuLyoqXG4gKiBDaGVjayBpZiBhIG5vZGUgaXMgYSAqZW1iZWRkZWQgY29udGVudCouXG4gKlxuICogQHR5cGUge2ltcG9ydCgnaGFzdC11dGlsLWlzLWVsZW1lbnQnKS5Bc3NlcnRQcmVkaWNhdGU8RWxlbWVudCAmIHt0YWdOYW1lOiAnYXVkaW8nIHwgJ2NhbnZhcycgfCAnZW1iZWQnIHwgJ2lmcmFtZScgfCAnaW1nJyB8ICdtYXRoJyB8ICdvYmplY3QnIHwgJ3BpY3R1cmUnIHwgJ3N2ZycgfCAndmlkZW8nfT59XG4gKiBAcGFyYW0gdmFsdWVcbiAqICAgVGhpbmcgdG8gY2hlY2sgKHR5cGljYWxseSBgTm9kZWApLlxuICogQHJldHVybnNcbiAqICAgV2hldGhlciBgdmFsdWVgIGlzIGFuIGVsZW1lbnQgY29uc2lkZXJlZCBlbWJlZGRlZCBjb250ZW50LlxuICpcbiAqICAgVGhlIGVsZW1lbnRzIGBhdWRpb2AsIGBjYW52YXNgLCBgZW1iZWRgLCBgaWZyYW1lYCwgYGltZ2AsIGBtYXRoYCxcbiAqICAgYG9iamVjdGAsIGBwaWN0dXJlYCwgYHN2Z2AsIGFuZCBgdmlkZW9gIGFyZSBlbWJlZGRlZCBjb250ZW50LlxuICovXG4vLyBAdHMtZXhwZWN0LWVycm9yIFN1cmUsIHRoZSBhc3NlcnRpb24gbWF0Y2hlcy5cbmV4cG9ydCBjb25zdCBlbWJlZGRlZCA9IGNvbnZlcnRFbGVtZW50KFtcbiAgJ2F1ZGlvJyxcbiAgJ2NhbnZhcycsXG4gICdlbWJlZCcsXG4gICdpZnJhbWUnLFxuICAnaW1nJyxcbiAgJ21hdGgnLFxuICAnb2JqZWN0JyxcbiAgJ3BpY3R1cmUnLFxuICAnc3ZnJyxcbiAgJ3ZpZGVvJ1xuXSlcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-phrasing/node_modules/hast-util-embedded/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/all.js":
/*!********************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/all.js ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   all: function() { return /* binding */ all; }\n/* harmony export */ });\n/* harmony import */ var _one_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./one.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/one.js\");\n/**\n * @typedef {import('./types.js').H} H\n * @typedef {import('./types.js').Node} Node\n * @typedef {import('./types.js').MdastNode} MdastNode\n */\n\n\n\n/**\n * @param {H} h\n * @param {Node} parent\n * @returns {Array<MdastNode>}\n */\nfunction all(h, parent) {\n  /** @type {Array<Node>} */\n  // @ts-expect-error Assume `parent` is a parent.\n  const nodes = parent.children || []\n  /** @type {Array<MdastNode>} */\n  const values = []\n  let index = -1\n\n  while (++index < nodes.length) {\n    // @ts-expect-error assume `parent` is a parent.\n    const result = (0,_one_js__WEBPACK_IMPORTED_MODULE_0__.one)(h, nodes[index], parent)\n\n    if (Array.isArray(result)) {\n      values.push(...result)\n    } else if (result) {\n      values.push(result)\n    }\n  }\n\n  let start = 0\n  let end = values.length\n\n  while (start < end && values[start].type === 'break') {\n    start++\n  }\n\n  while (end > start && values[end - 1].type === 'break') {\n    end--\n  }\n\n  return start === 0 && end === values.length\n    ? values\n    : values.slice(start, end)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/all.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/a.js":
/*!***************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/a.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   a: function() { return /* binding */ a; }\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../all.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/* harmony import */ var _util_resolve_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/resolve.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/resolve.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Properties} Properties\n */\n\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction a(h, node) {\n  /** @type {Properties} */\n  // @ts-expect-error: `props` are defined.\n  const props = node.properties\n  return h(\n    node,\n    'link',\n    {\n      title: props.title || null,\n      url: (0,_util_resolve_js__WEBPACK_IMPORTED_MODULE_0__.resolve)(h, String(props.href || '') || null)\n    },\n    (0,_all_js__WEBPACK_IMPORTED_MODULE_1__.all)(h, node)\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9hLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSwrQkFBK0I7QUFDNUMsYUFBYSxrQ0FBa0M7QUFDL0M7O0FBRTZCO0FBQ2E7O0FBRTFDO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1AsYUFBYSxZQUFZO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyx5REFBTztBQUNsQixLQUFLO0FBQ0wsSUFBSSw0Q0FBRztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL2EuanM/NTU4MCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSGFuZGxlfSBIYW5kbGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5Qcm9wZXJ0aWVzfSBQcm9wZXJ0aWVzXG4gKi9cblxuaW1wb3J0IHthbGx9IGZyb20gJy4uL2FsbC5qcydcbmltcG9ydCB7cmVzb2x2ZX0gZnJvbSAnLi4vdXRpbC9yZXNvbHZlLmpzJ1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge0VsZW1lbnR9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGEoaCwgbm9kZSkge1xuICAvKiogQHR5cGUge1Byb3BlcnRpZXN9ICovXG4gIC8vIEB0cy1leHBlY3QtZXJyb3I6IGBwcm9wc2AgYXJlIGRlZmluZWQuXG4gIGNvbnN0IHByb3BzID0gbm9kZS5wcm9wZXJ0aWVzXG4gIHJldHVybiBoKFxuICAgIG5vZGUsXG4gICAgJ2xpbmsnLFxuICAgIHtcbiAgICAgIHRpdGxlOiBwcm9wcy50aXRsZSB8fCBudWxsLFxuICAgICAgdXJsOiByZXNvbHZlKGgsIFN0cmluZyhwcm9wcy5ocmVmIHx8ICcnKSB8fCBudWxsKVxuICAgIH0sXG4gICAgYWxsKGgsIG5vZGUpXG4gIClcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/a.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/base.js":
/*!******************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/base.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base: function() { return /* binding */ base; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction base(h, node) {\n  if (!h.baseFound) {\n    h.frozenBaseUrl =\n      String((node.properties && node.properties.href) || '') || null\n    h.baseFound = true\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9iYXNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsK0JBQStCO0FBQzVDOztBQUVBO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9iYXNlLmpzPzBjZWMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqL1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge0VsZW1lbnR9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGJhc2UoaCwgbm9kZSkge1xuICBpZiAoIWguYmFzZUZvdW5kKSB7XG4gICAgaC5mcm96ZW5CYXNlVXJsID1cbiAgICAgIFN0cmluZygobm9kZS5wcm9wZXJ0aWVzICYmIG5vZGUucHJvcGVydGllcy5ocmVmKSB8fCAnJykgfHwgbnVsbFxuICAgIGguYmFzZUZvdW5kID0gdHJ1ZVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/base.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/blockquote.js":
/*!************************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/blockquote.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blockquote: function() { return /* binding */ blockquote; }\n/* harmony export */ });\n/* harmony import */ var _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/wrap-children.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap-children.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction blockquote(h, node) {\n  return h(node, 'blockquote', (0,_util_wrap_children_js__WEBPACK_IMPORTED_MODULE_0__.wrapChildren)(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9ibG9ja3F1b3RlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFcUQ7O0FBRXJEO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1AsK0JBQStCLG9FQUFZO0FBQzNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9ibG9ja3F1b3RlLmpzPzFhNTkiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqL1xuXG5pbXBvcnQge3dyYXBDaGlsZHJlbn0gZnJvbSAnLi4vdXRpbC93cmFwLWNoaWxkcmVuLmpzJ1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge0VsZW1lbnR9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGJsb2NrcXVvdGUoaCwgbm9kZSkge1xuICByZXR1cm4gaChub2RlLCAnYmxvY2txdW90ZScsIHdyYXBDaGlsZHJlbihoLCBub2RlKSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/blockquote.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/br.js":
/*!****************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/br.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   br: function() { return /* binding */ br; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction br(h, node) {\n  return h.wrapText ? h(node, 'break') : h(node, 'text', ' ')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9ici5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFQTtBQUNBLFVBQVU7QUFDVixXQUFXLFNBQVM7QUFDcEI7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL2JyLmpzPzYxYjkiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqL1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge0VsZW1lbnR9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGJyKGgsIG5vZGUpIHtcbiAgcmV0dXJuIGgud3JhcFRleHQgPyBoKG5vZGUsICdicmVhaycpIDogaChub2RlLCAndGV4dCcsICcgJylcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/br.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/code.js":
/*!******************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/code.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   code: function() { return /* binding */ code; }\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(app-pages-browser)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-to-text */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var trim_trailing_lines__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! trim-trailing-lines */ \"(app-pages-browser)/../../node_modules/trim-trailing-lines/index.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').ElementChild} ElementChild\n */\n\n\n\n\n\n\nconst prefix = 'language-'\n\nconst pre = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('pre')\nconst isCode = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('code')\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction code(h, node) {\n  const children = node.children\n  let index = -1\n  /** @type {Array<string|number>|undefined} */\n  let classList\n  /** @type {string|undefined} */\n  let lang\n\n  if (pre(node)) {\n    while (++index < children.length) {\n      const child = children[index]\n\n      if (\n        isCode(child) &&\n        child.properties &&\n        child.properties.className &&\n        Array.isArray(child.properties.className)\n      ) {\n        classList = child.properties.className\n        break\n      }\n    }\n  }\n\n  if (classList) {\n    index = -1\n\n    while (++index < classList.length) {\n      if (String(classList[index]).slice(0, prefix.length) === prefix) {\n        lang = String(classList[index]).slice(prefix.length)\n        break\n      }\n    }\n  }\n\n  return h(\n    node,\n    'code',\n    {lang: lang || null, meta: null},\n    (0,trim_trailing_lines__WEBPACK_IMPORTED_MODULE_1__.trimTrailingLines)((0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_2__.wrapText)(h, (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__.toText)(node)))\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/code.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/comment.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/comment.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   comment: function() { return /* binding */ comment; }\n/* harmony export */ });\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Comment} Comment\n */\n\n\n/**\n * @type {Handle}\n * @param {Comment} node\n */\nfunction comment(h, node) {\n  return h(node, 'html', '<!--' + (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__.wrapText)(h, node.value) + '-->')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9jb21tZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1QztBQUM2Qzs7QUFFN0M7QUFDQSxVQUFVO0FBQ1YsV0FBVyxTQUFTO0FBQ3BCO0FBQ087QUFDUCxrQ0FBa0MsNERBQVE7QUFDMUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL2NvbW1lbnQuanM/MGI3OSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSGFuZGxlfSBIYW5kbGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuQ29tbWVudH0gQ29tbWVudFxuICovXG5pbXBvcnQge3dyYXBUZXh0fSBmcm9tICcuLi91dGlsL3dyYXAtdGV4dC5qcydcblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtDb21tZW50fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjb21tZW50KGgsIG5vZGUpIHtcbiAgcmV0dXJuIGgobm9kZSwgJ2h0bWwnLCAnPCEtLScgKyB3cmFwVGV4dChoLCBub2RlLnZhbHVlKSArICctLT4nKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/comment.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/del.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/del.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   del: function() { return /* binding */ del; }\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction del(h, node) {\n  return h(node, 'delete', (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9kZWwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsK0JBQStCO0FBQzVDOztBQUU2Qjs7QUFFN0I7QUFDQSxVQUFVO0FBQ1YsV0FBVyxTQUFTO0FBQ3BCO0FBQ087QUFDUCwyQkFBMkIsNENBQUc7QUFDOUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL2RlbC5qcz8zNjE0Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKi9cblxuaW1wb3J0IHthbGx9IGZyb20gJy4uL2FsbC5qcydcblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtFbGVtZW50fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBkZWwoaCwgbm9kZSkge1xuICByZXR1cm4gaChub2RlLCAnZGVsZXRlJywgYWxsKGgsIG5vZGUpKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/del.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/dl.js":
/*!****************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/dl.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dl: function() { return /* binding */ dl; }\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(app-pages-browser)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var _util_list_items_spread_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/list-items-spread.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/list-items-spread.js\");\n/* harmony import */ var _util_wrap_list_items_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/wrap-list-items.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap-list-items.js\");\n/**\n * @typedef {import('../types.js').H} H\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').ElementChild} ElementChild\n * @typedef {import('../types.js').MdastNode} MdastNode\n * @typedef {import('../types.js').MdastListContent} MdastListContent\n * @typedef {import('../types.js').MdastBlockContent} MdastBlockContent\n * @typedef {import('../types.js').MdastDefinitionContent} MdastDefinitionContent\n *\n * @typedef Group\n * @property {Array<Element>} titles\n * @property {Array<ElementChild>} definitions\n */\n\n\n\n\n\nconst div = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('div')\nconst dt = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('dt')\nconst dd = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('dd')\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction dl(h, node) {\n  const children = node.children\n  let index = -1\n  /** @type {Array<ElementChild>} */\n  let clean = []\n  /** @type {Array<Group>} */\n  const groups = []\n  /** @type {Group} */\n  let group = {titles: [], definitions: []}\n  /** @type {ElementChild} */\n  let child\n  /** @type {Array<MdastBlockContent|MdastDefinitionContent>} */\n  let result\n\n  // Unwrap `<div>`s\n  while (++index < children.length) {\n    child = children[index]\n    clean = clean.concat(div(child) ? child.children : child)\n  }\n\n  index = -1\n\n  // Group titles and definitions.\n  while (++index < clean.length) {\n    child = clean[index]\n\n    if (dt(child)) {\n      if (dd(clean[index - 1])) {\n        groups.push(group)\n        group = {titles: [], definitions: []}\n      }\n\n      group.titles.push(child)\n    } else {\n      group.definitions.push(child)\n    }\n  }\n\n  groups.push(group)\n\n  // Create items.\n  index = -1\n  /** @type {Array<MdastListContent>} */\n  const content = []\n\n  while (++index < groups.length) {\n    result = [\n      ...handle(h, groups[index].titles),\n      ...handle(h, groups[index].definitions)\n    ]\n\n    if (result.length > 0) {\n      content.push({\n        type: 'listItem',\n        spread: result.length > 1,\n        checked: null,\n        children: result\n      })\n    }\n  }\n\n  // Create a list if there are items.\n  if (content.length > 0) {\n    return h(\n      node,\n      'list',\n      {ordered: false, start: null, spread: (0,_util_list_items_spread_js__WEBPACK_IMPORTED_MODULE_1__.listItemsSpread)(content)},\n      content\n    )\n  }\n}\n\n/**\n * @param {H} h\n * @param {Array<ElementChild>} children\n * @returns {Array<MdastBlockContent|MdastDefinitionContent>}\n */\nfunction handle(h, children) {\n  const nodes = (0,_util_wrap_list_items_js__WEBPACK_IMPORTED_MODULE_2__.wrapListItems)(h, {type: 'element', tagName: 'x', children})\n\n  if (nodes.length === 0) {\n    return []\n  }\n\n  if (nodes.length === 1) {\n    return nodes[0].children\n  }\n\n  return [\n    {\n      type: 'list',\n      ordered: false,\n      start: null,\n      spread: (0,_util_list_items_spread_js__WEBPACK_IMPORTED_MODULE_1__.listItemsSpread)(nodes),\n      children: nodes\n    }\n  ]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/dl.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/em.js":
/*!****************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/em.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   em: function() { return /* binding */ em; }\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction em(h, node) {\n  return h(node, 'emphasis', (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9lbS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSwrQkFBK0I7QUFDNUM7O0FBRTZCOztBQUU3QjtBQUNBLFVBQVU7QUFDVixXQUFXLFNBQVM7QUFDcEI7QUFDTztBQUNQLDZCQUE2Qiw0Q0FBRztBQUNoQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvZW0uanM/NDk2NCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSGFuZGxlfSBIYW5kbGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuRWxlbWVudH0gRWxlbWVudFxuICovXG5cbmltcG9ydCB7YWxsfSBmcm9tICcuLi9hbGwuanMnXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gZW0oaCwgbm9kZSkge1xuICByZXR1cm4gaChub2RlLCAnZW1waGFzaXMnLCBhbGwoaCwgbm9kZSkpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/em.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/heading.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/heading.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   heading: function() { return /* binding */ heading; }\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').MdastNode} MdastNode\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction heading(h, node) {\n  // `else` shouldn’t happen, of course…\n  /* c8 ignore next */\n  const depth = Number(node.tagName.charAt(1)) || 1\n  const wrap = h.wrapText\n\n  h.wrapText = false\n  const result = h(node, 'heading', {depth}, (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node))\n  h.wrapText = wrap\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9oZWFkaW5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1QyxhQUFhLGlDQUFpQztBQUM5Qzs7QUFFNkI7O0FBRTdCO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxxQ0FBcUMsTUFBTSxFQUFFLDRDQUFHO0FBQ2hEOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL2hlYWRpbmcuanM/YWM1NCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSGFuZGxlfSBIYW5kbGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5NZGFzdE5vZGV9IE1kYXN0Tm9kZVxuICovXG5cbmltcG9ydCB7YWxsfSBmcm9tICcuLi9hbGwuanMnXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gaGVhZGluZyhoLCBub2RlKSB7XG4gIC8vIGBlbHNlYCBzaG91bGRu4oCZdCBoYXBwZW4sIG9mIGNvdXJzZeKAplxuICAvKiBjOCBpZ25vcmUgbmV4dCAqL1xuICBjb25zdCBkZXB0aCA9IE51bWJlcihub2RlLnRhZ05hbWUuY2hhckF0KDEpKSB8fCAxXG4gIGNvbnN0IHdyYXAgPSBoLndyYXBUZXh0XG5cbiAgaC53cmFwVGV4dCA9IGZhbHNlXG4gIGNvbnN0IHJlc3VsdCA9IGgobm9kZSwgJ2hlYWRpbmcnLCB7ZGVwdGh9LCBhbGwoaCwgbm9kZSkpXG4gIGgud3JhcFRleHQgPSB3cmFwXG5cbiAgcmV0dXJuIHJlc3VsdFxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/heading.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/hr.js":
/*!****************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/hr.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hr: function() { return /* binding */ hr; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction hr(h, node) {\n  return h(node, 'thematicBreak')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9oci5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFQTtBQUNBLFVBQVU7QUFDVixXQUFXLFNBQVM7QUFDcEI7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL2hyLmpzPzliNWUiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqL1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge0VsZW1lbnR9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGhyKGgsIG5vZGUpIHtcbiAgcmV0dXJuIGgobm9kZSwgJ3RoZW1hdGljQnJlYWsnKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/hr.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/iframe.js":
/*!********************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/iframe.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   iframe: function() { return /* binding */ iframe; }\n/* harmony export */ });\n/* harmony import */ var _util_resolve_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/resolve.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/resolve.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Properties} Properties\n */\n\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction iframe(h, node) {\n  /** @type {Properties} */\n  // @ts-expect-error: `props` are defined.\n  const props = node.properties\n  const src = String(props.src || '')\n  const title = String(props.title || '')\n\n  // Only create a link if there is a title.\n  // We can’t use the content of the frame because conforming HTML parsers treat\n  // it as text, whereas legacy parsers treat it as HTML, so it will likely\n  // contain tags that will show up in text.\n  if (src && title) {\n    return {\n      type: 'link',\n      title: null,\n      url: (0,_util_resolve_js__WEBPACK_IMPORTED_MODULE_0__.resolve)(h, src),\n      children: [{type: 'text', value: (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, title)}]\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/iframe.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/img.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/img.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   img: function() { return /* binding */ img; }\n/* harmony export */ });\n/* harmony import */ var _util_resolve_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/resolve.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/resolve.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Properties} Properties\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction img(h, node) {\n  /** @type {Properties} */\n  // @ts-expect-error: `props` are defined.\n  const props = node.properties\n  return h(node, 'image', {\n    url: (0,_util_resolve_js__WEBPACK_IMPORTED_MODULE_0__.resolve)(h, String(props.src || '') || null),\n    title: props.title || null,\n    alt: props.alt || ''\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9pbWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsK0JBQStCO0FBQzVDLGFBQWEsa0NBQWtDO0FBQy9DOztBQUUwQzs7QUFFMUM7QUFDQSxVQUFVO0FBQ1YsV0FBVyxTQUFTO0FBQ3BCO0FBQ087QUFDUCxhQUFhLFlBQVk7QUFDekI7QUFDQTtBQUNBO0FBQ0EsU0FBUyx5REFBTztBQUNoQjtBQUNBO0FBQ0EsR0FBRztBQUNIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9pbWcuanM/NDA5MCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSGFuZGxlfSBIYW5kbGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5Qcm9wZXJ0aWVzfSBQcm9wZXJ0aWVzXG4gKi9cblxuaW1wb3J0IHtyZXNvbHZlfSBmcm9tICcuLi91dGlsL3Jlc29sdmUuanMnXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gaW1nKGgsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtQcm9wZXJ0aWVzfSAqL1xuICAvLyBAdHMtZXhwZWN0LWVycm9yOiBgcHJvcHNgIGFyZSBkZWZpbmVkLlxuICBjb25zdCBwcm9wcyA9IG5vZGUucHJvcGVydGllc1xuICByZXR1cm4gaChub2RlLCAnaW1hZ2UnLCB7XG4gICAgdXJsOiByZXNvbHZlKGgsIFN0cmluZyhwcm9wcy5zcmMgfHwgJycpIHx8IG51bGwpLFxuICAgIHRpdGxlOiBwcm9wcy50aXRsZSB8fCBudWxsLFxuICAgIGFsdDogcHJvcHMuYWx0IHx8ICcnXG4gIH0pXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/img.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/index.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/index.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handlers: function() { return /* binding */ handlers; }\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../all.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/* harmony import */ var _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/wrap-children.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap-children.js\");\n/* harmony import */ var _a_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./a.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/a.js\");\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./base.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/base.js\");\n/* harmony import */ var _blockquote_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./blockquote.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/blockquote.js\");\n/* harmony import */ var _br_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./br.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/br.js\");\n/* harmony import */ var _code_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./code.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/code.js\");\n/* harmony import */ var _comment_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./comment.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/comment.js\");\n/* harmony import */ var _del_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./del.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/del.js\");\n/* harmony import */ var _dl_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./dl.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/dl.js\");\n/* harmony import */ var _em_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./em.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/em.js\");\n/* harmony import */ var _heading_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./heading.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/heading.js\");\n/* harmony import */ var _hr_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./hr.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/hr.js\");\n/* harmony import */ var _iframe_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./iframe.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/iframe.js\");\n/* harmony import */ var _img_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./img.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/img.js\");\n/* harmony import */ var _inline_code_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./inline-code.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/inline-code.js\");\n/* harmony import */ var _input_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./input.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/input.js\");\n/* harmony import */ var _li_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./li.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/li.js\");\n/* harmony import */ var _list_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./list.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/list.js\");\n/* harmony import */ var _media_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./media.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/media.js\");\n/* harmony import */ var _p_js__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./p.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/p.js\");\n/* harmony import */ var _q_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./q.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/q.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./root.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/root.js\");\n/* harmony import */ var _select_js__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./select.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/select.js\");\n/* harmony import */ var _strong_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./strong.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/strong.js\");\n/* harmony import */ var _table_cell_js__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./table-cell.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/table-cell.js\");\n/* harmony import */ var _table_row_js__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./table-row.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/table-row.js\");\n/* harmony import */ var _table_js__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./table.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/table.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./text.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/text.js\");\n/* harmony import */ var _textarea_js__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./textarea.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/textarea.js\");\n/* harmony import */ var _wbr_js__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./wbr.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/wbr.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst handlers = {\n  root: _root_js__WEBPACK_IMPORTED_MODULE_0__.root,\n  text: _text_js__WEBPACK_IMPORTED_MODULE_1__.text,\n  comment: _comment_js__WEBPACK_IMPORTED_MODULE_2__.comment,\n  doctype: ignore,\n\n  applet: ignore,\n  area: ignore,\n  basefont: ignore,\n  bgsound: ignore,\n  caption: ignore,\n  col: ignore,\n  colgroup: ignore,\n  command: ignore,\n  content: ignore,\n  datalist: ignore,\n  dialog: ignore,\n  element: ignore,\n  embed: ignore,\n  frame: ignore,\n  frameset: ignore,\n  isindex: ignore,\n  keygen: ignore,\n  link: ignore,\n  math: ignore,\n  menu: ignore,\n  menuitem: ignore,\n  meta: ignore,\n  nextid: ignore,\n  noembed: ignore,\n  noframes: ignore,\n  optgroup: ignore,\n  option: ignore,\n  param: ignore,\n  script: ignore,\n  shadow: ignore,\n  source: ignore,\n  spacer: ignore,\n  style: ignore,\n  svg: ignore,\n  template: ignore,\n  title: ignore,\n  track: ignore,\n\n  abbr: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  acronym: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  bdi: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  bdo: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  big: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  blink: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  button: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  canvas: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  cite: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  data: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  details: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  dfn: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  font: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  ins: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  label: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  map: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  marquee: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  meter: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  nobr: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  noscript: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  object: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  output: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  progress: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  rb: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  rbc: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  rp: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  rt: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  rtc: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  ruby: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  slot: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  small: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  span: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  sup: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  sub: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  tbody: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  tfoot: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  thead: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  time: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n\n  address: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  article: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  aside: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  body: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  center: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  div: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  fieldset: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  figcaption: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  figure: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  form: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  footer: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  header: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  hgroup: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  html: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  legend: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  main: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  multicol: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  nav: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  picture: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  section: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n\n  a: _a_js__WEBPACK_IMPORTED_MODULE_5__.a,\n  audio: _media_js__WEBPACK_IMPORTED_MODULE_6__.media,\n  b: _strong_js__WEBPACK_IMPORTED_MODULE_7__.strong,\n  base: _base_js__WEBPACK_IMPORTED_MODULE_8__.base,\n  blockquote: _blockquote_js__WEBPACK_IMPORTED_MODULE_9__.blockquote,\n  br: _br_js__WEBPACK_IMPORTED_MODULE_10__.br,\n  code: _inline_code_js__WEBPACK_IMPORTED_MODULE_11__.inlineCode,\n  dir: _list_js__WEBPACK_IMPORTED_MODULE_12__.list,\n  dl: _dl_js__WEBPACK_IMPORTED_MODULE_13__.dl,\n  dt: _li_js__WEBPACK_IMPORTED_MODULE_14__.li,\n  dd: _li_js__WEBPACK_IMPORTED_MODULE_14__.li,\n  del: _del_js__WEBPACK_IMPORTED_MODULE_15__.del,\n  em: _em_js__WEBPACK_IMPORTED_MODULE_16__.em,\n  h1: _heading_js__WEBPACK_IMPORTED_MODULE_17__.heading,\n  h2: _heading_js__WEBPACK_IMPORTED_MODULE_17__.heading,\n  h3: _heading_js__WEBPACK_IMPORTED_MODULE_17__.heading,\n  h4: _heading_js__WEBPACK_IMPORTED_MODULE_17__.heading,\n  h5: _heading_js__WEBPACK_IMPORTED_MODULE_17__.heading,\n  h6: _heading_js__WEBPACK_IMPORTED_MODULE_17__.heading,\n  hr: _hr_js__WEBPACK_IMPORTED_MODULE_18__.hr,\n  i: _em_js__WEBPACK_IMPORTED_MODULE_16__.em,\n  iframe: _iframe_js__WEBPACK_IMPORTED_MODULE_19__.iframe,\n  img: _img_js__WEBPACK_IMPORTED_MODULE_20__.img,\n  image: _img_js__WEBPACK_IMPORTED_MODULE_20__.img,\n  input: _input_js__WEBPACK_IMPORTED_MODULE_21__.input,\n  kbd: _inline_code_js__WEBPACK_IMPORTED_MODULE_11__.inlineCode,\n  li: _li_js__WEBPACK_IMPORTED_MODULE_14__.li,\n  listing: _code_js__WEBPACK_IMPORTED_MODULE_22__.code,\n  mark: _em_js__WEBPACK_IMPORTED_MODULE_16__.em,\n  ol: _list_js__WEBPACK_IMPORTED_MODULE_12__.list,\n  p: _p_js__WEBPACK_IMPORTED_MODULE_23__.p,\n  plaintext: _code_js__WEBPACK_IMPORTED_MODULE_22__.code,\n  pre: _code_js__WEBPACK_IMPORTED_MODULE_22__.code,\n  q: _q_js__WEBPACK_IMPORTED_MODULE_24__.q,\n  s: _del_js__WEBPACK_IMPORTED_MODULE_15__.del,\n  samp: _inline_code_js__WEBPACK_IMPORTED_MODULE_11__.inlineCode,\n  select: _select_js__WEBPACK_IMPORTED_MODULE_25__.select,\n  strike: _del_js__WEBPACK_IMPORTED_MODULE_15__.del,\n  strong: _strong_js__WEBPACK_IMPORTED_MODULE_7__.strong,\n  summary: _p_js__WEBPACK_IMPORTED_MODULE_23__.p,\n  table: _table_js__WEBPACK_IMPORTED_MODULE_26__.table,\n  td: _table_cell_js__WEBPACK_IMPORTED_MODULE_27__.tableCell,\n  textarea: _textarea_js__WEBPACK_IMPORTED_MODULE_28__.textarea,\n  th: _table_cell_js__WEBPACK_IMPORTED_MODULE_27__.tableCell,\n  tr: _table_row_js__WEBPACK_IMPORTED_MODULE_29__.tableRow,\n  tt: _inline_code_js__WEBPACK_IMPORTED_MODULE_11__.inlineCode,\n  u: _em_js__WEBPACK_IMPORTED_MODULE_16__.em,\n  ul: _list_js__WEBPACK_IMPORTED_MODULE_12__.list,\n  var: _inline_code_js__WEBPACK_IMPORTED_MODULE_11__.inlineCode,\n  video: _media_js__WEBPACK_IMPORTED_MODULE_6__.media,\n  wbr: _wbr_js__WEBPACK_IMPORTED_MODULE_30__.wbr,\n  xmp: _code_js__WEBPACK_IMPORTED_MODULE_22__.code\n}\n\nfunction ignore() {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/inline-code.js":
/*!*************************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/inline-code.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inlineCode: function() { return /* binding */ inlineCode; }\n/* harmony export */ });\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-to-text */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction inlineCode(h, node) {\n  return h(node, 'inlineCode', (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__.wrapText)(h, (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_1__.toText)(node)))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9pbmxpbmUtY29kZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsK0JBQStCO0FBQzVDOztBQUV3QztBQUNLOztBQUU3QztBQUNBLFVBQVU7QUFDVixXQUFXLFNBQVM7QUFDcEI7QUFDTztBQUNQLCtCQUErQiw0REFBUSxJQUFJLHlEQUFNO0FBQ2pEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9pbmxpbmUtY29kZS5qcz9hNjQxIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKi9cblxuaW1wb3J0IHt0b1RleHR9IGZyb20gJ2hhc3QtdXRpbC10by10ZXh0J1xuaW1wb3J0IHt3cmFwVGV4dH0gZnJvbSAnLi4vdXRpbC93cmFwLXRleHQuanMnXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gaW5saW5lQ29kZShoLCBub2RlKSB7XG4gIHJldHVybiBoKG5vZGUsICdpbmxpbmVDb2RlJywgd3JhcFRleHQoaCwgdG9UZXh0KG5vZGUpKSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/inline-code.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/input.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/input.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   input: function() { return /* binding */ input; }\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(app-pages-browser)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var _util_find_selected_options_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/find-selected-options.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/find-selected-options.js\");\n/* harmony import */ var _util_own_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/own.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/own.js\");\n/* harmony import */ var _util_resolve_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/resolve.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/resolve.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Properties} Properties\n * @typedef {import('../types.js').MdastNode} MdastNode\n */\n\n\n\n\n\n\n\nconst datalist = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('datalist')\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\n// eslint-disable-next-line complexity\nfunction input(h, node) {\n  /** @type {Properties} */\n  // @ts-expect-error: `props` are defined.\n  const props = node.properties\n  let value = String(props.value || props.placeholder || '')\n  /** @type {Array<MdastNode>} */\n  const results = []\n  /** @type {Array<string>} */\n  const texts = []\n  /** @type {Array<[string, string|null]>} */\n  let values = []\n  let index = -1\n  /** @type {string} */\n  let list\n\n  if (props.disabled || props.type === 'hidden' || props.type === 'file') {\n    return\n  }\n\n  if (props.type === 'checkbox' || props.type === 'radio') {\n    return h(\n      node,\n      'text',\n      (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, h[props.checked ? 'checked' : 'unchecked'])\n    )\n  }\n\n  if (props.type === 'image') {\n    return props.alt || value\n      ? h(node, 'image', {\n          url: (0,_util_resolve_js__WEBPACK_IMPORTED_MODULE_2__.resolve)(h, String(props.src || '') || null),\n          title: (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, String(props.title || '')) || null,\n          alt: (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, String(props.alt || value))\n        })\n      : []\n  }\n\n  if (value) {\n    values = [[value, null]]\n  } else if (\n    // `list` is not supported on these types:\n    props.type !== 'password' &&\n    props.type !== 'file' &&\n    props.type !== 'submit' &&\n    props.type !== 'reset' &&\n    props.type !== 'button' &&\n    props.list\n  ) {\n    list = String(props.list).toUpperCase()\n\n    if (_util_own_js__WEBPACK_IMPORTED_MODULE_3__.own.call(h.nodeById, list) && datalist(h.nodeById[list])) {\n      values = (0,_util_find_selected_options_js__WEBPACK_IMPORTED_MODULE_4__.findSelectedOptions)(h, h.nodeById[list], props)\n    }\n  }\n\n  if (values.length === 0) {\n    return\n  }\n\n  // Hide password value.\n  if (props.type === 'password') {\n    // Passwords don’t support `list`.\n    values[0] = ['•'.repeat(values[0][0].length), null]\n  }\n\n  if (props.type === 'url' || props.type === 'email') {\n    while (++index < values.length) {\n      value = (0,_util_resolve_js__WEBPACK_IMPORTED_MODULE_2__.resolve)(h, values[index][0])\n\n      results.push(\n        h(\n          node,\n          'link',\n          {\n            title: null,\n            url: (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, props.type === 'email' ? 'mailto:' + value : value)\n          },\n          [{type: 'text', value: (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, values[index][1] || value)}]\n        )\n      )\n\n      if (index !== values.length - 1) {\n        results.push({type: 'text', value: ', '})\n      }\n    }\n\n    return results\n  }\n\n  while (++index < values.length) {\n    texts.push(\n      values[index][1]\n        ? values[index][1] + ' (' + values[index][0] + ')'\n        : values[index][0]\n    )\n  }\n\n  return h(node, 'text', (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, texts.join(', ')))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/input.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/li.js":
/*!****************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/li.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   li: function() { return /* binding */ li; }\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(app-pages-browser)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/wrap-children.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap-children.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').ElementChild} ElementChild\n * @typedef {import('../types.js').MdastNode} MdastNode\n */\n\n\n\n\nconst p = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('p')\nconst input = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('input')\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction li(h, node) {\n  const head = node.children[0]\n  /** @type {boolean|null} */\n  let checked = null\n  /** @type {ElementChild} */\n  let checkbox\n  /** @type {Element|undefined} */\n  let clone\n\n  // Check if this node starts with a checkbox.\n  if (p(head)) {\n    checkbox = head.children[0]\n\n    if (\n      input(checkbox) &&\n      checkbox.properties &&\n      (checkbox.properties.type === 'checkbox' ||\n        checkbox.properties.type === 'radio')\n    ) {\n      checked = Boolean(checkbox.properties.checked)\n      clone = {\n        ...node,\n        children: [\n          {...head, children: head.children.slice(1)},\n          ...node.children.slice(1)\n        ]\n      }\n    }\n  }\n\n  const content = (0,_util_wrap_children_js__WEBPACK_IMPORTED_MODULE_1__.wrapChildren)(h, clone || node)\n\n  return h(node, 'listItem', {spread: content.length > 1, checked}, content)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/li.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/list.js":
/*!******************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/list.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   list: function() { return /* binding */ list; }\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(app-pages-browser)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var hast_util_has_property__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-has-property */ \"(app-pages-browser)/../../node_modules/hast-util-has-property/lib/index.js\");\n/* harmony import */ var _util_list_items_spread_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/list-items-spread.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/list-items-spread.js\");\n/* harmony import */ var _util_wrap_list_items_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/wrap-list-items.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap-list-items.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n\n\n\nconst ol = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('ol')\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction list(h, node) {\n  const ordered = ol(node)\n  const children = (0,_util_wrap_list_items_js__WEBPACK_IMPORTED_MODULE_1__.wrapListItems)(h, node)\n  /** @type {number|null} */\n  let start = null\n\n  if (ordered) {\n    start = (0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_2__.hasProperty)(node, 'start')\n      ? // @ts-expect-error: `props` exist.\n        Number.parseInt(String(node.properties.start), 10)\n      : 1\n  }\n\n  return h(\n    node,\n    'list',\n    {ordered, start, spread: (0,_util_list_items_spread_js__WEBPACK_IMPORTED_MODULE_3__.listItemsSpread)(children)},\n    children\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9saXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFbUQ7QUFDRDtBQUNVO0FBQ0o7O0FBRXhELFdBQVcsb0VBQWM7O0FBRXpCO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1A7QUFDQSxtQkFBbUIsdUVBQWE7QUFDaEMsYUFBYSxhQUFhO0FBQzFCOztBQUVBO0FBQ0EsWUFBWSxtRUFBVztBQUN2QjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxLQUFLLHdCQUF3QiwyRUFBZSxXQUFXO0FBQ3ZEO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvbGlzdC5qcz9hN2Q1Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKi9cblxuaW1wb3J0IHtjb252ZXJ0RWxlbWVudH0gZnJvbSAnaGFzdC11dGlsLWlzLWVsZW1lbnQnXG5pbXBvcnQge2hhc1Byb3BlcnR5fSBmcm9tICdoYXN0LXV0aWwtaGFzLXByb3BlcnR5J1xuaW1wb3J0IHtsaXN0SXRlbXNTcHJlYWR9IGZyb20gJy4uL3V0aWwvbGlzdC1pdGVtcy1zcHJlYWQuanMnXG5pbXBvcnQge3dyYXBMaXN0SXRlbXN9IGZyb20gJy4uL3V0aWwvd3JhcC1saXN0LWl0ZW1zLmpzJ1xuXG5jb25zdCBvbCA9IGNvbnZlcnRFbGVtZW50KCdvbCcpXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gbGlzdChoLCBub2RlKSB7XG4gIGNvbnN0IG9yZGVyZWQgPSBvbChub2RlKVxuICBjb25zdCBjaGlsZHJlbiA9IHdyYXBMaXN0SXRlbXMoaCwgbm9kZSlcbiAgLyoqIEB0eXBlIHtudW1iZXJ8bnVsbH0gKi9cbiAgbGV0IHN0YXJ0ID0gbnVsbFxuXG4gIGlmIChvcmRlcmVkKSB7XG4gICAgc3RhcnQgPSBoYXNQcm9wZXJ0eShub2RlLCAnc3RhcnQnKVxuICAgICAgPyAvLyBAdHMtZXhwZWN0LWVycm9yOiBgcHJvcHNgIGV4aXN0LlxuICAgICAgICBOdW1iZXIucGFyc2VJbnQoU3RyaW5nKG5vZGUucHJvcGVydGllcy5zdGFydCksIDEwKVxuICAgICAgOiAxXG4gIH1cblxuICByZXR1cm4gaChcbiAgICBub2RlLFxuICAgICdsaXN0JyxcbiAgICB7b3JkZXJlZCwgc3RhcnQsIHNwcmVhZDogbGlzdEl0ZW1zU3ByZWFkKGNoaWxkcmVuKX0sXG4gICAgY2hpbGRyZW5cbiAgKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/list.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/media.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/media.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   media: function() { return /* binding */ media; }\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(app-pages-browser)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! mdast-util-to-string */ \"(app-pages-browser)/../../node_modules/mdast-util-to-string/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unist-util-visit */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! unist-util-visit */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit-parents/lib/index.js\");\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../all.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/* harmony import */ var _util_resolve_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/resolve.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/resolve.js\");\n/* harmony import */ var _util_wrap_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/wrap.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Properties} Properties\n * @typedef {import('../types.js').ElementChild} ElementChild\n */\n\n\n\n\n\n\n\n\nconst source = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('source')\nconst video = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('video')\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction media(h, node) {\n  let nodes = (0,_all_js__WEBPACK_IMPORTED_MODULE_1__.all)(h, node)\n  /** @type {Properties} */\n  // @ts-expect-error: `props` are defined.\n  const properties = node.properties\n  const poster = video(node) && String(properties.poster || '')\n  let src = String(properties.src || '')\n  let index = -1\n  /** @type {boolean} */\n  let linkInFallbackContent = false\n  /** @type {ElementChild} */\n  let child\n\n  ;(0,unist_util_visit__WEBPACK_IMPORTED_MODULE_2__.visit)({type: 'root', children: nodes}, 'link', findLink)\n\n  // If the content links to something, or if it’s not phrasing…\n  if (linkInFallbackContent || (0,_util_wrap_js__WEBPACK_IMPORTED_MODULE_3__.wrapNeeded)(nodes)) {\n    return nodes\n  }\n\n  // Find the source.\n  while (!src && ++index < node.children.length) {\n    child = node.children[index]\n    if (source(child)) {\n      // @ts-expect-error: `props` are defined.\n      src = String(child.properties.src || '')\n    }\n  }\n\n  // If there’s a poster defined on the video, create an image.\n  if (poster) {\n    nodes = [\n      {\n        type: 'image',\n        title: null,\n        url: (0,_util_resolve_js__WEBPACK_IMPORTED_MODULE_4__.resolve)(h, poster),\n        alt: (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_5__.toString)({children: nodes})\n      }\n    ]\n  }\n\n  // Link to the media resource.\n  return {\n    type: 'link',\n    // @ts-expect-error Types are broken.\n    title: node.properties.title || null,\n    url: (0,_util_resolve_js__WEBPACK_IMPORTED_MODULE_4__.resolve)(h, src),\n    // @ts-expect-error Assume phrasing content.\n    children: nodes\n  }\n\n  function findLink() {\n    linkInFallbackContent = true\n    return unist_util_visit__WEBPACK_IMPORTED_MODULE_6__.EXIT\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9tZWRpYS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSwrQkFBK0I7QUFDNUMsYUFBYSxrQ0FBa0M7QUFDL0MsYUFBYSxvQ0FBb0M7QUFDakQ7O0FBRW1EO0FBQ047QUFDRDtBQUNmO0FBQ2E7QUFDQTs7QUFFMUMsZUFBZSxvRUFBYztBQUM3QixjQUFjLG9FQUFjOztBQUU1QjtBQUNBLFVBQVU7QUFDVixXQUFXLFNBQVM7QUFDcEI7QUFDTztBQUNQLGNBQWMsNENBQUc7QUFDakIsYUFBYSxZQUFZO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLFNBQVM7QUFDdEI7QUFDQSxhQUFhLGNBQWM7QUFDM0I7O0FBRUEsRUFBRSx3REFBSyxFQUFFLDhCQUE4Qjs7QUFFdkM7QUFDQSwrQkFBK0IseURBQVU7QUFDekM7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEseURBQU87QUFDcEIsYUFBYSw4REFBUSxFQUFFLGdCQUFnQjtBQUN2QztBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMseURBQU87QUFDaEI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxXQUFXLGtEQUFJO0FBQ2Y7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvbWVkaWEuanM/ODBiNSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSGFuZGxlfSBIYW5kbGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5Qcm9wZXJ0aWVzfSBQcm9wZXJ0aWVzXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnRDaGlsZH0gRWxlbWVudENoaWxkXG4gKi9cblxuaW1wb3J0IHtjb252ZXJ0RWxlbWVudH0gZnJvbSAnaGFzdC11dGlsLWlzLWVsZW1lbnQnXG5pbXBvcnQge3RvU3RyaW5nfSBmcm9tICdtZGFzdC11dGlsLXRvLXN0cmluZydcbmltcG9ydCB7dmlzaXQsIEVYSVR9IGZyb20gJ3VuaXN0LXV0aWwtdmlzaXQnXG5pbXBvcnQge2FsbH0gZnJvbSAnLi4vYWxsLmpzJ1xuaW1wb3J0IHtyZXNvbHZlfSBmcm9tICcuLi91dGlsL3Jlc29sdmUuanMnXG5pbXBvcnQge3dyYXBOZWVkZWR9IGZyb20gJy4uL3V0aWwvd3JhcC5qcydcblxuY29uc3Qgc291cmNlID0gY29udmVydEVsZW1lbnQoJ3NvdXJjZScpXG5jb25zdCB2aWRlbyA9IGNvbnZlcnRFbGVtZW50KCd2aWRlbycpXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gbWVkaWEoaCwgbm9kZSkge1xuICBsZXQgbm9kZXMgPSBhbGwoaCwgbm9kZSlcbiAgLyoqIEB0eXBlIHtQcm9wZXJ0aWVzfSAqL1xuICAvLyBAdHMtZXhwZWN0LWVycm9yOiBgcHJvcHNgIGFyZSBkZWZpbmVkLlxuICBjb25zdCBwcm9wZXJ0aWVzID0gbm9kZS5wcm9wZXJ0aWVzXG4gIGNvbnN0IHBvc3RlciA9IHZpZGVvKG5vZGUpICYmIFN0cmluZyhwcm9wZXJ0aWVzLnBvc3RlciB8fCAnJylcbiAgbGV0IHNyYyA9IFN0cmluZyhwcm9wZXJ0aWVzLnNyYyB8fCAnJylcbiAgbGV0IGluZGV4ID0gLTFcbiAgLyoqIEB0eXBlIHtib29sZWFufSAqL1xuICBsZXQgbGlua0luRmFsbGJhY2tDb250ZW50ID0gZmFsc2VcbiAgLyoqIEB0eXBlIHtFbGVtZW50Q2hpbGR9ICovXG4gIGxldCBjaGlsZFxuXG4gIHZpc2l0KHt0eXBlOiAncm9vdCcsIGNoaWxkcmVuOiBub2Rlc30sICdsaW5rJywgZmluZExpbmspXG5cbiAgLy8gSWYgdGhlIGNvbnRlbnQgbGlua3MgdG8gc29tZXRoaW5nLCBvciBpZiBpdOKAmXMgbm90IHBocmFzaW5n4oCmXG4gIGlmIChsaW5rSW5GYWxsYmFja0NvbnRlbnQgfHwgd3JhcE5lZWRlZChub2RlcykpIHtcbiAgICByZXR1cm4gbm9kZXNcbiAgfVxuXG4gIC8vIEZpbmQgdGhlIHNvdXJjZS5cbiAgd2hpbGUgKCFzcmMgJiYgKytpbmRleCA8IG5vZGUuY2hpbGRyZW4ubGVuZ3RoKSB7XG4gICAgY2hpbGQgPSBub2RlLmNoaWxkcmVuW2luZGV4XVxuICAgIGlmIChzb3VyY2UoY2hpbGQpKSB7XG4gICAgICAvLyBAdHMtZXhwZWN0LWVycm9yOiBgcHJvcHNgIGFyZSBkZWZpbmVkLlxuICAgICAgc3JjID0gU3RyaW5nKGNoaWxkLnByb3BlcnRpZXMuc3JjIHx8ICcnKVxuICAgIH1cbiAgfVxuXG4gIC8vIElmIHRoZXJl4oCZcyBhIHBvc3RlciBkZWZpbmVkIG9uIHRoZSB2aWRlbywgY3JlYXRlIGFuIGltYWdlLlxuICBpZiAocG9zdGVyKSB7XG4gICAgbm9kZXMgPSBbXG4gICAgICB7XG4gICAgICAgIHR5cGU6ICdpbWFnZScsXG4gICAgICAgIHRpdGxlOiBudWxsLFxuICAgICAgICB1cmw6IHJlc29sdmUoaCwgcG9zdGVyKSxcbiAgICAgICAgYWx0OiB0b1N0cmluZyh7Y2hpbGRyZW46IG5vZGVzfSlcbiAgICAgIH1cbiAgICBdXG4gIH1cblxuICAvLyBMaW5rIHRvIHRoZSBtZWRpYSByZXNvdXJjZS5cbiAgcmV0dXJuIHtcbiAgICB0eXBlOiAnbGluaycsXG4gICAgLy8gQHRzLWV4cGVjdC1lcnJvciBUeXBlcyBhcmUgYnJva2VuLlxuICAgIHRpdGxlOiBub2RlLnByb3BlcnRpZXMudGl0bGUgfHwgbnVsbCxcbiAgICB1cmw6IHJlc29sdmUoaCwgc3JjKSxcbiAgICAvLyBAdHMtZXhwZWN0LWVycm9yIEFzc3VtZSBwaHJhc2luZyBjb250ZW50LlxuICAgIGNoaWxkcmVuOiBub2Rlc1xuICB9XG5cbiAgZnVuY3Rpb24gZmluZExpbmsoKSB7XG4gICAgbGlua0luRmFsbGJhY2tDb250ZW50ID0gdHJ1ZVxuICAgIHJldHVybiBFWElUXG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/media.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/p.js":
/*!***************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/p.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   p: function() { return /* binding */ p; }\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction p(h, node) {\n  const nodes = (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node)\n\n  if (nodes.length > 0) {\n    return h(node, 'paragraph', nodes)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9wLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFNkI7O0FBRTdCO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1AsZ0JBQWdCLDRDQUFHOztBQUVuQjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvcC5qcz80NDlmIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKi9cblxuaW1wb3J0IHthbGx9IGZyb20gJy4uL2FsbC5qcydcblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtFbGVtZW50fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwKGgsIG5vZGUpIHtcbiAgY29uc3Qgbm9kZXMgPSBhbGwoaCwgbm9kZSlcblxuICBpZiAobm9kZXMubGVuZ3RoID4gMCkge1xuICAgIHJldHVybiBoKG5vZGUsICdwYXJhZ3JhcGgnLCBub2RlcylcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/p.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/q.js":
/*!***************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/q.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   q: function() { return /* binding */ q; }\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').MdastNode} MdastNode\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction q(h, node) {\n  const expected = h.quotes[h.qNesting % h.quotes.length]\n\n  h.qNesting++\n  const contents = (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node)\n  h.qNesting--\n\n  contents.unshift({type: 'text', value: expected.charAt(0)})\n\n  contents.push({\n    type: 'text',\n    value: expected.length > 1 ? expected.charAt(1) : expected\n  })\n\n  return contents\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9xLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1QyxhQUFhLGlDQUFpQztBQUM5Qzs7QUFFNkI7O0FBRTdCO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1A7O0FBRUE7QUFDQSxtQkFBbUIsNENBQUc7QUFDdEI7O0FBRUEsb0JBQW9CLHdDQUF3Qzs7QUFFNUQ7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9xLmpzP2UyNjkiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuTWRhc3ROb2RlfSBNZGFzdE5vZGVcbiAqL1xuXG5pbXBvcnQge2FsbH0gZnJvbSAnLi4vYWxsLmpzJ1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge0VsZW1lbnR9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHEoaCwgbm9kZSkge1xuICBjb25zdCBleHBlY3RlZCA9IGgucXVvdGVzW2gucU5lc3RpbmcgJSBoLnF1b3Rlcy5sZW5ndGhdXG5cbiAgaC5xTmVzdGluZysrXG4gIGNvbnN0IGNvbnRlbnRzID0gYWxsKGgsIG5vZGUpXG4gIGgucU5lc3RpbmctLVxuXG4gIGNvbnRlbnRzLnVuc2hpZnQoe3R5cGU6ICd0ZXh0JywgdmFsdWU6IGV4cGVjdGVkLmNoYXJBdCgwKX0pXG5cbiAgY29udGVudHMucHVzaCh7XG4gICAgdHlwZTogJ3RleHQnLFxuICAgIHZhbHVlOiBleHBlY3RlZC5sZW5ndGggPiAxID8gZXhwZWN0ZWQuY2hhckF0KDEpIDogZXhwZWN0ZWRcbiAgfSlcblxuICByZXR1cm4gY29udGVudHNcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/q.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/root.js":
/*!******************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/root.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: function() { return /* binding */ root; }\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/* harmony import */ var _util_wrap_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/wrap.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Root} Root\n */\n\n\n\n\n/**\n * @type {Handle}\n * @param {Root} node\n */\nfunction root(h, node) {\n  let children = (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node)\n\n  if (h.document || (0,_util_wrap_js__WEBPACK_IMPORTED_MODULE_1__.wrapNeeded)(children)) {\n    children = (0,_util_wrap_js__WEBPACK_IMPORTED_MODULE_1__.wrap)(children)\n  }\n\n  return h(node, 'root', children)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9yb290LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSw0QkFBNEI7QUFDekM7O0FBRTZCO0FBQ21COztBQUVoRDtBQUNBLFVBQVU7QUFDVixXQUFXLE1BQU07QUFDakI7QUFDTztBQUNQLGlCQUFpQiw0Q0FBRzs7QUFFcEIsb0JBQW9CLHlEQUFVO0FBQzlCLGVBQWUsbURBQUk7QUFDbkI7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvcm9vdC5qcz84OGFjIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5Sb290fSBSb290XG4gKi9cblxuaW1wb3J0IHthbGx9IGZyb20gJy4uL2FsbC5qcydcbmltcG9ydCB7d3JhcCwgd3JhcE5lZWRlZH0gZnJvbSAnLi4vdXRpbC93cmFwLmpzJ1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge1Jvb3R9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHJvb3QoaCwgbm9kZSkge1xuICBsZXQgY2hpbGRyZW4gPSBhbGwoaCwgbm9kZSlcblxuICBpZiAoaC5kb2N1bWVudCB8fCB3cmFwTmVlZGVkKGNoaWxkcmVuKSkge1xuICAgIGNoaWxkcmVuID0gd3JhcChjaGlsZHJlbilcbiAgfVxuXG4gIHJldHVybiBoKG5vZGUsICdyb290JywgY2hpbGRyZW4pXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/root.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/select.js":
/*!********************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/select.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   select: function() { return /* binding */ select; }\n/* harmony export */ });\n/* harmony import */ var _util_find_selected_options_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/find-selected-options.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/find-selected-options.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction select(h, node) {\n  const values = (0,_util_find_selected_options_js__WEBPACK_IMPORTED_MODULE_0__.findSelectedOptions)(h, node)\n  let index = -1\n  /** @type {Array<string>} */\n  const results = []\n  /** @type {[string, string|null]} */\n  let value\n\n  while (++index < values.length) {\n    value = values[index]\n    results.push(value[1] ? value[1] + ' (' + value[0] + ')' : value[0])\n  }\n\n  if (results.length > 0) {\n    return h(node, 'text', (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, results.join(', ')))\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9zZWxlY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFb0U7QUFDdkI7O0FBRTdDO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1AsaUJBQWlCLG1GQUFtQjtBQUNwQztBQUNBLGFBQWEsZUFBZTtBQUM1QjtBQUNBLGFBQWEsdUJBQXVCO0FBQ3BDOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsMkJBQTJCLDREQUFRO0FBQ25DO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL3NlbGVjdC5qcz9hYzUxIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKi9cblxuaW1wb3J0IHtmaW5kU2VsZWN0ZWRPcHRpb25zfSBmcm9tICcuLi91dGlsL2ZpbmQtc2VsZWN0ZWQtb3B0aW9ucy5qcydcbmltcG9ydCB7d3JhcFRleHR9IGZyb20gJy4uL3V0aWwvd3JhcC10ZXh0LmpzJ1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge0VsZW1lbnR9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHNlbGVjdChoLCBub2RlKSB7XG4gIGNvbnN0IHZhbHVlcyA9IGZpbmRTZWxlY3RlZE9wdGlvbnMoaCwgbm9kZSlcbiAgbGV0IGluZGV4ID0gLTFcbiAgLyoqIEB0eXBlIHtBcnJheTxzdHJpbmc+fSAqL1xuICBjb25zdCByZXN1bHRzID0gW11cbiAgLyoqIEB0eXBlIHtbc3RyaW5nLCBzdHJpbmd8bnVsbF19ICovXG4gIGxldCB2YWx1ZVxuXG4gIHdoaWxlICgrK2luZGV4IDwgdmFsdWVzLmxlbmd0aCkge1xuICAgIHZhbHVlID0gdmFsdWVzW2luZGV4XVxuICAgIHJlc3VsdHMucHVzaCh2YWx1ZVsxXSA/IHZhbHVlWzFdICsgJyAoJyArIHZhbHVlWzBdICsgJyknIDogdmFsdWVbMF0pXG4gIH1cblxuICBpZiAocmVzdWx0cy5sZW5ndGggPiAwKSB7XG4gICAgcmV0dXJuIGgobm9kZSwgJ3RleHQnLCB3cmFwVGV4dChoLCByZXN1bHRzLmpvaW4oJywgJykpKVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/select.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/strong.js":
/*!********************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/strong.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strong: function() { return /* binding */ strong; }\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction strong(h, node) {\n  return h(node, 'strong', (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9zdHJvbmcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsK0JBQStCO0FBQzVDOztBQUU2Qjs7QUFFN0I7QUFDQSxVQUFVO0FBQ1YsV0FBVyxTQUFTO0FBQ3BCO0FBQ087QUFDUCwyQkFBMkIsNENBQUc7QUFDOUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL3N0cm9uZy5qcz8yNDA4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKi9cblxuaW1wb3J0IHthbGx9IGZyb20gJy4uL2FsbC5qcydcblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtFbGVtZW50fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBzdHJvbmcoaCwgbm9kZSkge1xuICByZXR1cm4gaChub2RlLCAnc3Ryb25nJywgYWxsKGgsIG5vZGUpKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/strong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/table-cell.js":
/*!************************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/table-cell.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tableCell: function() { return /* binding */ tableCell; }\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').MdastNode} MdastNode\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction tableCell(h, node) {\n  const wrap = h.wrapText\n\n  h.wrapText = false\n\n  const result = h(node, 'tableCell', (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node))\n\n  if (node.properties && (node.properties.rowSpan || node.properties.colSpan)) {\n    const data = result.data || (result.data = {})\n    if (node.properties.rowSpan) data.rowSpan = node.properties.rowSpan\n    if (node.properties.colSpan) data.colSpan = node.properties.colSpan\n  }\n\n  h.wrapText = wrap\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy90YWJsZS1jZWxsLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1QyxhQUFhLGlDQUFpQztBQUM5Qzs7QUFFNkI7O0FBRTdCO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1A7O0FBRUE7O0FBRUEsc0NBQXNDLDRDQUFHOztBQUV6QztBQUNBLGlEQUFpRDtBQUNqRDtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvdGFibGUtY2VsbC5qcz8wZGU0Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLk1kYXN0Tm9kZX0gTWRhc3ROb2RlXG4gKi9cblxuaW1wb3J0IHthbGx9IGZyb20gJy4uL2FsbC5qcydcblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtFbGVtZW50fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0YWJsZUNlbGwoaCwgbm9kZSkge1xuICBjb25zdCB3cmFwID0gaC53cmFwVGV4dFxuXG4gIGgud3JhcFRleHQgPSBmYWxzZVxuXG4gIGNvbnN0IHJlc3VsdCA9IGgobm9kZSwgJ3RhYmxlQ2VsbCcsIGFsbChoLCBub2RlKSlcblxuICBpZiAobm9kZS5wcm9wZXJ0aWVzICYmIChub2RlLnByb3BlcnRpZXMucm93U3BhbiB8fCBub2RlLnByb3BlcnRpZXMuY29sU3BhbikpIHtcbiAgICBjb25zdCBkYXRhID0gcmVzdWx0LmRhdGEgfHwgKHJlc3VsdC5kYXRhID0ge30pXG4gICAgaWYgKG5vZGUucHJvcGVydGllcy5yb3dTcGFuKSBkYXRhLnJvd1NwYW4gPSBub2RlLnByb3BlcnRpZXMucm93U3BhblxuICAgIGlmIChub2RlLnByb3BlcnRpZXMuY29sU3BhbikgZGF0YS5jb2xTcGFuID0gbm9kZS5wcm9wZXJ0aWVzLmNvbFNwYW5cbiAgfVxuXG4gIGgud3JhcFRleHQgPSB3cmFwXG5cbiAgcmV0dXJuIHJlc3VsdFxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/table-cell.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/table-row.js":
/*!***********************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/table-row.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tableRow: function() { return /* binding */ tableRow; }\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction tableRow(h, node) {\n  return h(node, 'tableRow', (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy90YWJsZS1yb3cuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsK0JBQStCO0FBQzVDOztBQUU2Qjs7QUFFN0I7QUFDQSxVQUFVO0FBQ1YsV0FBVyxTQUFTO0FBQ3BCO0FBQ087QUFDUCw2QkFBNkIsNENBQUc7QUFDaEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL3RhYmxlLXJvdy5qcz84ZTAxIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKi9cblxuaW1wb3J0IHthbGx9IGZyb20gJy4uL2FsbC5qcydcblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtFbGVtZW50fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0YWJsZVJvdyhoLCBub2RlKSB7XG4gIHJldHVybiBoKG5vZGUsICd0YWJsZVJvdycsIGFsbChoLCBub2RlKSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/table-row.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/table.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/table.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   table: function() { return /* binding */ table; }\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(app-pages-browser)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-to-text */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! unist-util-visit */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! unist-util-visit */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit-parents/lib/index.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../all.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').MdastNode} MdastNode\n * @typedef {import('../types.js').MdastTableContent} MdastTableContent\n * @typedef {import('../types.js').MdastRowContent} MdastRowContent\n * @typedef {import('../types.js').MdastPhrasingContent} MdastPhrasingContent\n *\n * @typedef Info\n * @property {Array<string|null>} align\n * @property {boolean} headless\n */\n\n\n\n\n\n\n\nconst thead = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('thead')\nconst tr = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('tr')\nconst cell = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)(['th', 'td'])\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction table(h, node) {\n  if (h.inTable) {\n    return h(node, 'text', (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_2__.toText)(node)))\n  }\n\n  h.inTable = true\n\n  const {headless, align} = inspect(node)\n  const rows = toRows((0,_all_js__WEBPACK_IMPORTED_MODULE_3__.all)(h, node), headless)\n  let columns = 1\n  let rowIndex = -1\n\n  while (++rowIndex < rows.length) {\n    const cells = rows[rowIndex].children\n    let cellIndex = -1\n\n    while (++cellIndex < cells.length) {\n      const cell = cells[cellIndex]\n\n      if (cell.data) {\n        const colSpan = Number.parseInt(String(cell.data.colSpan), 10) || 1\n        const rowSpan = Number.parseInt(String(cell.data.rowSpan), 10) || 1\n\n        if (colSpan > 1 || rowSpan > 1) {\n          let otherRowIndex = rowIndex - 1\n\n          while (++otherRowIndex < rowIndex + rowSpan) {\n            let colIndex = cellIndex - 1\n\n            while (++colIndex < cellIndex + colSpan) {\n              if (!rows[otherRowIndex]) {\n                // Don’t add rows that don’t exist.\n                // Browsers don’t render them either.\n                break\n              }\n\n              /** @type {Array<MdastRowContent>} */\n              const newCells = []\n\n              if (otherRowIndex !== rowIndex || colIndex !== cellIndex) {\n                newCells.push({type: 'tableCell', children: []})\n              }\n\n              rows[otherRowIndex].children.splice(colIndex, 0, ...newCells)\n            }\n          }\n        }\n\n        // Clean the data fields.\n        if ('colSpan' in cell.data) delete cell.data.colSpan\n        if ('rowSpan' in cell.data) delete cell.data.rowSpan\n        if (Object.keys(cell.data).length === 0) delete cell.data\n      }\n    }\n\n    if (cells.length > columns) columns = cells.length\n  }\n\n  // Add extra empty cells.\n  rowIndex = -1\n\n  while (++rowIndex < rows.length) {\n    const cells = rows[rowIndex].children\n    let cellIndex = cells.length - 1\n    while (++cellIndex < columns) {\n      cells.push({type: 'tableCell', children: []})\n    }\n  }\n\n  let alignIndex = align.length - 1\n  while (++alignIndex < columns) {\n    align.push(null)\n  }\n\n  h.inTable = false\n\n  return h(node, 'table', {align}, rows)\n}\n\n/**\n * Infer whether the HTML table has a head and how it aligns.\n *\n * @param {Element} node\n * @returns {Info}\n */\nfunction inspect(node) {\n  let headless = true\n  let rowIndex = 0\n  let cellIndex = 0\n  /** @type {Array<string|null>} */\n  const align = [null]\n\n  ;(0,unist_util_visit__WEBPACK_IMPORTED_MODULE_4__.visit)(node, 'element', (child) => {\n    if (child.tagName === 'table' && node !== child) {\n      return unist_util_visit__WEBPACK_IMPORTED_MODULE_5__.SKIP\n    }\n\n    // If there is a `thead`, assume there is a header row.\n    if (cell(child) && child.properties) {\n      if (!align[cellIndex]) {\n        align[cellIndex] = String(child.properties.align || '') || null\n      }\n\n      // If there is a th in the first row, assume there is a header row.\n      if (headless && rowIndex < 2 && child.tagName === 'th') {\n        headless = false\n      }\n\n      cellIndex++\n    } else if (thead(child)) {\n      headless = false\n    } else if (tr(child)) {\n      rowIndex++\n      cellIndex = 0\n    }\n  })\n\n  return {align, headless}\n}\n\n/**\n * Ensure the rows are properly structured.\n *\n * @param {Array<MdastNode>} children\n * @param {boolean} headless\n * @returns {Array<MdastTableContent>}\n */\nfunction toRows(children, headless) {\n  let index = -1\n  /** @type {Array<MdastTableContent>} */\n  const nodes = []\n  /** @type {Array<MdastRowContent>|undefined} */\n  let queue\n\n  // Add an empty header row.\n  if (headless) {\n    nodes.push({type: 'tableRow', children: []})\n  }\n\n  while (++index < children.length) {\n    const node = children[index]\n\n    if (node.type === 'tableRow') {\n      if (queue) {\n        node.children.unshift(...queue)\n        queue = undefined\n      }\n\n      nodes.push(node)\n    } else {\n      if (!queue) queue = []\n      // @ts-expect-error Assume row content.\n      queue.push(node)\n    }\n  }\n\n  if (queue) {\n    nodes[nodes.length - 1].children.push(...queue)\n  }\n\n  index = -1\n\n  while (++index < nodes.length) {\n    nodes[index].children = toCells(nodes[index].children)\n  }\n\n  return nodes\n}\n\n/**\n * Ensure the cells in a row are properly structured.\n *\n * @param {Array<MdastNode>} children\n * @returns {Array<MdastRowContent>}\n */\nfunction toCells(children) {\n  /** @type {Array<MdastRowContent>} */\n  const nodes = []\n  let index = -1\n  /** @type {MdastNode} */\n  let node\n  /** @type {Array<MdastPhrasingContent>|undefined} */\n  let queue\n\n  while (++index < children.length) {\n    node = children[index]\n\n    if (node.type === 'tableCell') {\n      if (queue) {\n        node.children.unshift(...queue)\n        queue = undefined\n      }\n\n      nodes.push(node)\n    } else {\n      if (!queue) queue = []\n      // @ts-expect-error Assume phrasing content.\n      queue.push(node)\n    }\n  }\n\n  if (queue) {\n    node = nodes[nodes.length - 1]\n\n    if (!node) {\n      node = {type: 'tableCell', children: []}\n      nodes.push(node)\n    }\n\n    node.children.push(...queue)\n  }\n\n  return nodes\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy90YWJsZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1QyxhQUFhLGlDQUFpQztBQUM5QyxhQUFhLHlDQUF5QztBQUN0RCxhQUFhLHVDQUF1QztBQUNwRCxhQUFhLDRDQUE0QztBQUN6RDtBQUNBO0FBQ0EsY0FBYyxvQkFBb0I7QUFDbEMsY0FBYyxTQUFTO0FBQ3ZCOztBQUVtRDtBQUNYO0FBQ0k7QUFDQztBQUNoQjs7QUFFN0IsY0FBYyxvRUFBYztBQUM1QixXQUFXLG9FQUFjO0FBQ3pCLGFBQWEsb0VBQWM7O0FBRTNCO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1A7QUFDQSwyQkFBMkIsNERBQVEsSUFBSSx5REFBTTtBQUM3Qzs7QUFFQTs7QUFFQSxTQUFTLGlCQUFpQjtBQUMxQixzQkFBc0IsNENBQUc7QUFDekI7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIsd0JBQXdCO0FBQ2pEOztBQUVBO0FBQ0EsK0JBQStCLGdDQUFnQztBQUMvRDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLGdDQUFnQztBQUNsRDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBLDJCQUEyQixNQUFNO0FBQ2pDOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsb0JBQW9CO0FBQ2pDOztBQUVBLEVBQUUsd0RBQUs7QUFDUDtBQUNBLGFBQWEsa0RBQUk7QUFDakI7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLE1BQU07QUFDTjtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVILFVBQVU7QUFDVjs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGtCQUFrQjtBQUM3QixXQUFXLFNBQVM7QUFDcEIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLGFBQWEsMEJBQTBCO0FBQ3ZDO0FBQ0EsYUFBYSxrQ0FBa0M7QUFDL0M7O0FBRUE7QUFDQTtBQUNBLGdCQUFnQiwrQkFBK0I7QUFDL0M7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsa0JBQWtCO0FBQzdCLGFBQWE7QUFDYjtBQUNBO0FBQ0EsYUFBYSx3QkFBd0I7QUFDckM7QUFDQTtBQUNBLGFBQWEsV0FBVztBQUN4QjtBQUNBLGFBQWEsdUNBQXVDO0FBQ3BEOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxjQUFjO0FBQ2Q7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL3RhYmxlLmpzPzEyY2MiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuTWRhc3ROb2RlfSBNZGFzdE5vZGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuTWRhc3RUYWJsZUNvbnRlbnR9IE1kYXN0VGFibGVDb250ZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLk1kYXN0Um93Q29udGVudH0gTWRhc3RSb3dDb250ZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLk1kYXN0UGhyYXNpbmdDb250ZW50fSBNZGFzdFBocmFzaW5nQ29udGVudFxuICpcbiAqIEB0eXBlZGVmIEluZm9cbiAqIEBwcm9wZXJ0eSB7QXJyYXk8c3RyaW5nfG51bGw+fSBhbGlnblxuICogQHByb3BlcnR5IHtib29sZWFufSBoZWFkbGVzc1xuICovXG5cbmltcG9ydCB7Y29udmVydEVsZW1lbnR9IGZyb20gJ2hhc3QtdXRpbC1pcy1lbGVtZW50J1xuaW1wb3J0IHt0b1RleHR9IGZyb20gJ2hhc3QtdXRpbC10by10ZXh0J1xuaW1wb3J0IHt2aXNpdCwgU0tJUH0gZnJvbSAndW5pc3QtdXRpbC12aXNpdCdcbmltcG9ydCB7d3JhcFRleHR9IGZyb20gJy4uL3V0aWwvd3JhcC10ZXh0LmpzJ1xuaW1wb3J0IHthbGx9IGZyb20gJy4uL2FsbC5qcydcblxuY29uc3QgdGhlYWQgPSBjb252ZXJ0RWxlbWVudCgndGhlYWQnKVxuY29uc3QgdHIgPSBjb252ZXJ0RWxlbWVudCgndHInKVxuY29uc3QgY2VsbCA9IGNvbnZlcnRFbGVtZW50KFsndGgnLCAndGQnXSlcblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtFbGVtZW50fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0YWJsZShoLCBub2RlKSB7XG4gIGlmIChoLmluVGFibGUpIHtcbiAgICByZXR1cm4gaChub2RlLCAndGV4dCcsIHdyYXBUZXh0KGgsIHRvVGV4dChub2RlKSkpXG4gIH1cblxuICBoLmluVGFibGUgPSB0cnVlXG5cbiAgY29uc3Qge2hlYWRsZXNzLCBhbGlnbn0gPSBpbnNwZWN0KG5vZGUpXG4gIGNvbnN0IHJvd3MgPSB0b1Jvd3MoYWxsKGgsIG5vZGUpLCBoZWFkbGVzcylcbiAgbGV0IGNvbHVtbnMgPSAxXG4gIGxldCByb3dJbmRleCA9IC0xXG5cbiAgd2hpbGUgKCsrcm93SW5kZXggPCByb3dzLmxlbmd0aCkge1xuICAgIGNvbnN0IGNlbGxzID0gcm93c1tyb3dJbmRleF0uY2hpbGRyZW5cbiAgICBsZXQgY2VsbEluZGV4ID0gLTFcblxuICAgIHdoaWxlICgrK2NlbGxJbmRleCA8IGNlbGxzLmxlbmd0aCkge1xuICAgICAgY29uc3QgY2VsbCA9IGNlbGxzW2NlbGxJbmRleF1cblxuICAgICAgaWYgKGNlbGwuZGF0YSkge1xuICAgICAgICBjb25zdCBjb2xTcGFuID0gTnVtYmVyLnBhcnNlSW50KFN0cmluZyhjZWxsLmRhdGEuY29sU3BhbiksIDEwKSB8fCAxXG4gICAgICAgIGNvbnN0IHJvd1NwYW4gPSBOdW1iZXIucGFyc2VJbnQoU3RyaW5nKGNlbGwuZGF0YS5yb3dTcGFuKSwgMTApIHx8IDFcblxuICAgICAgICBpZiAoY29sU3BhbiA+IDEgfHwgcm93U3BhbiA+IDEpIHtcbiAgICAgICAgICBsZXQgb3RoZXJSb3dJbmRleCA9IHJvd0luZGV4IC0gMVxuXG4gICAgICAgICAgd2hpbGUgKCsrb3RoZXJSb3dJbmRleCA8IHJvd0luZGV4ICsgcm93U3Bhbikge1xuICAgICAgICAgICAgbGV0IGNvbEluZGV4ID0gY2VsbEluZGV4IC0gMVxuXG4gICAgICAgICAgICB3aGlsZSAoKytjb2xJbmRleCA8IGNlbGxJbmRleCArIGNvbFNwYW4pIHtcbiAgICAgICAgICAgICAgaWYgKCFyb3dzW290aGVyUm93SW5kZXhdKSB7XG4gICAgICAgICAgICAgICAgLy8gRG9u4oCZdCBhZGQgcm93cyB0aGF0IGRvbuKAmXQgZXhpc3QuXG4gICAgICAgICAgICAgICAgLy8gQnJvd3NlcnMgZG9u4oCZdCByZW5kZXIgdGhlbSBlaXRoZXIuXG4gICAgICAgICAgICAgICAgYnJlYWtcbiAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgIC8qKiBAdHlwZSB7QXJyYXk8TWRhc3RSb3dDb250ZW50Pn0gKi9cbiAgICAgICAgICAgICAgY29uc3QgbmV3Q2VsbHMgPSBbXVxuXG4gICAgICAgICAgICAgIGlmIChvdGhlclJvd0luZGV4ICE9PSByb3dJbmRleCB8fCBjb2xJbmRleCAhPT0gY2VsbEluZGV4KSB7XG4gICAgICAgICAgICAgICAgbmV3Q2VsbHMucHVzaCh7dHlwZTogJ3RhYmxlQ2VsbCcsIGNoaWxkcmVuOiBbXX0pXG4gICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICByb3dzW290aGVyUm93SW5kZXhdLmNoaWxkcmVuLnNwbGljZShjb2xJbmRleCwgMCwgLi4ubmV3Q2VsbHMpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgLy8gQ2xlYW4gdGhlIGRhdGEgZmllbGRzLlxuICAgICAgICBpZiAoJ2NvbFNwYW4nIGluIGNlbGwuZGF0YSkgZGVsZXRlIGNlbGwuZGF0YS5jb2xTcGFuXG4gICAgICAgIGlmICgncm93U3BhbicgaW4gY2VsbC5kYXRhKSBkZWxldGUgY2VsbC5kYXRhLnJvd1NwYW5cbiAgICAgICAgaWYgKE9iamVjdC5rZXlzKGNlbGwuZGF0YSkubGVuZ3RoID09PSAwKSBkZWxldGUgY2VsbC5kYXRhXG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKGNlbGxzLmxlbmd0aCA+IGNvbHVtbnMpIGNvbHVtbnMgPSBjZWxscy5sZW5ndGhcbiAgfVxuXG4gIC8vIEFkZCBleHRyYSBlbXB0eSBjZWxscy5cbiAgcm93SW5kZXggPSAtMVxuXG4gIHdoaWxlICgrK3Jvd0luZGV4IDwgcm93cy5sZW5ndGgpIHtcbiAgICBjb25zdCBjZWxscyA9IHJvd3Nbcm93SW5kZXhdLmNoaWxkcmVuXG4gICAgbGV0IGNlbGxJbmRleCA9IGNlbGxzLmxlbmd0aCAtIDFcbiAgICB3aGlsZSAoKytjZWxsSW5kZXggPCBjb2x1bW5zKSB7XG4gICAgICBjZWxscy5wdXNoKHt0eXBlOiAndGFibGVDZWxsJywgY2hpbGRyZW46IFtdfSlcbiAgICB9XG4gIH1cblxuICBsZXQgYWxpZ25JbmRleCA9IGFsaWduLmxlbmd0aCAtIDFcbiAgd2hpbGUgKCsrYWxpZ25JbmRleCA8IGNvbHVtbnMpIHtcbiAgICBhbGlnbi5wdXNoKG51bGwpXG4gIH1cblxuICBoLmluVGFibGUgPSBmYWxzZVxuXG4gIHJldHVybiBoKG5vZGUsICd0YWJsZScsIHthbGlnbn0sIHJvd3MpXG59XG5cbi8qKlxuICogSW5mZXIgd2hldGhlciB0aGUgSFRNTCB0YWJsZSBoYXMgYSBoZWFkIGFuZCBob3cgaXQgYWxpZ25zLlxuICpcbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICogQHJldHVybnMge0luZm99XG4gKi9cbmZ1bmN0aW9uIGluc3BlY3Qobm9kZSkge1xuICBsZXQgaGVhZGxlc3MgPSB0cnVlXG4gIGxldCByb3dJbmRleCA9IDBcbiAgbGV0IGNlbGxJbmRleCA9IDBcbiAgLyoqIEB0eXBlIHtBcnJheTxzdHJpbmd8bnVsbD59ICovXG4gIGNvbnN0IGFsaWduID0gW251bGxdXG5cbiAgdmlzaXQobm9kZSwgJ2VsZW1lbnQnLCAoY2hpbGQpID0+IHtcbiAgICBpZiAoY2hpbGQudGFnTmFtZSA9PT0gJ3RhYmxlJyAmJiBub2RlICE9PSBjaGlsZCkge1xuICAgICAgcmV0dXJuIFNLSVBcbiAgICB9XG5cbiAgICAvLyBJZiB0aGVyZSBpcyBhIGB0aGVhZGAsIGFzc3VtZSB0aGVyZSBpcyBhIGhlYWRlciByb3cuXG4gICAgaWYgKGNlbGwoY2hpbGQpICYmIGNoaWxkLnByb3BlcnRpZXMpIHtcbiAgICAgIGlmICghYWxpZ25bY2VsbEluZGV4XSkge1xuICAgICAgICBhbGlnbltjZWxsSW5kZXhdID0gU3RyaW5nKGNoaWxkLnByb3BlcnRpZXMuYWxpZ24gfHwgJycpIHx8IG51bGxcbiAgICAgIH1cblxuICAgICAgLy8gSWYgdGhlcmUgaXMgYSB0aCBpbiB0aGUgZmlyc3Qgcm93LCBhc3N1bWUgdGhlcmUgaXMgYSBoZWFkZXIgcm93LlxuICAgICAgaWYgKGhlYWRsZXNzICYmIHJvd0luZGV4IDwgMiAmJiBjaGlsZC50YWdOYW1lID09PSAndGgnKSB7XG4gICAgICAgIGhlYWRsZXNzID0gZmFsc2VcbiAgICAgIH1cblxuICAgICAgY2VsbEluZGV4KytcbiAgICB9IGVsc2UgaWYgKHRoZWFkKGNoaWxkKSkge1xuICAgICAgaGVhZGxlc3MgPSBmYWxzZVxuICAgIH0gZWxzZSBpZiAodHIoY2hpbGQpKSB7XG4gICAgICByb3dJbmRleCsrXG4gICAgICBjZWxsSW5kZXggPSAwXG4gICAgfVxuICB9KVxuXG4gIHJldHVybiB7YWxpZ24sIGhlYWRsZXNzfVxufVxuXG4vKipcbiAqIEVuc3VyZSB0aGUgcm93cyBhcmUgcHJvcGVybHkgc3RydWN0dXJlZC5cbiAqXG4gKiBAcGFyYW0ge0FycmF5PE1kYXN0Tm9kZT59IGNoaWxkcmVuXG4gKiBAcGFyYW0ge2Jvb2xlYW59IGhlYWRsZXNzXG4gKiBAcmV0dXJucyB7QXJyYXk8TWRhc3RUYWJsZUNvbnRlbnQ+fVxuICovXG5mdW5jdGlvbiB0b1Jvd3MoY2hpbGRyZW4sIGhlYWRsZXNzKSB7XG4gIGxldCBpbmRleCA9IC0xXG4gIC8qKiBAdHlwZSB7QXJyYXk8TWRhc3RUYWJsZUNvbnRlbnQ+fSAqL1xuICBjb25zdCBub2RlcyA9IFtdXG4gIC8qKiBAdHlwZSB7QXJyYXk8TWRhc3RSb3dDb250ZW50Pnx1bmRlZmluZWR9ICovXG4gIGxldCBxdWV1ZVxuXG4gIC8vIEFkZCBhbiBlbXB0eSBoZWFkZXIgcm93LlxuICBpZiAoaGVhZGxlc3MpIHtcbiAgICBub2Rlcy5wdXNoKHt0eXBlOiAndGFibGVSb3cnLCBjaGlsZHJlbjogW119KVxuICB9XG5cbiAgd2hpbGUgKCsraW5kZXggPCBjaGlsZHJlbi5sZW5ndGgpIHtcbiAgICBjb25zdCBub2RlID0gY2hpbGRyZW5baW5kZXhdXG5cbiAgICBpZiAobm9kZS50eXBlID09PSAndGFibGVSb3cnKSB7XG4gICAgICBpZiAocXVldWUpIHtcbiAgICAgICAgbm9kZS5jaGlsZHJlbi51bnNoaWZ0KC4uLnF1ZXVlKVxuICAgICAgICBxdWV1ZSA9IHVuZGVmaW5lZFxuICAgICAgfVxuXG4gICAgICBub2Rlcy5wdXNoKG5vZGUpXG4gICAgfSBlbHNlIHtcbiAgICAgIGlmICghcXVldWUpIHF1ZXVlID0gW11cbiAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgQXNzdW1lIHJvdyBjb250ZW50LlxuICAgICAgcXVldWUucHVzaChub2RlKVxuICAgIH1cbiAgfVxuXG4gIGlmIChxdWV1ZSkge1xuICAgIG5vZGVzW25vZGVzLmxlbmd0aCAtIDFdLmNoaWxkcmVuLnB1c2goLi4ucXVldWUpXG4gIH1cblxuICBpbmRleCA9IC0xXG5cbiAgd2hpbGUgKCsraW5kZXggPCBub2Rlcy5sZW5ndGgpIHtcbiAgICBub2Rlc1tpbmRleF0uY2hpbGRyZW4gPSB0b0NlbGxzKG5vZGVzW2luZGV4XS5jaGlsZHJlbilcbiAgfVxuXG4gIHJldHVybiBub2Rlc1xufVxuXG4vKipcbiAqIEVuc3VyZSB0aGUgY2VsbHMgaW4gYSByb3cgYXJlIHByb3Blcmx5IHN0cnVjdHVyZWQuXG4gKlxuICogQHBhcmFtIHtBcnJheTxNZGFzdE5vZGU+fSBjaGlsZHJlblxuICogQHJldHVybnMge0FycmF5PE1kYXN0Um93Q29udGVudD59XG4gKi9cbmZ1bmN0aW9uIHRvQ2VsbHMoY2hpbGRyZW4pIHtcbiAgLyoqIEB0eXBlIHtBcnJheTxNZGFzdFJvd0NvbnRlbnQ+fSAqL1xuICBjb25zdCBub2RlcyA9IFtdXG4gIGxldCBpbmRleCA9IC0xXG4gIC8qKiBAdHlwZSB7TWRhc3ROb2RlfSAqL1xuICBsZXQgbm9kZVxuICAvKiogQHR5cGUge0FycmF5PE1kYXN0UGhyYXNpbmdDb250ZW50Pnx1bmRlZmluZWR9ICovXG4gIGxldCBxdWV1ZVxuXG4gIHdoaWxlICgrK2luZGV4IDwgY2hpbGRyZW4ubGVuZ3RoKSB7XG4gICAgbm9kZSA9IGNoaWxkcmVuW2luZGV4XVxuXG4gICAgaWYgKG5vZGUudHlwZSA9PT0gJ3RhYmxlQ2VsbCcpIHtcbiAgICAgIGlmIChxdWV1ZSkge1xuICAgICAgICBub2RlLmNoaWxkcmVuLnVuc2hpZnQoLi4ucXVldWUpXG4gICAgICAgIHF1ZXVlID0gdW5kZWZpbmVkXG4gICAgICB9XG5cbiAgICAgIG5vZGVzLnB1c2gobm9kZSlcbiAgICB9IGVsc2Uge1xuICAgICAgaWYgKCFxdWV1ZSkgcXVldWUgPSBbXVxuICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvciBBc3N1bWUgcGhyYXNpbmcgY29udGVudC5cbiAgICAgIHF1ZXVlLnB1c2gobm9kZSlcbiAgICB9XG4gIH1cblxuICBpZiAocXVldWUpIHtcbiAgICBub2RlID0gbm9kZXNbbm9kZXMubGVuZ3RoIC0gMV1cblxuICAgIGlmICghbm9kZSkge1xuICAgICAgbm9kZSA9IHt0eXBlOiAndGFibGVDZWxsJywgY2hpbGRyZW46IFtdfVxuICAgICAgbm9kZXMucHVzaChub2RlKVxuICAgIH1cblxuICAgIG5vZGUuY2hpbGRyZW4ucHVzaCguLi5xdWV1ZSlcbiAgfVxuXG4gIHJldHVybiBub2Rlc1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/table.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/text.js":
/*!******************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/text.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: function() { return /* binding */ text; }\n/* harmony export */ });\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Text} Text\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Text} node\n */\nfunction text(h, node) {\n  return h(node, 'text', (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__.wrapText)(h, node.value))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy90ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLDRCQUE0QjtBQUN6Qzs7QUFFNkM7O0FBRTdDO0FBQ0EsVUFBVTtBQUNWLFdBQVcsTUFBTTtBQUNqQjtBQUNPO0FBQ1AseUJBQXlCLDREQUFRO0FBQ2pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy90ZXh0LmpzP2I5YmUiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlRleHR9IFRleHRcbiAqL1xuXG5pbXBvcnQge3dyYXBUZXh0fSBmcm9tICcuLi91dGlsL3dyYXAtdGV4dC5qcydcblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtUZXh0fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0ZXh0KGgsIG5vZGUpIHtcbiAgcmV0dXJuIGgobm9kZSwgJ3RleHQnLCB3cmFwVGV4dChoLCBub2RlLnZhbHVlKSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/text.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/textarea.js":
/*!**********************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/textarea.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   textarea: function() { return /* binding */ textarea; }\n/* harmony export */ });\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-to-text */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction textarea(h, node) {\n  return h(node, 'text', (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__.wrapText)(h, (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_1__.toText)(node)))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy90ZXh0YXJlYS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsK0JBQStCO0FBQzVDOztBQUV3QztBQUNLOztBQUU3QztBQUNBLFVBQVU7QUFDVixXQUFXLFNBQVM7QUFDcEI7QUFDTztBQUNQLHlCQUF5Qiw0REFBUSxJQUFJLHlEQUFNO0FBQzNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy90ZXh0YXJlYS5qcz9mZjcwIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKi9cblxuaW1wb3J0IHt0b1RleHR9IGZyb20gJ2hhc3QtdXRpbC10by10ZXh0J1xuaW1wb3J0IHt3cmFwVGV4dH0gZnJvbSAnLi4vdXRpbC93cmFwLXRleHQuanMnXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gdGV4dGFyZWEoaCwgbm9kZSkge1xuICByZXR1cm4gaChub2RlLCAndGV4dCcsIHdyYXBUZXh0KGgsIHRvVGV4dChub2RlKSkpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/textarea.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/wbr.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/wbr.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wbr: function() { return /* binding */ wbr; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction wbr(h, node) {\n  return h(node, 'text', '\\u200B')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy93YnIuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSwrQkFBK0I7QUFDNUM7O0FBRUE7QUFDQSxVQUFVO0FBQ1YsV0FBVyxTQUFTO0FBQ3BCO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy93YnIuanM/MGIxZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSGFuZGxlfSBIYW5kbGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuRWxlbWVudH0gRWxlbWVudFxuICovXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gd2JyKGgsIG5vZGUpIHtcbiAgcmV0dXJuIGgobm9kZSwgJ3RleHQnLCAnXFx1MjAwQicpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/wbr.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/index.js":
/*!**********************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/index.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   all: function() { return /* reexport safe */ _all_js__WEBPACK_IMPORTED_MODULE_1__.all; },\n/* harmony export */   defaultHandlers: function() { return /* reexport safe */ _handlers_index_js__WEBPACK_IMPORTED_MODULE_3__.handlers; },\n/* harmony export */   one: function() { return /* reexport safe */ _one_js__WEBPACK_IMPORTED_MODULE_0__.one; },\n/* harmony export */   toMdast: function() { return /* binding */ toMdast; }\n/* harmony export */ });\n/* harmony import */ var rehype_minify_whitespace__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rehype-minify-whitespace */ \"(app-pages-browser)/../../node_modules/rehype-minify-whitespace/index.js\");\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unist-util-is */ \"(app-pages-browser)/../../node_modules/unist-util-is/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! unist-util-visit */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var _one_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./one.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/one.js\");\n/* harmony import */ var _handlers_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./handlers/index.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/index.js\");\n/* harmony import */ var _util_own_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./util/own.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/own.js\");\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./all.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('./types.js').Node} Node\n * @typedef {import('./types.js').Element} Element\n * @typedef {import('./types.js').Options} Options\n * @typedef {import('./types.js').Properties} Properties\n * @typedef {import('./types.js').H} H\n * @typedef {import('./types.js').HWithoutProps} HWithoutProps\n * @typedef {import('./types.js').HWithProps} HWithProps\n * @typedef {import('./types.js').MdastNode} MdastNode\n * @typedef {import('./types.js').MdastRoot} MdastRoot\n */\n\n\n\n\n\n\n\n\n\n\n\nconst block = (0,unist_util_is__WEBPACK_IMPORTED_MODULE_2__.convert)(['heading', 'paragraph', 'root'])\n\n/**\n * Transform hast to mdast.\n *\n * @param {Node} tree\n *   Tree (hast).\n * @param {Options} [options]\n *   Configuration (optional).\n */\nfunction toMdast(tree, options = {}) {\n  /** @type {Record<string, Element>} */\n  const byId = {}\n  /** @type {MdastNode|MdastRoot} */\n  let mdast\n\n  /**\n   * @type {H}\n   */\n  const h = Object.assign(\n    /**\n     * @type {HWithProps & HWithoutProps}\n     */\n    (\n      /**\n       * @param {Node} node\n       * @param {string} type\n       * @param {Properties|string|Array<Node>} [props]\n       * @param {string|Array<Node>} [children]\n       */\n      (node, type, props, children) => {\n        /** @type {Properties|undefined} */\n        let properties\n\n        if (typeof props === 'string' || Array.isArray(props)) {\n          children = props\n          properties = {}\n        } else {\n          properties = props\n        }\n\n        /** @type {Node} */\n        // @ts-expect-error Assume valid `type` and `children`/`value`.\n        const result = {type, ...properties}\n\n        if (typeof children === 'string') {\n          // @ts-expect-error: Looks like a literal.\n          result.value = children\n        } else if (children) {\n          // @ts-expect-error: Looks like a parent.\n          result.children = children\n        }\n\n        if (node.position) {\n          result.position = node.position\n        }\n\n        return result\n      }\n    ),\n    {\n      nodeById: byId,\n      baseFound: false,\n      inTable: false,\n      wrapText: true,\n      /** @type {string|null} */\n      frozenBaseUrl: null,\n      qNesting: 0,\n      handlers: options.handlers\n        ? {..._handlers_index_js__WEBPACK_IMPORTED_MODULE_3__.handlers, ...options.handlers}\n        : _handlers_index_js__WEBPACK_IMPORTED_MODULE_3__.handlers,\n      document: options.document,\n      checked: options.checked || '[x]',\n      unchecked: options.unchecked || '[ ]',\n      quotes: options.quotes || ['\"']\n    }\n  )\n\n  ;(0,unist_util_visit__WEBPACK_IMPORTED_MODULE_4__.visit)(tree, 'element', (node) => {\n    const id =\n      node.properties &&\n      'id' in node.properties &&\n      String(node.properties.id).toUpperCase()\n\n    if (id && !_util_own_js__WEBPACK_IMPORTED_MODULE_5__.own.call(byId, id)) {\n      byId[id] = node\n    }\n  })\n\n  // @ts-expect-error: does return a transformer, that does accept any node.\n  ;(0,rehype_minify_whitespace__WEBPACK_IMPORTED_MODULE_6__[\"default\"])({newlines: options.newlines === true})(tree)\n\n  const result = (0,_one_js__WEBPACK_IMPORTED_MODULE_0__.one)(h, tree, undefined)\n\n  if (!result) {\n    mdast = {type: 'root', children: []}\n  } else if (Array.isArray(result)) {\n    mdast = {type: 'root', children: result}\n  } else {\n    mdast = result\n  }\n\n  (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_4__.visit)(mdast, 'text', ontext)\n\n  return mdast\n\n  /**\n   * Collapse text nodes, and fix whitespace.\n   * Most of this is taken care of by `rehype-minify-whitespace`, but\n   * we’re generating some whitespace too, and some nodes are in the end\n   * ignored.\n   * So clean up.\n   *\n   * @type {import('unist-util-visit/complex-types').BuildVisitor<MdastRoot, 'text'>}\n   */\n  function ontext(node, index, parent) {\n    /* c8 ignore next 3 */\n    if (index === null || !parent) {\n      return\n    }\n\n    const previous = parent.children[index - 1]\n\n    if (previous && previous.type === node.type) {\n      previous.value += node.value\n      parent.children.splice(index, 1)\n\n      if (previous.position && node.position) {\n        previous.position.end = node.position.end\n      }\n\n      // Iterate over the previous node again, to handle its total value.\n      return index - 1\n    }\n\n    node.value = node.value.replace(/[\\t ]*(\\r?\\n|\\r)[\\t ]*/, '$1')\n\n    // We don’t care about other phrasing nodes in between (e.g., `[ asd ]()`),\n    // as there the whitespace matters.\n    if (parent && block(parent)) {\n      if (!index) {\n        node.value = node.value.replace(/^[\\t ]+/, '')\n      }\n\n      if (index === parent.children.length - 1) {\n        node.value = node.value.replace(/[\\t ]+$/, '')\n      }\n    }\n\n    if (!node.value) {\n      parent.children.splice(index, 1)\n      return index\n    }\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/one.js":
/*!********************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/one.js ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   one: function() { return /* binding */ one; }\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./all.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/* harmony import */ var _util_own_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/own.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/own.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/wrap-text.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('./types.js').H} H\n * @typedef {import('./types.js').Node} Node\n * @typedef {import('./types.js').Parent} Parent\n * @typedef {import('./types.js').Handle} Handle\n * @typedef {import('./types.js').MdastNode} MdastNode\n */\n\n\n\n\n\n/**\n * @param {H} h\n * @param {Node} node\n * @param {Parent|undefined} parent\n * @returns {MdastNode|Array<MdastNode>|void}\n */\nfunction one(h, node, parent) {\n  /** @type {Handle|undefined} */\n  let fn\n\n  if (node.type === 'element') {\n    if (node.properties && node.properties.dataMdast === 'ignore') {\n      return\n    }\n\n    if (_util_own_js__WEBPACK_IMPORTED_MODULE_0__.own.call(h.handlers, node.tagName)) {\n      fn = h.handlers[node.tagName]\n    }\n  } else if (_util_own_js__WEBPACK_IMPORTED_MODULE_0__.own.call(h.handlers, node.type)) {\n    fn = h.handlers[node.type]\n  }\n\n  if (typeof fn === 'function') {\n    return fn(h, node, parent)\n  }\n\n  return unknown(h, node)\n}\n\n/**\n * @type {Handle}\n * @param {Node} node\n */\nfunction unknown(h, node) {\n  // @ts-expect-error: Looks like a literal.\n  if (typeof node.value === 'string') {\n    // @ts-expect-error: Looks like a literal.\n    return h(node, 'text', (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, node.value))\n  }\n\n  return (0,_all_js__WEBPACK_IMPORTED_MODULE_2__.all)(h, node)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/one.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/find-selected-options.js":
/*!*******************************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/util/find-selected-options.js ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findSelectedOptions: function() { return /* binding */ findSelectedOptions; }\n/* harmony export */ });\n/* harmony import */ var hast_util_has_property__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-has-property */ \"(app-pages-browser)/../../node_modules/hast-util-has-property/lib/index.js\");\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(app-pages-browser)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-to-text */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var _wrap_text_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./wrap-text.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').H} H\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Child} Child\n * @typedef {import('../types.js').Properties} Properties\n */\n\n\n\n\n\n\nconst option = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('option')\n\n/**\n * @param {H} h\n * @param {Element} node\n * @param {Properties} [properties]\n * @returns {Array<[string, string|null]>}\n */\nfunction findSelectedOptions(h, node, properties) {\n  /** @type {Properties} */\n  // @ts-expect-error: `props` exist.\n  const props = properties || node.properties\n  let options = findOptions(node)\n  const size =\n    Math.min(Number.parseInt(String(props.size), 10), 0) ||\n    (props.multiple ? 4 : 1)\n  let index = -1\n  /** @type {Array<Element>} */\n  const selectedOptions = []\n  /** @type {Array<[string, string|null]>} */\n  const values = []\n\n  while (++index < options.length) {\n    if ((0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_1__.hasProperty)(options[index], 'selected')) {\n      selectedOptions.push(options[index])\n    }\n  }\n\n  const list = selectedOptions.length > 0 ? selectedOptions : options\n  options = list.slice(0, size)\n  index = -1\n\n  while (++index < options.length) {\n    const option = options[index]\n    const content = (0,_wrap_text_js__WEBPACK_IMPORTED_MODULE_2__.wrapText)(h, (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__.toText)(option))\n    /** @type {Properties} */\n    // @ts-expect-error: `props` exist.\n    const props = option.properties\n    const label = content || String(props.label || '')\n    const value = String(props.value || '') || content\n    values.push([value, label === value ? null : label])\n  }\n\n  return values\n}\n\n/**\n * @param {Parent} node\n */\nfunction findOptions(node) {\n  const children = node.children\n  let index = -1\n  /** @type {Array<Element>} */\n  let results = []\n  /** @type {Child} */\n  let child\n\n  while (++index < children.length) {\n    child = children[index]\n\n    // @ts-expect-error Looks like a parent.\n    if (Array.isArray(child.children)) {\n      // @ts-expect-error Looks like a parent.\n      results = results.concat(findOptions(child))\n    }\n\n    if (option(child) && !(0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_1__.hasProperty)(child, 'disabled')) {\n      results.push(child)\n    }\n  }\n\n  return results\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/find-selected-options.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/list-items-spread.js":
/*!***************************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/util/list-items-spread.js ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   listItemsSpread: function() { return /* binding */ listItemsSpread; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').MdastListContent} MdastListContent\n */\n\n/**\n * @param {Array<MdastListContent>} children\n * @returns {boolean}\n */\nfunction listItemsSpread(children) {\n  let index = -1\n\n  if (children.length > 1) {\n    while (++index < children.length) {\n      if (children[index].spread) {\n        return true\n      }\n    }\n  }\n\n  return false\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi91dGlsL2xpc3QtaXRlbXMtc3ByZWFkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsd0NBQXdDO0FBQ3JEOztBQUVBO0FBQ0EsV0FBVyx5QkFBeUI7QUFDcEMsYUFBYTtBQUNiO0FBQ087QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi91dGlsL2xpc3QtaXRlbXMtc3ByZWFkLmpzPzk5M2UiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLk1kYXN0TGlzdENvbnRlbnR9IE1kYXN0TGlzdENvbnRlbnRcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7QXJyYXk8TWRhc3RMaXN0Q29udGVudD59IGNoaWxkcmVuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGxpc3RJdGVtc1NwcmVhZChjaGlsZHJlbikge1xuICBsZXQgaW5kZXggPSAtMVxuXG4gIGlmIChjaGlsZHJlbi5sZW5ndGggPiAxKSB7XG4gICAgd2hpbGUgKCsraW5kZXggPCBjaGlsZHJlbi5sZW5ndGgpIHtcbiAgICAgIGlmIChjaGlsZHJlbltpbmRleF0uc3ByZWFkKSB7XG4gICAgICAgIHJldHVybiB0cnVlXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIGZhbHNlXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/list-items-spread.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/own.js":
/*!*************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/util/own.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   own: function() { return /* binding */ own; }\n/* harmony export */ });\nconst own = {}.hasOwnProperty\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi91dGlsL293bi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU8sY0FBYyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvdXRpbC9vd24uanM/N2IwYiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3Qgb3duID0ge30uaGFzT3duUHJvcGVydHlcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/own.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/resolve.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/util/resolve.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolve: function() { return /* binding */ resolve; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').H} H\n */\n\n/**\n * @param {H} h\n * @param {string|null|undefined} url\n * @returns {string}\n */\nfunction resolve(h, url) {\n  if (url === null || url === undefined) {\n    return ''\n  }\n\n  if (h.frozenBaseUrl) {\n    return String(new URL(url, h.frozenBaseUrl))\n  }\n\n  return url\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi91dGlsL3Jlc29sdmUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSx5QkFBeUI7QUFDdEM7O0FBRUE7QUFDQSxXQUFXLEdBQUc7QUFDZCxXQUFXLHVCQUF1QjtBQUNsQyxhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvdXRpbC9yZXNvbHZlLmpzPzE2NDMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkh9IEhcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7SH0gaFxuICogQHBhcmFtIHtzdHJpbmd8bnVsbHx1bmRlZmluZWR9IHVybFxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHJlc29sdmUoaCwgdXJsKSB7XG4gIGlmICh1cmwgPT09IG51bGwgfHwgdXJsID09PSB1bmRlZmluZWQpIHtcbiAgICByZXR1cm4gJydcbiAgfVxuXG4gIGlmIChoLmZyb3plbkJhc2VVcmwpIHtcbiAgICByZXR1cm4gU3RyaW5nKG5ldyBVUkwodXJsLCBoLmZyb3plbkJhc2VVcmwpKVxuICB9XG5cbiAgcmV0dXJuIHVybFxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/resolve.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap-children.js":
/*!***********************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/util/wrap-children.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wrapChildren: function() { return /* binding */ wrapChildren; }\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../all.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/* harmony import */ var _wrap_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./wrap.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap.js\");\n/**\n * @typedef {import('../types.js').H} H\n * @typedef {import('../types.js').Node} Node\n * @typedef {import('../types.js').MdastNode} MdastNode\n */\n\n\n\n\n/**\n * @param {H} h\n * @param {Node} node\n * @returns {Array<MdastNode>}\n */\nfunction wrapChildren(h, node) {\n  return (0,_wrap_js__WEBPACK_IMPORTED_MODULE_0__.wrap)((0,_all_js__WEBPACK_IMPORTED_MODULE_1__.all)(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi91dGlsL3dyYXAtY2hpbGRyZW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQSxhQUFhLHlCQUF5QjtBQUN0QyxhQUFhLDRCQUE0QjtBQUN6QyxhQUFhLGlDQUFpQztBQUM5Qzs7QUFFNkI7QUFDQzs7QUFFOUI7QUFDQSxXQUFXLEdBQUc7QUFDZCxXQUFXLE1BQU07QUFDakIsYUFBYTtBQUNiO0FBQ087QUFDUCxTQUFTLDhDQUFJLENBQUMsNENBQUc7QUFDakIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL3V0aWwvd3JhcC1jaGlsZHJlbi5qcz9mMzg1Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IfSBIXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLk5vZGV9IE5vZGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuTWRhc3ROb2RlfSBNZGFzdE5vZGVcbiAqL1xuXG5pbXBvcnQge2FsbH0gZnJvbSAnLi4vYWxsLmpzJ1xuaW1wb3J0IHt3cmFwfSBmcm9tICcuL3dyYXAuanMnXG5cbi8qKlxuICogQHBhcmFtIHtIfSBoXG4gKiBAcGFyYW0ge05vZGV9IG5vZGVcbiAqIEByZXR1cm5zIHtBcnJheTxNZGFzdE5vZGU+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gd3JhcENoaWxkcmVuKGgsIG5vZGUpIHtcbiAgcmV0dXJuIHdyYXAoYWxsKGgsIG5vZGUpKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap-children.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap-list-items.js":
/*!*************************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/util/wrap-list-items.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wrapListItems: function() { return /* binding */ wrapListItems; }\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').H} H\n * @typedef {import('../types.js').Child} Child\n * @typedef {import('../types.js').MdastListContent} MdastListContent\n */\n\n\n\n/**\n * @param {H} h\n * @param {Child} node\n * @returns {Array<MdastListContent>}\n */\nfunction wrapListItems(h, node) {\n  const children = (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node)\n  let index = -1\n\n  while (++index < children.length) {\n    const child = children[index]\n    if (child.type !== 'listItem') {\n      children[index] = {\n        type: 'listItem',\n        spread: false,\n        checked: null,\n        // @ts-expect-error Assume `children[index]` is block content.\n        children: [child]\n      }\n    }\n  }\n\n  // @ts-expect-error Assume all `listItem`s\n  return children\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi91dGlsL3dyYXAtbGlzdC1pdGVtcy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSx5QkFBeUI7QUFDdEMsYUFBYSw2QkFBNkI7QUFDMUMsYUFBYSx3Q0FBd0M7QUFDckQ7O0FBRTZCOztBQUU3QjtBQUNBLFdBQVcsR0FBRztBQUNkLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQLG1CQUFtQiw0Q0FBRztBQUN0Qjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi91dGlsL3dyYXAtbGlzdC1pdGVtcy5qcz9iYTJmIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IfSBIXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkNoaWxkfSBDaGlsZFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5NZGFzdExpc3RDb250ZW50fSBNZGFzdExpc3RDb250ZW50XG4gKi9cblxuaW1wb3J0IHthbGx9IGZyb20gJy4uL2FsbC5qcydcblxuLyoqXG4gKiBAcGFyYW0ge0h9IGhcbiAqIEBwYXJhbSB7Q2hpbGR9IG5vZGVcbiAqIEByZXR1cm5zIHtBcnJheTxNZGFzdExpc3RDb250ZW50Pn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHdyYXBMaXN0SXRlbXMoaCwgbm9kZSkge1xuICBjb25zdCBjaGlsZHJlbiA9IGFsbChoLCBub2RlKVxuICBsZXQgaW5kZXggPSAtMVxuXG4gIHdoaWxlICgrK2luZGV4IDwgY2hpbGRyZW4ubGVuZ3RoKSB7XG4gICAgY29uc3QgY2hpbGQgPSBjaGlsZHJlbltpbmRleF1cbiAgICBpZiAoY2hpbGQudHlwZSAhPT0gJ2xpc3RJdGVtJykge1xuICAgICAgY2hpbGRyZW5baW5kZXhdID0ge1xuICAgICAgICB0eXBlOiAnbGlzdEl0ZW0nLFxuICAgICAgICBzcHJlYWQ6IGZhbHNlLFxuICAgICAgICBjaGVja2VkOiBudWxsLFxuICAgICAgICAvLyBAdHMtZXhwZWN0LWVycm9yIEFzc3VtZSBgY2hpbGRyZW5baW5kZXhdYCBpcyBibG9jayBjb250ZW50LlxuICAgICAgICBjaGlsZHJlbjogW2NoaWxkXVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8vIEB0cy1leHBlY3QtZXJyb3IgQXNzdW1lIGFsbCBgbGlzdEl0ZW1gc1xuICByZXR1cm4gY2hpbGRyZW5cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap-list-items.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wrapText: function() { return /* binding */ wrapText; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').H} H\n */\n\n/**\n * @param {H} h\n * @param {string} value\n * @returns {string}\n */\nfunction wrapText(h, value) {\n  return h.wrapText ? value : value.replace(/\\r?\\n|\\r/g, ' ')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi91dGlsL3dyYXAtdGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLHlCQUF5QjtBQUN0Qzs7QUFFQTtBQUNBLFdBQVcsR0FBRztBQUNkLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL3V0aWwvd3JhcC10ZXh0LmpzP2E1OTYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkh9IEhcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7SH0gaFxuICogQHBhcmFtIHtzdHJpbmd9IHZhbHVlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gd3JhcFRleHQoaCwgdmFsdWUpIHtcbiAgcmV0dXJuIGgud3JhcFRleHQgPyB2YWx1ZSA6IHZhbHVlLnJlcGxhY2UoL1xccj9cXG58XFxyL2csICcgJylcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap.js":
/*!**************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/util/wrap.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wrap: function() { return /* binding */ wrap; },\n/* harmony export */   wrapNeeded: function() { return /* binding */ wrapNeeded; }\n/* harmony export */ });\n/* harmony import */ var extend__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! extend */ \"(app-pages-browser)/../../node_modules/extend/index.js\");\n/* harmony import */ var hast_util_phrasing__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-phrasing */ \"(app-pages-browser)/../../node_modules/hast-util-phrasing/lib/index.js\");\n/* harmony import */ var mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mdast-util-phrasing */ \"(app-pages-browser)/../../node_modules/mdast-util-phrasing/lib/index.js\");\n/**\n * @typedef {import('../types.js').MdastNode} MdastNode\n * @typedef {import('../types.js').MdastPhrasingContent} MdastPhrasingContent\n */\n\n\n\n\n\n/**\n * @param {Array<MdastNode>} nodes\n */\nfunction wrap(nodes) {\n  return runs(nodes, onphrasing)\n\n  /**\n   * @param {Array<MdastPhrasingContent>} nodes\n   * @returns {MdastNode|Array<MdastNode>}\n   */\n  function onphrasing(nodes) {\n    const head = nodes[0]\n\n    if (\n      nodes.length === 1 &&\n      head.type === 'text' &&\n      (head.value === ' ' || head.value === '\\n')\n    ) {\n      return []\n    }\n\n    return {type: 'paragraph', children: nodes}\n  }\n}\n\n/**\n * Check if there are non-phrasing mdast nodes returned.\n * This is needed if a fragment is given, which could just be a sentence, and\n * doesn’t need a wrapper paragraph.\n *\n * @param {Array<MdastNode>} nodes\n * @returns {boolean}\n */\nfunction wrapNeeded(nodes) {\n  let index = -1\n  /** @type {MdastNode} */\n  let node\n\n  while (++index < nodes.length) {\n    node = nodes[index]\n\n    if (!phrasing(node) || ('children' in node && wrapNeeded(node.children))) {\n      return true\n    }\n  }\n\n  return false\n}\n\n/**\n * Wrap all runs of mdast phrasing content in `paragraph` nodes.\n *\n * @param {Array<MdastNode>} nodes\n * @param {(nodes: Array<MdastPhrasingContent>) => MdastNode|Array<MdastNode>} onphrasing\n * @param {(node: MdastNode) => MdastNode} [onnonphrasing]\n */\nfunction runs(nodes, onphrasing, onnonphrasing) {\n  const nonphrasing = onnonphrasing || identity\n  /** @type {Array<MdastNode>} */\n  const flattened = flatten(nodes)\n  /** @type {Array<MdastNode>} */\n  let result = []\n  let index = -1\n  /** @type {Array<MdastPhrasingContent>|undefined} */\n  let queue\n  /** @type {MdastNode} */\n  let node\n\n  while (++index < flattened.length) {\n    node = flattened[index]\n\n    if (phrasing(node)) {\n      if (!queue) queue = []\n      queue.push(node)\n    } else {\n      if (queue) {\n        result = result.concat(onphrasing(queue))\n        queue = undefined\n      }\n\n      result = result.concat(nonphrasing(node))\n    }\n  }\n\n  if (queue) {\n    result = result.concat(onphrasing(queue))\n  }\n\n  return result\n}\n\n/**\n * Flatten a list of nodes.\n *\n * @param {Array<MdastNode>} nodes\n * @returns {Array<MdastNode>}\n */\nfunction flatten(nodes) {\n  /** @type {Array<MdastNode>} */\n  let flattened = []\n  let index = -1\n  /** @type {MdastNode} */\n  let node\n\n  while (++index < nodes.length) {\n    node = nodes[index]\n\n    // Straddling: some elements are *weird*.\n    // Namely: `map`, `ins`, `del`, and `a`, as they are hybrid elements.\n    // See: <https://html.spec.whatwg.org/#paragraphs>.\n    // Paragraphs are the weirdest of them all.\n    // See the straddling fixture for more info!\n    // `ins` is ignored in mdast, so we don’t need to worry about that.\n    // `map` maps to its content, so we don’t need to worry about that either.\n    // `del` maps to `delete` and `a` to `link`, so we do handle those.\n    // What we’ll do is split `node` over each of its children.\n    if (\n      (node.type === 'delete' || node.type === 'link') &&\n      wrapNeeded(node.children)\n    ) {\n      flattened = flattened.concat(split(node))\n    } else {\n      flattened.push(node)\n    }\n  }\n\n  return flattened\n}\n\n/**\n * @param {MdastNode} node\n * @returns {Array<MdastNode>}\n */\nfunction split(node) {\n  // @ts-expect-error Assume parent.\n  return runs(node.children, onphrasing, onnonphrasing)\n\n  /**\n   * Use `child`, add `parent` as its first child, put the original children\n   * into `parent`.\n   * If `child` is not a parent, `parent` will not be added.\n   *\n   * @param {MdastNode} child\n   * @returns {MdastNode}\n   */\n  function onnonphrasing(child) {\n    if ('children' in child && 'children' in node) {\n      const {children, ...rest} = node\n      return {\n        ...child,\n        // @ts-expect-error: assume matching parent & child.\n        children: [{...extend__WEBPACK_IMPORTED_MODULE_0__(true, {}, rest), children: child.children}]\n      }\n    }\n\n    return {...child}\n  }\n\n  /**\n   * Use `parent`, put the phrasing run inside it.\n   *\n   * @param {Array<MdastPhrasingContent>} nodes\n   * @returns {MdastNode}\n   */\n  function onphrasing(nodes) {\n    // @ts-expect-error: assume parent.\n    const {children, ...rest} = node\n    // @ts-expect-error: assume matching parent & child.\n    return {...extend__WEBPACK_IMPORTED_MODULE_0__(true, {}, rest), children: nodes}\n  }\n}\n\n/**\n * Check if an mdast node is phrasing.\n *\n * Also supports checking embedded hast fields.\n *\n * @param {MdastNode} node\n * @returns {node is MdastPhrasingContent}\n */\nfunction phrasing(node) {\n  return node.data && node.data.hName\n    ? (0,hast_util_phrasing__WEBPACK_IMPORTED_MODULE_1__.phrasing)({\n        type: 'element',\n        tagName: node.data.hName,\n        properties: {},\n        children: []\n      })\n    : (0,mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_2__.phrasing)(node)\n}\n\n/**\n * @template {unknown} T\n * @param {T} n\n * @returns {T}\n */\nfunction identity(n) {\n  return n\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/util/wrap.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/hast-util-to-text/lib/index.js":
/*!*****************************************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/node_modules/hast-util-to-text/lib/index.js ***!
  \*****************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toText: function() { return /* binding */ toText; }\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(app-pages-browser)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var unist_util_find_after__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-find-after */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-find-after/lib/index.js\");\n/**\n * @typedef {import('hast-util-is-element').TestFunctionAnything} TestFunctionAnything\n * @typedef {import('hast').Content} Content\n * @typedef {import('hast').Text} Text\n * @typedef {import('hast').Comment} Comment\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Element} Element\n */\n\n/**\n * @typedef {Content | Root} Node\n *   Any node.\n * @typedef {Extract<Node, import('unist').Parent>} Parent\n *   Any parent.\n * @typedef {'normal' | 'pre' | 'nowrap' | 'pre-wrap'} Whitespace\n *   Valid and useful whitespace values (from CSS).\n * @typedef {0 | 1 | 2} BreakNumber\n *   Specific break:\n *\n *   *   `0` — space\n *   *   `1` — line ending\n *   *   `2` — blank line\n * @typedef {'\\n'} BreakForce\n *   Forced break.\n * @typedef {boolean} BreakValue\n *   Whether there was a break.\n * @typedef {BreakValue | BreakNumber | undefined} BreakBefore\n *   Any value for a break before.\n * @typedef {BreakValue | BreakNumber | BreakForce | undefined} BreakAfter\n *   Any value for a break after.\n *\n * @typedef CollectionInfo\n *   Info on current collection.\n * @property {Whitespace} whitespace\n *   Current whitespace setting.\n * @property {BreakBefore} breakBefore\n *   Whether there was a break before.\n * @property {BreakAfter} breakAfter\n *   Whether there was a break after.\n *\n * @typedef Options\n *   Configuration.\n * @property {Whitespace | null | undefined} [whitespace='normal']\n *   Initial CSS whitespace setting to use.\n */\n\n\n\n\nconst searchLineFeeds = /\\n/g\nconst searchTabOrSpaces = /[\\t ]+/g\n\nconst br = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('br')\nconst p = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('p')\nconst cell = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)(['th', 'td'])\nconst row = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('tr')\n\n// Note that we don’t need to include void elements here as they don’t have text.\n// See: <https://github.com/wooorm/html-void-elements>\nconst notRendered = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  // List from: <https://html.spec.whatwg.org/#hidden-elements>\n  'datalist',\n  'head',\n  'noembed',\n  'noframes',\n  'noscript', // Act as if we support scripting.\n  'rp',\n  'script',\n  'style',\n  'template',\n  'title',\n  // Hidden attribute.\n  hidden,\n  // From: <https://html.spec.whatwg.org/#flow-content-3>\n  closedDialog\n])\n\n// See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\nconst blockOrCaption = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  'address', // Flow content\n  'article', // Sections and headings\n  'aside', // Sections and headings\n  'blockquote', // Flow content\n  'body', // Page\n  'caption', // `table-caption`\n  'center', // Flow content (legacy)\n  'dd', // Lists\n  'dialog', // Flow content\n  'dir', // Lists (legacy)\n  'dl', // Lists\n  'dt', // Lists\n  'div', // Flow content\n  'figure', // Flow content\n  'figcaption', // Flow content\n  'footer', // Flow content\n  'form,', // Flow content\n  'h1', // Sections and headings\n  'h2', // Sections and headings\n  'h3', // Sections and headings\n  'h4', // Sections and headings\n  'h5', // Sections and headings\n  'h6', // Sections and headings\n  'header', // Flow content\n  'hgroup', // Sections and headings\n  'hr', // Flow content\n  'html', // Page\n  'legend', // Flow content\n  'listing', // Flow content (legacy)\n  'main', // Flow content\n  'menu', // Lists\n  'nav', // Sections and headings\n  'ol', // Lists\n  'p', // Flow content\n  'plaintext', // Flow content (legacy)\n  'pre', // Flow content\n  'section', // Sections and headings\n  'ul', // Lists\n  'xmp' // Flow content (legacy)\n])\n\n/**\n * Get the plain-text value of a node.\n *\n * ###### Algorithm\n *\n * *   if `tree` is a comment, returns its `value`\n * *   if `tree` is a text, applies normal whitespace collapsing to its\n *     `value`, as defined by the CSS Text spec\n * *   if `tree` is a root or element, applies an algorithm similar to the\n *     `innerText` getter as defined by HTML\n *\n * ###### Notes\n *\n * > 👉 **Note**: the algorithm acts as if `tree` is being rendered, and as if\n * > we’re a CSS-supporting user agent, with scripting enabled.\n *\n * *   if `tree` is an element that is not displayed (such as a `head`), we’ll\n *     still use the `innerText` algorithm instead of switching to `textContent`\n * *   if descendants of `tree` are elements that are not displayed, they are\n *     ignored\n * *   CSS is not considered, except for the default user agent style sheet\n * *   a line feed is collapsed instead of ignored in cases where Fullwidth, Wide,\n *     or Halfwidth East Asian Width characters are used, the same goes for a case\n *     with Chinese, Japanese, or Yi writing systems\n * *   replaced elements (such as `audio`) are treated like non-replaced elements\n *\n * @param {Node} tree\n *   Tree to turn into text.\n * @param {Options} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized `tree`.\n */\nfunction toText(tree, options = {}) {\n  const children = 'children' in tree ? tree.children : []\n  const block = blockOrCaption(tree)\n  const whitespace = inferWhitespace(tree, {\n    whitespace: options.whitespace || 'normal',\n    breakBefore: false,\n    breakAfter: false\n  })\n\n  /** @type {Array<string | BreakNumber>} */\n  const results = []\n\n  // Treat `text` and `comment` as having normal white-space.\n  // This deviates from the spec as in the DOM the node’s `.data` has to be\n  // returned.\n  // If you want that behavior use `hast-util-to-string`.\n  // All other nodes are later handled as if they are `element`s (so the\n  // algorithm also works on a `root`).\n  // Nodes without children are treated as a void element, so `doctype` is thus\n  // ignored.\n  if (tree.type === 'text' || tree.type === 'comment') {\n    results.push(\n      ...collectText(tree, {\n        whitespace,\n        breakBefore: true,\n        breakAfter: true\n      })\n    )\n  }\n\n  // 1.  If this element is not being rendered, or if the user agent is a\n  //     non-CSS user agent, then return the same value as the textContent IDL\n  //     attribute on this element.\n  //\n  //     Note: we’re not supporting stylesheets so we’re acting as if the node\n  //     is rendered.\n  //\n  //     If you want that behavior use `hast-util-to-string`.\n  //     Important: we’ll have to account for this later though.\n\n  // 2.  Let results be a new empty list.\n  let index = -1\n\n  // 3.  For each child node node of this element:\n  while (++index < children.length) {\n    // 3.1. Let current be the list resulting in running the inner text\n    //      collection steps with node.\n    //      Each item in results will either be a JavaScript string or a\n    //      positive integer (a required line break count).\n    // 3.2. For each item item in current, append item to results.\n    results.push(\n      // @ts-expect-error Looks like a parent.\n      ...innerTextCollection(children[index], tree, {\n        whitespace,\n        breakBefore: index ? undefined : block,\n        breakAfter:\n          index < children.length - 1 ? br(children[index + 1]) : block\n      })\n    )\n  }\n\n  // 4.  Remove any items from results that are the empty string.\n  // 5.  Remove any runs of consecutive required line break count items at the\n  //     start or end of results.\n  // 6.  Replace each remaining run of consecutive required line break count\n  //     items with a string consisting of as many U+000A LINE FEED (LF)\n  //     characters as the maximum of the values in the required line break\n  //     count items.\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {number | undefined} */\n  let count\n\n  index = -1\n\n  while (++index < results.length) {\n    const value = results[index]\n\n    if (typeof value === 'number') {\n      if (count !== undefined && value > count) count = value\n    } else if (value) {\n      if (count !== undefined && count > -1) {\n        result.push('\\n'.repeat(count) || ' ')\n      }\n\n      count = -1\n      result.push(value)\n    }\n  }\n\n  // 7.  Return the concatenation of the string items in results.\n  return result.join('')\n}\n\n/**\n * <https://html.spec.whatwg.org/#inner-text-collection-steps>\n *\n * @param {Node} node\n * @param {Parent} parent\n * @param {CollectionInfo} info\n * @returns {Array<string | BreakNumber>}\n */\nfunction innerTextCollection(node, parent, info) {\n  if (node.type === 'element') {\n    return collectElement(node, parent, info)\n  }\n\n  if (node.type === 'text') {\n    return info.whitespace === 'normal'\n      ? collectText(node, info)\n      : collectPreText(node)\n  }\n\n  return []\n}\n\n/**\n * Collect an element.\n *\n * @param {Element} node\n *   Element node.\n * @param {Parent} parent\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Array<string | BreakNumber>}\n */\nfunction collectElement(node, parent, info) {\n  // First we infer the `white-space` property.\n  const whitespace = inferWhitespace(node, info)\n  const children = node.children || []\n  let index = -1\n  /** @type {Array<string | BreakNumber>} */\n  let items = []\n\n  // We’re ignoring point 3, and exiting without any content here, because we\n  // deviated from the spec in `toText` at step 3.\n  if (notRendered(node)) {\n    return items\n  }\n\n  /** @type {BreakNumber | undefined} */\n  let prefix\n  /** @type {BreakNumber | BreakForce | undefined} */\n  let suffix\n  // Note: we first detect if there is going to be a break before or after the\n  // contents, as that changes the white-space handling.\n\n  // 2.  If node’s computed value of `visibility` is not `visible`, then return\n  //     items.\n  //\n  //     Note: Ignored, as everything is visible by default user agent styles.\n\n  // 3.  If node is not being rendered, then return items. [...]\n  //\n  //     Note: We already did this above.\n\n  // See `collectText` for step 4.\n\n  // 5.  If node is a `<br>` element, then append a string containing a single\n  //     U+000A LINE FEED (LF) character to items.\n  if (br(node)) {\n    suffix = '\\n'\n  }\n\n  // 7.  If node’s computed value of `display` is `table-row`, and node’s CSS\n  //     box is not the last `table-row` box of the nearest ancestor `table`\n  //     box, then append a string containing a single U+000A LINE FEED (LF)\n  //     character to items.\n  //\n  //     See: <https://html.spec.whatwg.org/#tables-2>\n  //     Note: needs further investigation as this does not account for implicit\n  //     rows.\n  else if (row(node) && (0,unist_util_find_after__WEBPACK_IMPORTED_MODULE_1__.findAfter)(parent, node, row)) {\n    suffix = '\\n'\n  }\n\n  // 8.  If node is a `<p>` element, then append 2 (a required line break count)\n  //     at the beginning and end of items.\n  else if (p(node)) {\n    prefix = 2\n    suffix = 2\n  }\n\n  // 9.  If node’s used value of `display` is block-level or `table-caption`,\n  //     then append 1 (a required line break count) at the beginning and end of\n  //     items.\n  else if (blockOrCaption(node)) {\n    prefix = 1\n    suffix = 1\n  }\n\n  // 1.  Let items be the result of running the inner text collection steps with\n  //     each child node of node in tree order, and then concatenating the\n  //     results to a single list.\n  while (++index < children.length) {\n    items = items.concat(\n      innerTextCollection(children[index], node, {\n        whitespace,\n        breakBefore: index ? undefined : prefix,\n        breakAfter:\n          index < children.length - 1 ? br(children[index + 1]) : suffix\n      })\n    )\n  }\n\n  // 6.  If node’s computed value of `display` is `table-cell`, and node’s CSS\n  //     box is not the last `table-cell` box of its enclosing `table-row` box,\n  //     then append a string containing a single U+0009 CHARACTER TABULATION\n  //     (tab) character to items.\n  //\n  //     See: <https://html.spec.whatwg.org/#tables-2>\n  if (cell(node) && (0,unist_util_find_after__WEBPACK_IMPORTED_MODULE_1__.findAfter)(parent, node, cell)) {\n    items.push('\\t')\n  }\n\n  // Add the pre- and suffix.\n  if (prefix) items.unshift(prefix)\n  if (suffix) items.push(suffix)\n\n  return items\n}\n\n/**\n * 4.  If node is a Text node, then for each CSS text box produced by node,\n *     in content order, compute the text of the box after application of the\n *     CSS `white-space` processing rules and `text-transform` rules, set\n *     items to the list of the resulting strings, and return items.\n *     The CSS `white-space` processing rules are slightly modified:\n *     collapsible spaces at the end of lines are always collapsed, but they\n *     are only removed if the line is the last line of the block, or it ends\n *     with a br element.\n *     Soft hyphens should be preserved.\n *\n *     Note: See `collectText` and `collectPreText`.\n *     Note: we don’t deal with `text-transform`, no element has that by\n *     default.\n *\n * See: <https://drafts.csswg.org/css-text/#white-space-phase-1>\n *\n * @param {Text | Comment} node\n *   Text node.\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Array<string | BreakNumber>}\n *   Result.\n */\nfunction collectText(node, info) {\n  const value = String(node.value)\n  /** @type {Array<string>} */\n  const lines = []\n  /** @type {Array<string | BreakNumber>} */\n  const result = []\n  let start = 0\n\n  while (start <= value.length) {\n    searchLineFeeds.lastIndex = start\n\n    const match = searchLineFeeds.exec(value)\n    const end = match && 'index' in match ? match.index : value.length\n\n    lines.push(\n      // Any sequence of collapsible spaces and tabs immediately preceding or\n      // following a segment break is removed.\n      trimAndCollapseSpacesAndTabs(\n        // […] ignoring bidi formatting characters (characters with the\n        // Bidi_Control property [UAX9]: ALM, LTR, RTL, LRE-RLO, LRI-PDI) as if\n        // they were not there.\n        value\n          .slice(start, end)\n          .replace(/[\\u061C\\u200E\\u200F\\u202A-\\u202E\\u2066-\\u2069]/g, ''),\n        start === 0 ? info.breakBefore : true,\n        end === value.length ? info.breakAfter : true\n      )\n    )\n\n    start = end + 1\n  }\n\n  // Collapsible segment breaks are transformed for rendering according to the\n  // segment break transformation rules.\n  // So here we jump to 4.1.2 of [CSSTEXT]:\n  // Any collapsible segment break immediately following another collapsible\n  // segment break is removed\n  let index = -1\n  /** @type {BreakNumber | undefined} */\n  let join\n\n  while (++index < lines.length) {\n    // *   If the character immediately before or immediately after the segment\n    //     break is the zero-width space character (U+200B), then the break is\n    //     removed, leaving behind the zero-width space.\n    if (\n      lines[index].charCodeAt(lines[index].length - 1) === 0x200b /* ZWSP */ ||\n      (index < lines.length - 1 &&\n        lines[index + 1].charCodeAt(0) === 0x200b) /* ZWSP */\n    ) {\n      result.push(lines[index])\n      join = undefined\n    }\n\n    // *   Otherwise, if the East Asian Width property [UAX11] of both the\n    //     character before and after the segment break is Fullwidth, Wide, or\n    //     Halfwidth (not Ambiguous), and neither side is Hangul, then the\n    //     segment break is removed.\n    //\n    //     Note: ignored.\n    // *   Otherwise, if the writing system of the segment break is Chinese,\n    //     Japanese, or Yi, and the character before or after the segment break\n    //     is punctuation or a symbol (Unicode general category P* or S*) and\n    //     has an East Asian Width property of Ambiguous, and the character on\n    //     the other side of the segment break is Fullwidth, Wide, or Halfwidth,\n    //     and not Hangul, then the segment break is removed.\n    //\n    //     Note: ignored.\n\n    // *   Otherwise, the segment break is converted to a space (U+0020).\n    else if (lines[index]) {\n      if (typeof join === 'number') result.push(join)\n      result.push(lines[index])\n      join = 0\n    } else if (index === 0 || index === lines.length - 1) {\n      // If this line is empty, and it’s the first or last, add a space.\n      // Note that this function is only called in normal whitespace, so we\n      // don’t worry about `pre`.\n      result.push(0)\n    }\n  }\n\n  return result\n}\n\n/**\n * Collect a text node as “pre” whitespace.\n *\n * @param {Text} node\n *   Text node.\n * @returns {Array<string | BreakNumber>}\n *   Result.\n */\nfunction collectPreText(node) {\n  return [String(node.value)]\n}\n\n/**\n * 3.  Every collapsible tab is converted to a collapsible space (U+0020).\n * 4.  Any collapsible space immediately following another collapsible\n *     space—even one outside the boundary of the inline containing that\n *     space, provided both spaces are within the same inline formatting\n *     context—is collapsed to have zero advance width. (It is invisible,\n *     but retains its soft wrap opportunity, if any.)\n *\n * @param {string} value\n *   Value to collapse.\n * @param {BreakBefore} breakBefore\n *   Whether there was a break before.\n * @param {BreakAfter} breakAfter\n *   Whether there was a break after.\n * @returns {string}\n *   Result.\n */\nfunction trimAndCollapseSpacesAndTabs(value, breakBefore, breakAfter) {\n  /** @type {Array<string>} */\n  const result = []\n  let start = 0\n  /** @type {number | undefined} */\n  let end\n\n  while (start < value.length) {\n    searchTabOrSpaces.lastIndex = start\n    const match = searchTabOrSpaces.exec(value)\n    end = match ? match.index : value.length\n\n    // If we’re not directly after a segment break, but there was white space,\n    // add an empty value that will be turned into a space.\n    if (!start && !end && match && !breakBefore) {\n      result.push('')\n    }\n\n    if (start !== end) {\n      result.push(value.slice(start, end))\n    }\n\n    start = match ? end + match[0].length : end\n  }\n\n  // If we reached the end, there was trailing white space, and there’s no\n  // segment break after this node, add an empty value that will be turned\n  // into a space.\n  if (start !== end && !breakAfter) {\n    result.push('')\n  }\n\n  return result.join(' ')\n}\n\n/**\n * Figure out the whitespace of a node.\n *\n * We don’t support void elements here (so `nobr wbr` -> `normal` is ignored).\n *\n * @param {Node} node\n *   Node (typically `Element`).\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Whitespace}\n *   Applied whitespace.\n */\nfunction inferWhitespace(node, info) {\n  if (node.type === 'element') {\n    const props = node.properties || {}\n    switch (node.tagName) {\n      case 'listing':\n      case 'plaintext':\n      case 'xmp': {\n        return 'pre'\n      }\n\n      case 'nobr': {\n        return 'nowrap'\n      }\n\n      case 'pre': {\n        return props.wrap ? 'pre-wrap' : 'pre'\n      }\n\n      case 'td':\n      case 'th': {\n        return props.noWrap ? 'nowrap' : info.whitespace\n      }\n\n      case 'textarea': {\n        return 'pre-wrap'\n      }\n\n      default:\n    }\n  }\n\n  return info.whitespace\n}\n\n/** @type {TestFunctionAnything} */\nfunction hidden(node) {\n  return Boolean((node.properties || {}).hidden)\n}\n\n/** @type {TestFunctionAnything} */\nfunction closedDialog(node) {\n  return node.tagName === 'dialog' && !(node.properties || {}).open\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/hast-util-to-text/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-find-after/lib/index.js":
/*!*********************************************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/node_modules/unist-util-find-after/lib/index.js ***!
  \*********************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findAfter: function() { return /* binding */ findAfter; }\n/* harmony export */ });\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-is */ \"(app-pages-browser)/../../node_modules/unist-util-is/lib/index.js\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n * @typedef {import('unist-util-is').Test} Test\n */\n\n\n\n/**\n * Find the first node in `parent` after another `node` or after an index,\n * that passes `test`.\n\n * @param parent\n *   Parent node.\n * @param index\n *   Child of `parent` or it’s index.\n * @param test\n *   `unist-util-is`-compatible test.\n * @returns\n *   Child of `parent` or `null`.\n */\nconst findAfter =\n  /**\n   * @type {(\n   *  (<T extends Node>(node: Parent, index: Node | number, test: import('unist-util-is').PredicateTest<T>) => T | null) &\n   *  ((node: Parent, index: Node | number, test?: Test) => Node | null)\n   * )}\n   */\n  (\n    /**\n     * @param {Parent} parent\n     * @param {Node | number} index\n     * @param {Test} [test]\n     * @returns {Node | null}\n     */\n    function (parent, index, test) {\n      const is = (0,unist_util_is__WEBPACK_IMPORTED_MODULE_0__.convert)(test)\n\n      if (!parent || !parent.type || !parent.children) {\n        throw new Error('Expected parent node')\n      }\n\n      if (typeof index === 'number') {\n        if (index < 0 || index === Number.POSITIVE_INFINITY) {\n          throw new Error('Expected positive finite number as index')\n        }\n      } else {\n        index = parent.children.indexOf(index)\n\n        if (index < 0) {\n          throw new Error('Expected child node or index')\n        }\n      }\n\n      while (++index < parent.children.length) {\n        if (is(parent.children[index], index, parent)) {\n          return parent.children[index]\n        }\n      }\n\n      return null\n    }\n  )\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-find-after/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit-parents/lib/color.browser.js":
/*!********************************************************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit-parents/lib/color.browser.js ***!
  \********************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   color: function() { return /* binding */ color; }\n/* harmony export */ });\n/**\n * @param {string} d\n * @returns {string}\n */\nfunction color(d) {\n  return d\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L25vZGVfbW9kdWxlcy91bmlzdC11dGlsLXZpc2l0LXBhcmVudHMvbGliL2NvbG9yLmJyb3dzZXIuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9ub2RlX21vZHVsZXMvdW5pc3QtdXRpbC12aXNpdC1wYXJlbnRzL2xpYi9jb2xvci5icm93c2VyLmpzPzRmMDAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAcGFyYW0ge3N0cmluZ30gZFxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNvbG9yKGQpIHtcbiAgcmV0dXJuIGRcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit-parents/lib/color.browser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit-parents/lib/index.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit-parents/lib/index.js ***!
  \************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CONTINUE: function() { return /* binding */ CONTINUE; },\n/* harmony export */   EXIT: function() { return /* binding */ EXIT; },\n/* harmony export */   SKIP: function() { return /* binding */ SKIP; },\n/* harmony export */   visitParents: function() { return /* binding */ visitParents; }\n/* harmony export */ });\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-is */ \"(app-pages-browser)/../../node_modules/unist-util-is/lib/index.js\");\n/* harmony import */ var _color_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./color.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit-parents/lib/color.browser.js\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n * @typedef {import('unist-util-is').Test} Test\n */\n\n/**\n * @typedef {boolean | 'skip'} Action\n *   Union of the action types.\n *\n * @typedef {number} Index\n *   Move to the sibling at `index` next (after node itself is completely\n *   traversed).\n *\n *   Useful if mutating the tree, such as removing the node the visitor is\n *   currently on, or any of its previous siblings.\n *   Results less than 0 or greater than or equal to `children.length` stop\n *   traversing the parent.\n *\n * @typedef {[(Action | null | undefined | void)?, (Index | null | undefined)?]} ActionTuple\n *   List with one or two values, the first an action, the second an index.\n *\n * @typedef {Action | ActionTuple | Index | null | undefined | void} VisitorResult\n *   Any value that can be returned from a visitor.\n */\n\n/**\n * @template {Node} [Visited=Node]\n *   Visited node type.\n * @template {Parent} [Ancestor=Parent]\n *   Ancestor type.\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform the parent of node (the last of `ancestors`).\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of an ancestor still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Array<Ancestor>} ancestors\n *   Ancestors of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   Tree type.\n * @template {Test} [Check=string]\n *   Test type.\n * @typedef {Visitor<import('./complex-types.js').Matches<import('./complex-types.js').InclusiveDescendant<Tree>, Check>, Extract<import('./complex-types.js').InclusiveDescendant<Tree>, Parent>>} BuildVisitor\n *   Build a typed `Visitor` function from a tree and a test.\n *\n *   It will infer which values are passed as `node` and which as `parents`.\n */\n\n\n\n\n/**\n * Continue traversing as normal.\n */\nconst CONTINUE = true\n\n/**\n * Stop traversing immediately.\n */\nconst EXIT = false\n\n/**\n * Do not traverse this node’s children.\n */\nconst SKIP = 'skip'\n\n/**\n * Visit nodes, with ancestral information.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @param tree\n *   Tree to traverse.\n * @param test\n *   `unist-util-is`-compatible test\n * @param visitor\n *   Handle each node.\n * @param reverse\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns\n *   Nothing.\n */\nconst visitParents =\n  /**\n   * @type {(\n   *   (<Tree extends Node, Check extends Test>(tree: Tree, test: Check, visitor: BuildVisitor<Tree, Check>, reverse?: boolean | null | undefined) => void) &\n   *   (<Tree extends Node>(tree: Tree, visitor: BuildVisitor<Tree>, reverse?: boolean | null | undefined) => void)\n   * )}\n   */\n  (\n    /**\n     * @param {Node} tree\n     * @param {Test} test\n     * @param {Visitor<Node>} visitor\n     * @param {boolean | null | undefined} [reverse]\n     * @returns {void}\n     */\n    function (tree, test, visitor, reverse) {\n      if (typeof test === 'function' && typeof visitor !== 'function') {\n        reverse = visitor\n        // @ts-expect-error no visitor given, so `visitor` is test.\n        visitor = test\n        test = null\n      }\n\n      const is = (0,unist_util_is__WEBPACK_IMPORTED_MODULE_0__.convert)(test)\n      const step = reverse ? -1 : 1\n\n      factory(tree, undefined, [])()\n\n      /**\n       * @param {Node} node\n       * @param {number | undefined} index\n       * @param {Array<Parent>} parents\n       */\n      function factory(node, index, parents) {\n        /** @type {Record<string, unknown>} */\n        // @ts-expect-error: hush\n        const value = node && typeof node === 'object' ? node : {}\n\n        if (typeof value.type === 'string') {\n          const name =\n            // `hast`\n            typeof value.tagName === 'string'\n              ? value.tagName\n              : // `xast`\n              typeof value.name === 'string'\n              ? value.name\n              : undefined\n\n          Object.defineProperty(visit, 'name', {\n            value:\n              'node (' + (0,_color_js__WEBPACK_IMPORTED_MODULE_1__.color)(node.type + (name ? '<' + name + '>' : '')) + ')'\n          })\n        }\n\n        return visit\n\n        function visit() {\n          /** @type {ActionTuple} */\n          let result = []\n          /** @type {ActionTuple} */\n          let subresult\n          /** @type {number} */\n          let offset\n          /** @type {Array<Parent>} */\n          let grandparents\n\n          if (!test || is(node, index, parents[parents.length - 1] || null)) {\n            result = toResult(visitor(node, parents))\n\n            if (result[0] === EXIT) {\n              return result\n            }\n          }\n\n          // @ts-expect-error looks like a parent.\n          if (node.children && result[0] !== SKIP) {\n            // @ts-expect-error looks like a parent.\n            offset = (reverse ? node.children.length : -1) + step\n            // @ts-expect-error looks like a parent.\n            grandparents = parents.concat(node)\n\n            // @ts-expect-error looks like a parent.\n            while (offset > -1 && offset < node.children.length) {\n              // @ts-expect-error looks like a parent.\n              subresult = factory(node.children[offset], offset, grandparents)()\n\n              if (subresult[0] === EXIT) {\n                return subresult\n              }\n\n              offset =\n                typeof subresult[1] === 'number' ? subresult[1] : offset + step\n            }\n          }\n\n          return result\n        }\n      }\n    }\n  )\n\n/**\n * Turn a return value into a clean result.\n *\n * @param {VisitorResult} value\n *   Valid return values from visitors.\n * @returns {ActionTuple}\n *   Clean result.\n */\nfunction toResult(value) {\n  if (Array.isArray(value)) {\n    return value\n  }\n\n  if (typeof value === 'number') {\n    return [CONTINUE, value]\n  }\n\n  return [value]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit-parents/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit/lib/index.js":
/*!****************************************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit/lib/index.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CONTINUE: function() { return /* reexport safe */ unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_0__.CONTINUE; },\n/* harmony export */   EXIT: function() { return /* reexport safe */ unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_0__.EXIT; },\n/* harmony export */   SKIP: function() { return /* reexport safe */ unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_0__.SKIP; },\n/* harmony export */   visit: function() { return /* binding */ visit; }\n/* harmony export */ });\n/* harmony import */ var unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit-parents */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit-parents/lib/index.js\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n * @typedef {import('unist-util-is').Test} Test\n * @typedef {import('unist-util-visit-parents').VisitorResult} VisitorResult\n */\n\n/**\n * Check if `Child` can be a child of `Ancestor`.\n *\n * Returns the ancestor when `Child` can be a child of `Ancestor`, or returns\n * `never`.\n *\n * @template {Node} Ancestor\n *   Node type.\n * @template {Node} Child\n *   Node type.\n * @typedef {(\n *   Ancestor extends Parent\n *     ? Child extends Ancestor['children'][number]\n *       ? Ancestor\n *       : never\n *     : never\n * )} ParentsOf\n */\n\n/**\n * @template {Node} [Visited=Node]\n *   Visited node type.\n * @template {Parent} [Ancestor=Parent]\n *   Ancestor type.\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform `parent`.\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of `parent` still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Visited extends Node ? number | null : never} index\n *   Index of `node` in `parent`.\n * @param {Ancestor extends Node ? Ancestor | null : never} parent\n *   Parent of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n */\n\n/**\n * Build a typed `Visitor` function from a node and all possible parents.\n *\n * It will infer which values are passed as `node` and which as `parent`.\n *\n * @template {Node} Visited\n *   Node type.\n * @template {Parent} Ancestor\n *   Parent type.\n * @typedef {Visitor<Visited, ParentsOf<Ancestor, Visited>>} BuildVisitorFromMatch\n */\n\n/**\n * Build a typed `Visitor` function from a list of descendants and a test.\n *\n * It will infer which values are passed as `node` and which as `parent`.\n *\n * @template {Node} Descendant\n *   Node type.\n * @template {Test} Check\n *   Test type.\n * @typedef {(\n *   BuildVisitorFromMatch<\n *     import('unist-util-visit-parents/complex-types.js').Matches<Descendant, Check>,\n *     Extract<Descendant, Parent>\n *   >\n * )} BuildVisitorFromDescendants\n */\n\n/**\n * Build a typed `Visitor` function from a tree and a test.\n *\n * It will infer which values are passed as `node` and which as `parent`.\n *\n * @template {Node} [Tree=Node]\n *   Node type.\n * @template {Test} [Check=string]\n *   Test type.\n * @typedef {(\n *   BuildVisitorFromDescendants<\n *     import('unist-util-visit-parents/complex-types.js').InclusiveDescendant<Tree>,\n *     Check\n *   >\n * )} BuildVisitor\n */\n\n\n\n/**\n * Visit nodes.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @param tree\n *   Tree to traverse.\n * @param test\n *   `unist-util-is`-compatible test\n * @param visitor\n *   Handle each node.\n * @param reverse\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns\n *   Nothing.\n */\nconst visit =\n  /**\n   * @type {(\n   *   (<Tree extends Node, Check extends Test>(tree: Tree, test: Check, visitor: BuildVisitor<Tree, Check>, reverse?: boolean | null | undefined) => void) &\n   *   (<Tree extends Node>(tree: Tree, visitor: BuildVisitor<Tree>, reverse?: boolean | null | undefined) => void)\n   * )}\n   */\n  (\n    /**\n     * @param {Node} tree\n     * @param {Test} test\n     * @param {Visitor} visitor\n     * @param {boolean | null | undefined} [reverse]\n     * @returns {void}\n     */\n    function (tree, test, visitor, reverse) {\n      if (typeof test === 'function' && typeof visitor !== 'function') {\n        reverse = visitor\n        visitor = test\n        test = null\n      }\n\n      (0,unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_0__.visitParents)(tree, test, overload, reverse)\n\n      /**\n       * @param {Node} node\n       * @param {Array<Parent>} parents\n       */\n      function overload(node, parents) {\n        const parent = parents[parents.length - 1]\n        return visitor(\n          node,\n          parent ? parent.children.indexOf(node) : null,\n          parent\n        )\n      }\n    }\n  )\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/mdast-util-phrasing/lib/index.js":
/*!***********************************************************!*\
  !*** ../../node_modules/mdast-util-phrasing/lib/index.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   phrasing: function() { return /* binding */ phrasing; }\n/* harmony export */ });\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-is */ \"(app-pages-browser)/../../node_modules/unist-util-is/lib/index.js\");\n/**\n * @typedef {import('mdast').PhrasingContent} PhrasingContent\n * @typedef {import('unist-util-is').AssertPredicate<PhrasingContent>} AssertPredicatePhrasing\n */\n\n\n\n/**\n * Check if the given value is *phrasing content*.\n *\n * @param\n *   Thing to check, typically `Node`.\n * @returns\n *   Whether `value` is phrasing content.\n */\nconst phrasing = /** @type {AssertPredicatePhrasing} */ (\n  (0,unist_util_is__WEBPACK_IMPORTED_MODULE_0__.convert)([\n    'break',\n    'delete',\n    'emphasis',\n    'footnote',\n    'footnoteReference',\n    'image',\n    'imageReference',\n    'inlineCode',\n    'link',\n    'linkReference',\n    'strong',\n    'text'\n  ])\n)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC1waHJhc2luZy9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEsaUNBQWlDO0FBQzlDLGFBQWEsMERBQTBEO0FBQ3ZFOztBQUVxQzs7QUFFckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPLDRCQUE0Qix5QkFBeUI7QUFDNUQsRUFBRSxzREFBTztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXBocmFzaW5nL2xpYi9pbmRleC5qcz8xNGIwIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5QaHJhc2luZ0NvbnRlbnR9IFBocmFzaW5nQ29udGVudFxuICogQHR5cGVkZWYge2ltcG9ydCgndW5pc3QtdXRpbC1pcycpLkFzc2VydFByZWRpY2F0ZTxQaHJhc2luZ0NvbnRlbnQ+fSBBc3NlcnRQcmVkaWNhdGVQaHJhc2luZ1xuICovXG5cbmltcG9ydCB7Y29udmVydH0gZnJvbSAndW5pc3QtdXRpbC1pcydcblxuLyoqXG4gKiBDaGVjayBpZiB0aGUgZ2l2ZW4gdmFsdWUgaXMgKnBocmFzaW5nIGNvbnRlbnQqLlxuICpcbiAqIEBwYXJhbVxuICogICBUaGluZyB0byBjaGVjaywgdHlwaWNhbGx5IGBOb2RlYC5cbiAqIEByZXR1cm5zXG4gKiAgIFdoZXRoZXIgYHZhbHVlYCBpcyBwaHJhc2luZyBjb250ZW50LlxuICovXG5leHBvcnQgY29uc3QgcGhyYXNpbmcgPSAvKiogQHR5cGUge0Fzc2VydFByZWRpY2F0ZVBocmFzaW5nfSAqLyAoXG4gIGNvbnZlcnQoW1xuICAgICdicmVhaycsXG4gICAgJ2RlbGV0ZScsXG4gICAgJ2VtcGhhc2lzJyxcbiAgICAnZm9vdG5vdGUnLFxuICAgICdmb290bm90ZVJlZmVyZW5jZScsXG4gICAgJ2ltYWdlJyxcbiAgICAnaW1hZ2VSZWZlcmVuY2UnLFxuICAgICdpbmxpbmVDb2RlJyxcbiAgICAnbGluaycsXG4gICAgJ2xpbmtSZWZlcmVuY2UnLFxuICAgICdzdHJvbmcnLFxuICAgICd0ZXh0J1xuICBdKVxuKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/mdast-util-phrasing/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/mdast-util-to-string/lib/index.js":
/*!************************************************************!*\
  !*** ../../node_modules/mdast-util-to-string/lib/index.js ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toString: function() { return /* binding */ toString; }\n/* harmony export */ });\n/**\n * @typedef {import('mdast').Root|import('mdast').Content} Node\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [includeImageAlt=true]\n *   Whether to use `alt` for `image`s.\n * @property {boolean | null | undefined} [includeHtml=true]\n *   Whether to use `value` of HTML.\n */\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Get the text content of a node or list of nodes.\n *\n * Prefers the node’s plain-text fields, otherwise serializes its children,\n * and if the given value is an array, serialize the nodes in it.\n *\n * @param {unknown} value\n *   Thing to serialize, typically `Node`.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized `value`.\n */\nfunction toString(value, options) {\n  const settings = options || emptyOptions\n  const includeImageAlt =\n    typeof settings.includeImageAlt === 'boolean'\n      ? settings.includeImageAlt\n      : true\n  const includeHtml =\n    typeof settings.includeHtml === 'boolean' ? settings.includeHtml : true\n\n  return one(value, includeImageAlt, includeHtml)\n}\n\n/**\n * One node or several nodes.\n *\n * @param {unknown} value\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized node.\n */\nfunction one(value, includeImageAlt, includeHtml) {\n  if (node(value)) {\n    if ('value' in value) {\n      return value.type === 'html' && !includeHtml ? '' : value.value\n    }\n\n    if (includeImageAlt && 'alt' in value && value.alt) {\n      return value.alt\n    }\n\n    if ('children' in value) {\n      return all(value.children, includeImageAlt, includeHtml)\n    }\n  }\n\n  if (Array.isArray(value)) {\n    return all(value, includeImageAlt, includeHtml)\n  }\n\n  return ''\n}\n\n/**\n * Serialize a list of nodes.\n *\n * @param {Array<unknown>} values\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized nodes.\n */\nfunction all(values, includeImageAlt, includeHtml) {\n  /** @type {Array<string>} */\n  const result = []\n  let index = -1\n\n  while (++index < values.length) {\n    result[index] = one(values[index], includeImageAlt, includeHtml)\n  }\n\n  return result.join('')\n}\n\n/**\n * Check if `value` looks like a node.\n *\n * @param {unknown} value\n *   Thing.\n * @returns {value is Node}\n *   Whether `value` is a node.\n */\nfunction node(value) {\n  return Boolean(value && typeof value === 'object')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/mdast-util-to-string/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/rehype-minify-whitespace/block.js":
/*!************************************************************!*\
  !*** ../../node_modules/rehype-minify-whitespace/block.js ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blocks: function() { return /* binding */ blocks; }\n/* harmony export */ });\n// See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\nconst blocks = [\n  'address', // Flow content.\n  'article', // Sections and headings.\n  'aside', // Sections and headings.\n  'blockquote', // Flow content.\n  'body', // Page.\n  'br', // Contribute whitespace intrinsically.\n  'caption', // Similar to block.\n  'center', // Flow content, legacy.\n  'col', // Similar to block.\n  'colgroup', // Similar to block.\n  'dd', // Lists.\n  'dialog', // Flow content.\n  'dir', // Lists, legacy.\n  'div', // Flow content.\n  'dl', // Lists.\n  'dt', // Lists.\n  'figcaption', // Flow content.\n  'figure', // Flow content.\n  'footer', // Flow content.\n  'form', // Flow content.\n  'h1', // Sections and headings.\n  'h2', // Sections and headings.\n  'h3', // Sections and headings.\n  'h4', // Sections and headings.\n  'h5', // Sections and headings.\n  'h6', // Sections and headings.\n  'head', // Page.\n  'header', // Flow content.\n  'hgroup', // Sections and headings.\n  'hr', // Flow content.\n  'html', // Page.\n  'legend', // Flow content.\n  'li', // Block-like.\n  'li', // Similar to block.\n  'listing', // Flow content, legacy\n  'main', // Flow content.\n  'menu', // Lists.\n  'nav', // Sections and headings.\n  'ol', // Lists.\n  'optgroup', // Similar to block.\n  'option', // Similar to block.\n  'p', // Flow content.\n  'plaintext', // Flow content, legacy\n  'pre', // Flow content.\n  'section', // Sections and headings.\n  'summary', // Similar to block.\n  'table', // Similar to block.\n  'tbody', // Similar to block.\n  'td', // Block-like.\n  'td', // Similar to block.\n  'tfoot', // Similar to block.\n  'th', // Block-like.\n  'th', // Similar to block.\n  'thead', // Similar to block.\n  'tr', // Similar to block.\n  'ul', // Lists.\n  'wbr', // Contribute whitespace intrinsically.\n  'xmp' // Flow content, legacy\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/rehype-minify-whitespace/block.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/rehype-minify-whitespace/content.js":
/*!**************************************************************!*\
  !*** ../../node_modules/rehype-minify-whitespace/content.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   content: function() { return /* binding */ content; }\n/* harmony export */ });\nconst content = [\n  // Form.\n  'button',\n  'input',\n  'select',\n  'textarea'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvcmVoeXBlLW1pbmlmeS13aGl0ZXNwYWNlL2NvbnRlbnQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvcmVoeXBlLW1pbmlmeS13aGl0ZXNwYWNlL2NvbnRlbnQuanM/NjZmYSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgY29udGVudCA9IFtcbiAgLy8gRm9ybS5cbiAgJ2J1dHRvbicsXG4gICdpbnB1dCcsXG4gICdzZWxlY3QnLFxuICAndGV4dGFyZWEnXG5dXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/rehype-minify-whitespace/content.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/rehype-minify-whitespace/index.js":
/*!************************************************************!*\
  !*** ../../node_modules/rehype-minify-whitespace/index.js ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ rehypeMinifyWhitespace; }\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-is-element */ \"(app-pages-browser)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var hast_util_embedded__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-embedded */ \"(app-pages-browser)/../../node_modules/rehype-minify-whitespace/node_modules/hast-util-embedded/lib/index.js\");\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-is */ \"(app-pages-browser)/../../node_modules/unist-util-is/lib/index.js\");\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-whitespace */ \"(app-pages-browser)/../../node_modules/rehype-minify-whitespace/node_modules/hast-util-whitespace/index.js\");\n/* harmony import */ var _block_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./block.js */ \"(app-pages-browser)/../../node_modules/rehype-minify-whitespace/block.js\");\n/* harmony import */ var _content_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./content.js */ \"(app-pages-browser)/../../node_modules/rehype-minify-whitespace/content.js\");\n/* harmony import */ var _skippable_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./skippable.js */ \"(app-pages-browser)/../../node_modules/rehype-minify-whitespace/skippable.js\");\n/**\n * rehype plugin to minify whitespace between elements.\n *\n * ## What is this?\n *\n * This package is a plugin that can minify the whitespace between elements.\n *\n * ## When should I use this?\n *\n * You can use this plugin when you want to improve the size of HTML documents.\n *\n * ## API\n *\n * ### `unified().use(rehypeMinifyWhitespace[, options])`\n *\n * Minify whitespace.\n *\n * ##### `options`\n *\n * Configuration (optional).\n *\n * ##### `options.newlines`\n *\n * Whether to collapse runs of whitespace that include line endings to one\n * line ending (`boolean`, default: `false`).\n * The default is to collapse everything to one space.\n *\n * @example\n *   <h1>Heading</h1>\n *   <p><strong>This</strong> and <em>that</em></p>\n */\n\n/**\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {Root|Root['children'][number]} Node\n *\n * @typedef Options\n * @property {boolean} [newlines=false]\n *   If `newlines: true`, collapses whitespace containing newlines to `'\\n'`\n *   instead of `' '`.\n *   The default is to collapse to a single space.\n *\n * @typedef {'pre'|'nowrap'|'pre-wrap'|'normal'} Whitespace\n *\n * @typedef Context\n * @property {ReturnType<collapseFactory>} collapse\n * @property {Whitespace} whitespace\n * @property {boolean} [before]\n * @property {boolean} [after]\n *\n * @typedef Result\n * @property {boolean} remove\n * @property {boolean} ignore\n * @property {boolean} stripAtStart\n */\n\n\n\n\n\n\n\n\n\nconst ignorableNode = (0,unist_util_is__WEBPACK_IMPORTED_MODULE_0__.convert)(['doctype', 'comment'])\n\n/**\n * Minify whitespace.\n *\n * @type {import('unified').Plugin<[Options?]|Array<void>, Root>}\n */\nfunction rehypeMinifyWhitespace(options = {}) {\n  const collapse = collapseFactory(\n    options.newlines ? replaceNewlines : replaceWhitespace\n  )\n\n  return (tree) => {\n    minify(tree, {collapse, whitespace: 'normal'})\n  }\n}\n\n/**\n * @param {Node} node\n * @param {Context} context\n * @returns {Result}\n */\nfunction minify(node, context) {\n  if ('children' in node) {\n    const settings = Object.assign({}, context)\n\n    if (node.type === 'root' || blocklike(node)) {\n      settings.before = true\n      settings.after = true\n    }\n\n    settings.whitespace = inferWhiteSpace(node, context)\n\n    return all(node, settings)\n  }\n\n  if (node.type === 'text') {\n    if (context.whitespace === 'normal') {\n      return minifyText(node, context)\n    }\n\n    // Naïve collapse, but no trimming:\n    if (context.whitespace === 'nowrap') {\n      node.value = context.collapse(node.value)\n    }\n\n    // The `pre-wrap` or `pre` whitespace settings are neither collapsed nor\n    // trimmed.\n  }\n\n  return {remove: false, ignore: ignorableNode(node), stripAtStart: false}\n}\n\n/**\n * @param {Text} node\n * @param {Context} context\n * @returns {Result}\n */\nfunction minifyText(node, context) {\n  const value = context.collapse(node.value)\n  const result = {remove: false, ignore: false, stripAtStart: false}\n  let start = 0\n  let end = value.length\n\n  if (context.before && removable(value.charAt(0))) {\n    start++\n  }\n\n  if (start !== end && removable(value.charAt(end - 1))) {\n    if (context.after) {\n      end--\n    } else {\n      result.stripAtStart = true\n    }\n  }\n\n  if (start === end) {\n    result.remove = true\n  } else {\n    node.value = value.slice(start, end)\n  }\n\n  return result\n}\n\n/**\n * @param {Root|Element} parent\n * @param {Context} context\n * @returns {Result}\n */\nfunction all(parent, context) {\n  let before = context.before\n  const after = context.after\n  const children = parent.children\n  let length = children.length\n  let index = -1\n\n  while (++index < length) {\n    const result = minify(\n      children[index],\n      Object.assign({}, context, {\n        before,\n        after: collapsableAfter(children, index, after)\n      })\n    )\n\n    if (result.remove) {\n      children.splice(index, 1)\n      index--\n      length--\n    } else if (!result.ignore) {\n      before = result.stripAtStart\n    }\n\n    // If this element, such as a `<select>` or `<img>`, contributes content\n    // somehow, allow whitespace again.\n    if (content(children[index])) {\n      before = false\n    }\n  }\n\n  return {remove: false, ignore: false, stripAtStart: Boolean(before || after)}\n}\n\n/**\n * @param {Array<Node>} nodes\n * @param {number} index\n * @param {boolean|undefined} [after]\n * @returns {boolean|undefined}\n */\nfunction collapsableAfter(nodes, index, after) {\n  while (++index < nodes.length) {\n    const node = nodes[index]\n    let result = inferBoundary(node)\n\n    if (result === undefined && 'children' in node && !skippable(node)) {\n      result = collapsableAfter(node.children, -1)\n    }\n\n    if (typeof result === 'boolean') {\n      return result\n    }\n  }\n\n  return after\n}\n\n/**\n * Infer two types of boundaries:\n *\n * 1. `true` — boundary for which whitespace around it does not contribute\n *    anything\n * 2. `false` — boundary for which whitespace around it *does* contribute\n *\n * No result (`undefined`) is returned if it is unknown.\n *\n * @param {Node} node\n * @returns {boolean|undefined}\n */\nfunction inferBoundary(node) {\n  if (node.type === 'element') {\n    if (content(node)) {\n      return false\n    }\n\n    if (blocklike(node)) {\n      return true\n    }\n\n    // Unknown: either depends on siblings if embedded or metadata, or on\n    // children.\n  } else if (node.type === 'text') {\n    if (!(0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_1__.whitespace)(node)) {\n      return false\n    }\n  } else if (!ignorableNode(node)) {\n    return false\n  }\n}\n\n/**\n * Infer whether a node is skippable.\n *\n * @param {Node} node\n * @returns {boolean}\n */\nfunction content(node) {\n  return (0,hast_util_embedded__WEBPACK_IMPORTED_MODULE_2__.embedded)(node) || (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__.isElement)(node, _content_js__WEBPACK_IMPORTED_MODULE_4__.content)\n}\n\n/**\n * See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\n *\n * @param {Element} node\n * @returns {boolean}\n */\nfunction blocklike(node) {\n  return (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__.isElement)(node, _block_js__WEBPACK_IMPORTED_MODULE_5__.blocks)\n}\n\n/**\n * @param {Element|Root} node\n * @returns {boolean}\n */\nfunction skippable(node) {\n  return (\n    Boolean(\n      'properties' in node && node.properties && node.properties.hidden\n    ) ||\n    ignorableNode(node) ||\n    (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__.isElement)(node, _skippable_js__WEBPACK_IMPORTED_MODULE_6__.skippable)\n  )\n}\n\n/**\n * @param {string} character\n * @returns {boolean}\n */\nfunction removable(character) {\n  return character === ' ' || character === '\\n'\n}\n\n/**\n * @param {string} value\n * @returns {string}\n */\nfunction replaceNewlines(value) {\n  const match = /\\r?\\n|\\r/.exec(value)\n  return match ? match[0] : ' '\n}\n\n/**\n * @returns {string}\n */\nfunction replaceWhitespace() {\n  return ' '\n}\n\n/**\n * @param {(value: string) => string} replace\n */\nfunction collapseFactory(replace) {\n  return collapse\n\n  /**\n   * @param {string} value\n   * @returns {string}\n   */\n  function collapse(value) {\n    return String(value).replace(/[\\t\\n\\v\\f\\r ]+/g, replace)\n  }\n}\n\n/**\n * We don’t need to support void elements here (so `nobr wbr` -> `normal` is\n * ignored).\n *\n * @param {Root|Element} node\n * @param {Context} context\n * @returns {Whitespace}\n */\nfunction inferWhiteSpace(node, context) {\n  if ('tagName' in node && node.properties) {\n    switch (node.tagName) {\n      // Whitespace in script/style, while not displayed by CSS as significant,\n      // could have some meaning in JS/CSS, so we can’t touch them.\n      case 'listing':\n      case 'plaintext':\n      case 'script':\n      case 'style':\n      case 'xmp':\n        return 'pre'\n      case 'nobr':\n        return 'nowrap'\n      case 'pre':\n        return node.properties.wrap ? 'pre-wrap' : 'pre'\n      case 'td':\n      case 'th':\n        return node.properties.noWrap ? 'nowrap' : context.whitespace\n      case 'textarea':\n        return 'pre-wrap'\n      default:\n    }\n  }\n\n  return context.whitespace\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/rehype-minify-whitespace/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/rehype-minify-whitespace/node_modules/hast-util-embedded/lib/index.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/rehype-minify-whitespace/node_modules/hast-util-embedded/lib/index.js ***!
  \************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   embedded: function() { return /* binding */ embedded; }\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(app-pages-browser)/../../node_modules/hast-util-is-element/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n */\n\n\n\n/**\n * Check if a node is a *embedded content*.\n *\n * @type {import('hast-util-is-element').AssertPredicate<Element & {tagName: 'audio' | 'canvas' | 'embed' | 'iframe' | 'img' | 'math' | 'object' | 'picture' | 'svg' | 'video'}>}\n * @param value\n *   Thing to check (typically `Node`).\n * @returns\n *   Whether `value` is an element considered embedded content.\n *\n *   The elements `audio`, `canvas`, `embed`, `iframe`, `img`, `math`,\n *   `object`, `picture`, `svg`, and `video` are embedded content.\n */\n// @ts-expect-error Sure, the assertion matches.\nconst embedded = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  'audio',\n  'canvas',\n  'embed',\n  'iframe',\n  'img',\n  'math',\n  'object',\n  'picture',\n  'svg',\n  'video'\n])\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvcmVoeXBlLW1pbmlmeS13aGl0ZXNwYWNlL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtZW1iZWRkZWQvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLHdCQUF3QjtBQUNyQzs7QUFFbUQ7O0FBRW5EO0FBQ0E7QUFDQTtBQUNBLFVBQVUsMERBQTBELDJHQUEyRztBQUMvSztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTyxpQkFBaUIsb0VBQWM7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL3JlaHlwZS1taW5pZnktd2hpdGVzcGFjZS9ub2RlX21vZHVsZXMvaGFzdC11dGlsLWVtYmVkZGVkL2xpYi9pbmRleC5qcz9lN2FjIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqL1xuXG5pbXBvcnQge2NvbnZlcnRFbGVtZW50fSBmcm9tICdoYXN0LXV0aWwtaXMtZWxlbWVudCdcblxuLyoqXG4gKiBDaGVjayBpZiBhIG5vZGUgaXMgYSAqZW1iZWRkZWQgY29udGVudCouXG4gKlxuICogQHR5cGUge2ltcG9ydCgnaGFzdC11dGlsLWlzLWVsZW1lbnQnKS5Bc3NlcnRQcmVkaWNhdGU8RWxlbWVudCAmIHt0YWdOYW1lOiAnYXVkaW8nIHwgJ2NhbnZhcycgfCAnZW1iZWQnIHwgJ2lmcmFtZScgfCAnaW1nJyB8ICdtYXRoJyB8ICdvYmplY3QnIHwgJ3BpY3R1cmUnIHwgJ3N2ZycgfCAndmlkZW8nfT59XG4gKiBAcGFyYW0gdmFsdWVcbiAqICAgVGhpbmcgdG8gY2hlY2sgKHR5cGljYWxseSBgTm9kZWApLlxuICogQHJldHVybnNcbiAqICAgV2hldGhlciBgdmFsdWVgIGlzIGFuIGVsZW1lbnQgY29uc2lkZXJlZCBlbWJlZGRlZCBjb250ZW50LlxuICpcbiAqICAgVGhlIGVsZW1lbnRzIGBhdWRpb2AsIGBjYW52YXNgLCBgZW1iZWRgLCBgaWZyYW1lYCwgYGltZ2AsIGBtYXRoYCxcbiAqICAgYG9iamVjdGAsIGBwaWN0dXJlYCwgYHN2Z2AsIGFuZCBgdmlkZW9gIGFyZSBlbWJlZGRlZCBjb250ZW50LlxuICovXG4vLyBAdHMtZXhwZWN0LWVycm9yIFN1cmUsIHRoZSBhc3NlcnRpb24gbWF0Y2hlcy5cbmV4cG9ydCBjb25zdCBlbWJlZGRlZCA9IGNvbnZlcnRFbGVtZW50KFtcbiAgJ2F1ZGlvJyxcbiAgJ2NhbnZhcycsXG4gICdlbWJlZCcsXG4gICdpZnJhbWUnLFxuICAnaW1nJyxcbiAgJ21hdGgnLFxuICAnb2JqZWN0JyxcbiAgJ3BpY3R1cmUnLFxuICAnc3ZnJyxcbiAgJ3ZpZGVvJ1xuXSlcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/rehype-minify-whitespace/node_modules/hast-util-embedded/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/rehype-minify-whitespace/node_modules/hast-util-whitespace/index.js":
/*!**********************************************************************************************!*\
  !*** ../../node_modules/rehype-minify-whitespace/node_modules/hast-util-whitespace/index.js ***!
  \**********************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   whitespace: function() { return /* binding */ whitespace; }\n/* harmony export */ });\n/**\n * Check if the given value is *inter-element whitespace*.\n *\n * @param {unknown} thing\n *   Thing to check (typically `Node` or `string`).\n * @returns {boolean}\n *   Whether the `value` is inter-element whitespace (`boolean`): consisting of\n *   zero or more of space, tab (`\\t`), line feed (`\\n`), carriage return\n *   (`\\r`), or form feed (`\\f`).\n *   If a node is passed it must be a `Text` node, whose `value` field is\n *   checked.\n */\nfunction whitespace(thing) {\n  /** @type {string} */\n  const value =\n    // @ts-expect-error looks like a node.\n    thing && typeof thing === 'object' && thing.type === 'text'\n      ? // @ts-expect-error looks like a text.\n        thing.value || ''\n      : thing\n\n  // HTML whitespace expression.\n  // See <https://infra.spec.whatwg.org/#ascii-whitespace>.\n  return typeof value === 'string' && value.replace(/[ \\t\\n\\f\\r]/g, '') === ''\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvcmVoeXBlLW1pbmlmeS13aGl0ZXNwYWNlL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtd2hpdGVzcGFjZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsYUFBYSxRQUFRO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL3JlaHlwZS1taW5pZnktd2hpdGVzcGFjZS9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXdoaXRlc3BhY2UvaW5kZXguanM/ZTdiMSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENoZWNrIGlmIHRoZSBnaXZlbiB2YWx1ZSBpcyAqaW50ZXItZWxlbWVudCB3aGl0ZXNwYWNlKi5cbiAqXG4gKiBAcGFyYW0ge3Vua25vd259IHRoaW5nXG4gKiAgIFRoaW5nIHRvIGNoZWNrICh0eXBpY2FsbHkgYE5vZGVgIG9yIGBzdHJpbmdgKS5cbiAqIEByZXR1cm5zIHtib29sZWFufVxuICogICBXaGV0aGVyIHRoZSBgdmFsdWVgIGlzIGludGVyLWVsZW1lbnQgd2hpdGVzcGFjZSAoYGJvb2xlYW5gKTogY29uc2lzdGluZyBvZlxuICogICB6ZXJvIG9yIG1vcmUgb2Ygc3BhY2UsIHRhYiAoYFxcdGApLCBsaW5lIGZlZWQgKGBcXG5gKSwgY2FycmlhZ2UgcmV0dXJuXG4gKiAgIChgXFxyYCksIG9yIGZvcm0gZmVlZCAoYFxcZmApLlxuICogICBJZiBhIG5vZGUgaXMgcGFzc2VkIGl0IG11c3QgYmUgYSBgVGV4dGAgbm9kZSwgd2hvc2UgYHZhbHVlYCBmaWVsZCBpc1xuICogICBjaGVja2VkLlxuICovXG5leHBvcnQgZnVuY3Rpb24gd2hpdGVzcGFjZSh0aGluZykge1xuICAvKiogQHR5cGUge3N0cmluZ30gKi9cbiAgY29uc3QgdmFsdWUgPVxuICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgbG9va3MgbGlrZSBhIG5vZGUuXG4gICAgdGhpbmcgJiYgdHlwZW9mIHRoaW5nID09PSAnb2JqZWN0JyAmJiB0aGluZy50eXBlID09PSAndGV4dCdcbiAgICAgID8gLy8gQHRzLWV4cGVjdC1lcnJvciBsb29rcyBsaWtlIGEgdGV4dC5cbiAgICAgICAgdGhpbmcudmFsdWUgfHwgJydcbiAgICAgIDogdGhpbmdcblxuICAvLyBIVE1MIHdoaXRlc3BhY2UgZXhwcmVzc2lvbi5cbiAgLy8gU2VlIDxodHRwczovL2luZnJhLnNwZWMud2hhdHdnLm9yZy8jYXNjaWktd2hpdGVzcGFjZT4uXG4gIHJldHVybiB0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnICYmIHZhbHVlLnJlcGxhY2UoL1sgXFx0XFxuXFxmXFxyXS9nLCAnJykgPT09ICcnXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/rehype-minify-whitespace/node_modules/hast-util-whitespace/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/rehype-minify-whitespace/skippable.js":
/*!****************************************************************!*\
  !*** ../../node_modules/rehype-minify-whitespace/skippable.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   skippable: function() { return /* binding */ skippable; }\n/* harmony export */ });\nconst skippable = [\n  'area',\n  'base',\n  'basefont',\n  'dialog',\n  'datalist',\n  'head',\n  'link',\n  'meta',\n  'noembed',\n  'noframes',\n  'param',\n  'rp',\n  'script',\n  'source',\n  'style',\n  'template',\n  'track',\n  'title'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvcmVoeXBlLW1pbmlmeS13aGl0ZXNwYWNlL3NraXBwYWJsZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL3JlaHlwZS1taW5pZnktd2hpdGVzcGFjZS9za2lwcGFibGUuanM/NDliMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3Qgc2tpcHBhYmxlID0gW1xuICAnYXJlYScsXG4gICdiYXNlJyxcbiAgJ2Jhc2Vmb250JyxcbiAgJ2RpYWxvZycsXG4gICdkYXRhbGlzdCcsXG4gICdoZWFkJyxcbiAgJ2xpbmsnLFxuICAnbWV0YScsXG4gICdub2VtYmVkJyxcbiAgJ25vZnJhbWVzJyxcbiAgJ3BhcmFtJyxcbiAgJ3JwJyxcbiAgJ3NjcmlwdCcsXG4gICdzb3VyY2UnLFxuICAnc3R5bGUnLFxuICAndGVtcGxhdGUnLFxuICAndHJhY2snLFxuICAndGl0bGUnXG5dXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/rehype-minify-whitespace/skippable.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/rehype-remark/index.js":
/*!*************************************************!*\
  !*** ../../node_modules/rehype-remark/index.js ***!
  \*************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   all: function() { return /* reexport safe */ hast_util_to_mdast__WEBPACK_IMPORTED_MODULE_1__.all; },\n/* harmony export */   \"default\": function() { return /* reexport safe */ _lib_index_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; },\n/* harmony export */   defaultHandlers: function() { return /* reexport safe */ hast_util_to_mdast__WEBPACK_IMPORTED_MODULE_0__.handlers; },\n/* harmony export */   one: function() { return /* reexport safe */ hast_util_to_mdast__WEBPACK_IMPORTED_MODULE_2__.one; }\n/* harmony export */ });\n/* harmony import */ var hast_util_to_mdast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-to-mdast */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/handlers/index.js\");\n/* harmony import */ var hast_util_to_mdast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-to-mdast */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/* harmony import */ var hast_util_to_mdast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-to-mdast */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/one.js\");\n/* harmony import */ var _lib_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/index.js */ \"(app-pages-browser)/../../node_modules/rehype-remark/lib/index.js\");\n/**\n * @typedef {import('hast-util-to-mdast').Context} Context\n * @typedef {import('hast-util-to-mdast').H} H\n * @typedef {import('hast-util-to-mdast').Handle} Handle\n * @typedef {import('./lib/index.js').Options} Options\n * @typedef {import('./lib/index.js').Processor} Processor\n */\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvcmVoeXBlLXJlbWFyay9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBO0FBQ0EsYUFBYSxzQ0FBc0M7QUFDbkQsYUFBYSxnQ0FBZ0M7QUFDN0MsYUFBYSxxQ0FBcUM7QUFDbEQsYUFBYSxrQ0FBa0M7QUFDL0MsYUFBYSxvQ0FBb0M7QUFDakQ7O0FBRTREO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvcmVoeXBlLXJlbWFyay9pbmRleC5qcz9mMzRiIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdC11dGlsLXRvLW1kYXN0JykuQ29udGV4dH0gQ29udGV4dFxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdC11dGlsLXRvLW1kYXN0JykuSH0gSFxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdC11dGlsLXRvLW1kYXN0JykuSGFuZGxlfSBIYW5kbGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4vbGliL2luZGV4LmpzJykuT3B0aW9uc30gT3B0aW9uc1xuICogQHR5cGVkZWYge2ltcG9ydCgnLi9saWIvaW5kZXguanMnKS5Qcm9jZXNzb3J9IFByb2Nlc3NvclxuICovXG5cbmV4cG9ydCB7ZGVmYXVsdEhhbmRsZXJzLCBhbGwsIG9uZX0gZnJvbSAnaGFzdC11dGlsLXRvLW1kYXN0J1xuZXhwb3J0IHtkZWZhdWx0fSBmcm9tICcuL2xpYi9pbmRleC5qcydcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/rehype-remark/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/rehype-remark/lib/index.js":
/*!*****************************************************!*\
  !*** ../../node_modules/rehype-remark/lib/index.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var hast_util_to_mdast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-to-mdast */ \"(app-pages-browser)/../../node_modules/hast-util-to-mdast/lib/index.js\");\n/**\n * @typedef {import('hast-util-to-mdast').Options} Options\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('mdast').Root} MdastRoot\n * @typedef {import('unified').Processor<any, any, any, any>} Processor\n */\n\n\n\n/**\n * Plugin to bridge or mutate to rehype.\n *\n * If a destination is given, runs the destination with the new mdast\n * tree (bridge-mode).\n * Without destination, returns the mdast tree: further plugins run on that\n * tree (mutate-mode).\n *\n * @param destination\n *   Optional unified processor.\n * @param options\n *   Options passed to `hast-util-to-mdast`.\n */\nconst rehypeRemark =\n  /**\n   * @type {(import('unified').Plugin<[Processor, Options?], HastRoot> & import('unified').Plugin<[Options?]|void[], HastRoot, MdastRoot>)}\n   */\n  (\n    /**\n     * @param {Processor|Options} [destination]\n     * @param {Options} [options]\n     */\n    function (destination, options) {\n      /** @type {Options|undefined} */\n      let settings\n      /** @type {Processor|undefined} */\n      let processor\n\n      if (typeof destination === 'function') {\n        processor = destination\n        settings = options || {}\n      } else {\n        settings = destination || {}\n      }\n\n      if (settings.document === undefined || settings.document === null) {\n        settings = Object.assign({}, settings, {document: true})\n      }\n\n      return processor ? bridge(processor, settings) : mutate(settings)\n    }\n  )\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (rehypeRemark);\n\n/**\n * Bridge-mode.\n * Runs the destination with the new mdast tree.\n *\n * @type {import('unified').Plugin<[Processor, Options?], HastRoot>}\n */\nfunction bridge(destination, options) {\n  return (node, file, next) => {\n    destination.run((0,hast_util_to_mdast__WEBPACK_IMPORTED_MODULE_0__.toMdast)(node, options), file, (error) => {\n      next(error)\n    })\n  }\n}\n\n/**\n * Mutate-mode.\n * Further transformers run on the mdast tree.\n *\n * @type {import('unified').Plugin<[Options?]|void[], HastRoot, MdastRoot>}\n */\nfunction mutate(options = {}) {\n  return (node) => {\n    const result = /** @type {MdastRoot} */ ((0,hast_util_to_mdast__WEBPACK_IMPORTED_MODULE_0__.toMdast)(node, options))\n    return result\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/rehype-remark/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/trim-trailing-lines/index.js":
/*!*******************************************************!*\
  !*** ../../node_modules/trim-trailing-lines/index.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   trimTrailingLines: function() { return /* binding */ trimTrailingLines; }\n/* harmony export */ });\n/**\n * Remove final line endings from `value`\n *\n * @param {unknown} value\n *   Value with trailing line endings, coerced to string.\n * @return {string}\n *   Value without trailing line endings.\n */\nfunction trimTrailingLines(value) {\n  const input = String(value)\n  let end = input.length\n\n  while (end > 0) {\n    const code = input.codePointAt(end - 1)\n    if (code !== undefined && (code === 10 || code === 13)) {\n      end--\n    } else {\n      break\n    }\n  }\n\n  return input.slice(0, end)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvdHJpbS10cmFpbGluZy1saW5lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDTztBQUNQO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy90cmltLXRyYWlsaW5nLWxpbmVzL2luZGV4LmpzP2JmYTAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBSZW1vdmUgZmluYWwgbGluZSBlbmRpbmdzIGZyb20gYHZhbHVlYFxuICpcbiAqIEBwYXJhbSB7dW5rbm93bn0gdmFsdWVcbiAqICAgVmFsdWUgd2l0aCB0cmFpbGluZyBsaW5lIGVuZGluZ3MsIGNvZXJjZWQgdG8gc3RyaW5nLlxuICogQHJldHVybiB7c3RyaW5nfVxuICogICBWYWx1ZSB3aXRob3V0IHRyYWlsaW5nIGxpbmUgZW5kaW5ncy5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHRyaW1UcmFpbGluZ0xpbmVzKHZhbHVlKSB7XG4gIGNvbnN0IGlucHV0ID0gU3RyaW5nKHZhbHVlKVxuICBsZXQgZW5kID0gaW5wdXQubGVuZ3RoXG5cbiAgd2hpbGUgKGVuZCA+IDApIHtcbiAgICBjb25zdCBjb2RlID0gaW5wdXQuY29kZVBvaW50QXQoZW5kIC0gMSlcbiAgICBpZiAoY29kZSAhPT0gdW5kZWZpbmVkICYmIChjb2RlID09PSAxMCB8fCBjb2RlID09PSAxMykpIHtcbiAgICAgIGVuZC0tXG4gICAgfSBlbHNlIHtcbiAgICAgIGJyZWFrXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIGlucHV0LnNsaWNlKDAsIGVuZClcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/trim-trailing-lines/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/unist-util-is/lib/index.js":
/*!*****************************************************!*\
  !*** ../../node_modules/unist-util-is/lib/index.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convert: function() { return /* binding */ convert; },\n/* harmony export */   is: function() { return /* binding */ is; }\n/* harmony export */ });\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n */\n\n/**\n * @typedef {Record<string, unknown>} Props\n * @typedef {null | undefined | string | Props | TestFunctionAnything | Array<string | Props | TestFunctionAnything>} Test\n *   Check for an arbitrary node, unaware of TypeScript inferral.\n *\n * @callback TestFunctionAnything\n *   Check if a node passes a test, unaware of TypeScript inferral.\n * @param {unknown} this\n *   The given context.\n * @param {Node} node\n *   A node.\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean | void}\n *   Whether this node passes the test.\n */\n\n/**\n * @template {Node} Kind\n *   Node type.\n * @typedef {Kind['type'] | Partial<Kind> | TestFunctionPredicate<Kind> | Array<Kind['type'] | Partial<Kind> | TestFunctionPredicate<Kind>>} PredicateTest\n *   Check for a node that can be inferred by TypeScript.\n */\n\n/**\n * Check if a node passes a certain test.\n *\n * @template {Node} Kind\n *   Node type.\n * @callback TestFunctionPredicate\n *   Complex test function for a node that can be inferred by TypeScript.\n * @param {Node} node\n *   A node.\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {node is Kind}\n *   Whether this node passes the test.\n */\n\n/**\n * @callback AssertAnything\n *   Check that an arbitrary value is a node, unaware of TypeScript inferral.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is a node and passes a test.\n */\n\n/**\n * Check if a node is a node and passes a certain node test.\n *\n * @template {Node} Kind\n *   Node type.\n * @callback AssertPredicate\n *   Check that an arbitrary value is a specific node, aware of TypeScript.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {node is Kind}\n *   Whether this is a node and passes a test.\n */\n\n/**\n * Check if `node` is a `Node` and whether it passes the given test.\n *\n * @param node\n *   Thing to check, typically `Node`.\n * @param test\n *   A check for a specific node.\n * @param index\n *   The node’s position in its parent.\n * @param parent\n *   The node’s parent.\n * @returns\n *   Whether `node` is a node and passes a test.\n */\nconst is =\n  /**\n   * @type {(\n   *   (() => false) &\n   *   (<Kind extends Node = Node>(node: unknown, test: PredicateTest<Kind>, index: number, parent: Parent, context?: unknown) => node is Kind) &\n   *   (<Kind extends Node = Node>(node: unknown, test: PredicateTest<Kind>, index?: null | undefined, parent?: null | undefined, context?: unknown) => node is Kind) &\n   *   ((node: unknown, test: Test, index: number, parent: Parent, context?: unknown) => boolean) &\n   *   ((node: unknown, test?: Test, index?: null | undefined, parent?: null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [node]\n     * @param {Test} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parent | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function is(node, test, index, parent, context) {\n      const check = convert(test)\n\n      if (\n        index !== undefined &&\n        index !== null &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite index')\n      }\n\n      if (\n        parent !== undefined &&\n        parent !== null &&\n        (!is(parent) || !parent.children)\n      ) {\n        throw new Error('Expected parent node')\n      }\n\n      if (\n        (parent === undefined || parent === null) !==\n        (index === undefined || index === null)\n      ) {\n        throw new Error('Expected both parent and index')\n      }\n\n      // @ts-expect-error Looks like a node.\n      return node && node.type && typeof node.type === 'string'\n        ? Boolean(check.call(context, node, index, parent))\n        : false\n    }\n  )\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param test\n *   *   when nullish, checks if `node` is a `Node`.\n *   *   when `string`, works like passing `(node) => node.type === test`.\n *   *   when `function` checks if function passed the node is true.\n *   *   when `object`, checks that all keys in test are in node, and that they have (strictly) equal values.\n *   *   when `array`, checks if any one of the subtests pass.\n * @returns\n *   An assertion.\n */\nconst convert =\n  /**\n   * @type {(\n   *   (<Kind extends Node>(test: PredicateTest<Kind>) => AssertPredicate<Kind>) &\n   *   ((test?: Test) => AssertAnything)\n   * )}\n   */\n  (\n    /**\n     * @param {Test} [test]\n     * @returns {AssertAnything}\n     */\n    function (test) {\n      if (test === undefined || test === null) {\n        return ok\n      }\n\n      if (typeof test === 'string') {\n        return typeFactory(test)\n      }\n\n      if (typeof test === 'object') {\n        return Array.isArray(test) ? anyFactory(test) : propsFactory(test)\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      throw new Error('Expected function, string, or object as test')\n    }\n  )\n\n/**\n * @param {Array<string | Props | TestFunctionAnything>} tests\n * @returns {AssertAnything}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<AssertAnything>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convert(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].call(this, ...parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn an object into a test for a node with a certain fields.\n *\n * @param {Props} check\n * @returns {AssertAnything}\n */\nfunction propsFactory(check) {\n  return castFactory(all)\n\n  /**\n   * @param {Node} node\n   * @returns {boolean}\n   */\n  function all(node) {\n    /** @type {string} */\n    let key\n\n    for (key in check) {\n      // @ts-expect-error: hush, it sure works as an index.\n      if (node[key] !== check[key]) return false\n    }\n\n    return true\n  }\n}\n\n/**\n * Turn a string into a test for a node with a certain type.\n *\n * @param {string} check\n * @returns {AssertAnything}\n */\nfunction typeFactory(check) {\n  return castFactory(type)\n\n  /**\n   * @param {Node} node\n   */\n  function type(node) {\n    return node && node.type === check\n  }\n}\n\n/**\n * Turn a custom test into a test for a node that passes that test.\n *\n * @param {TestFunctionAnything} check\n * @returns {AssertAnything}\n */\nfunction castFactory(check) {\n  return assertion\n\n  /**\n   * @this {unknown}\n   * @param {unknown} node\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function assertion(node, ...parameters) {\n    return Boolean(\n      node &&\n        typeof node === 'object' &&\n        'type' in node &&\n        // @ts-expect-error: fine.\n        Boolean(check.call(this, node, ...parameters))\n    )\n  }\n}\n\nfunction ok() {\n  return true\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/unist-util-is/lib/index.js\n"));

/***/ })

}]);