"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_rehype-stringify_index_js"],{

/***/ "(app-pages-browser)/../../node_modules/character-entities-html4/index.js":
/*!************************************************************!*\
  !*** ../../node_modules/character-entities-html4/index.js ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   characterEntitiesHtml4: function() { return /* binding */ characterEntitiesHtml4; }\n/* harmony export */ });\n/**\n * Map of named character references from HTML 4.\n *\n * @type {Record<string, string>}\n */\nconst characterEntitiesHtml4 = {\n  nbsp: ' ',\n  iexcl: '¡',\n  cent: '¢',\n  pound: '£',\n  curren: '¤',\n  yen: '¥',\n  brvbar: '¦',\n  sect: '§',\n  uml: '¨',\n  copy: '©',\n  ordf: 'ª',\n  laquo: '«',\n  not: '¬',\n  shy: '­',\n  reg: '®',\n  macr: '¯',\n  deg: '°',\n  plusmn: '±',\n  sup2: '²',\n  sup3: '³',\n  acute: '´',\n  micro: 'µ',\n  para: '¶',\n  middot: '·',\n  cedil: '¸',\n  sup1: '¹',\n  ordm: 'º',\n  raquo: '»',\n  frac14: '¼',\n  frac12: '½',\n  frac34: '¾',\n  iquest: '¿',\n  Agrave: 'À',\n  Aacute: 'Á',\n  Acirc: 'Â',\n  Atilde: 'Ã',\n  Auml: 'Ä',\n  Aring: 'Å',\n  AElig: 'Æ',\n  Ccedil: 'Ç',\n  Egrave: 'È',\n  Eacute: 'É',\n  Ecirc: 'Ê',\n  Euml: 'Ë',\n  Igrave: 'Ì',\n  Iacute: 'Í',\n  Icirc: 'Î',\n  Iuml: 'Ï',\n  ETH: 'Ð',\n  Ntilde: 'Ñ',\n  Ograve: 'Ò',\n  Oacute: 'Ó',\n  Ocirc: 'Ô',\n  Otilde: 'Õ',\n  Ouml: 'Ö',\n  times: '×',\n  Oslash: 'Ø',\n  Ugrave: 'Ù',\n  Uacute: 'Ú',\n  Ucirc: 'Û',\n  Uuml: 'Ü',\n  Yacute: 'Ý',\n  THORN: 'Þ',\n  szlig: 'ß',\n  agrave: 'à',\n  aacute: 'á',\n  acirc: 'â',\n  atilde: 'ã',\n  auml: 'ä',\n  aring: 'å',\n  aelig: 'æ',\n  ccedil: 'ç',\n  egrave: 'è',\n  eacute: 'é',\n  ecirc: 'ê',\n  euml: 'ë',\n  igrave: 'ì',\n  iacute: 'í',\n  icirc: 'î',\n  iuml: 'ï',\n  eth: 'ð',\n  ntilde: 'ñ',\n  ograve: 'ò',\n  oacute: 'ó',\n  ocirc: 'ô',\n  otilde: 'õ',\n  ouml: 'ö',\n  divide: '÷',\n  oslash: 'ø',\n  ugrave: 'ù',\n  uacute: 'ú',\n  ucirc: 'û',\n  uuml: 'ü',\n  yacute: 'ý',\n  thorn: 'þ',\n  yuml: 'ÿ',\n  fnof: 'ƒ',\n  Alpha: 'Α',\n  Beta: 'Β',\n  Gamma: 'Γ',\n  Delta: 'Δ',\n  Epsilon: 'Ε',\n  Zeta: 'Ζ',\n  Eta: 'Η',\n  Theta: 'Θ',\n  Iota: 'Ι',\n  Kappa: 'Κ',\n  Lambda: 'Λ',\n  Mu: 'Μ',\n  Nu: 'Ν',\n  Xi: 'Ξ',\n  Omicron: 'Ο',\n  Pi: 'Π',\n  Rho: 'Ρ',\n  Sigma: 'Σ',\n  Tau: 'Τ',\n  Upsilon: 'Υ',\n  Phi: 'Φ',\n  Chi: 'Χ',\n  Psi: 'Ψ',\n  Omega: 'Ω',\n  alpha: 'α',\n  beta: 'β',\n  gamma: 'γ',\n  delta: 'δ',\n  epsilon: 'ε',\n  zeta: 'ζ',\n  eta: 'η',\n  theta: 'θ',\n  iota: 'ι',\n  kappa: 'κ',\n  lambda: 'λ',\n  mu: 'μ',\n  nu: 'ν',\n  xi: 'ξ',\n  omicron: 'ο',\n  pi: 'π',\n  rho: 'ρ',\n  sigmaf: 'ς',\n  sigma: 'σ',\n  tau: 'τ',\n  upsilon: 'υ',\n  phi: 'φ',\n  chi: 'χ',\n  psi: 'ψ',\n  omega: 'ω',\n  thetasym: 'ϑ',\n  upsih: 'ϒ',\n  piv: 'ϖ',\n  bull: '•',\n  hellip: '…',\n  prime: '′',\n  Prime: '″',\n  oline: '‾',\n  frasl: '⁄',\n  weierp: '℘',\n  image: 'ℑ',\n  real: 'ℜ',\n  trade: '™',\n  alefsym: 'ℵ',\n  larr: '←',\n  uarr: '↑',\n  rarr: '→',\n  darr: '↓',\n  harr: '↔',\n  crarr: '↵',\n  lArr: '⇐',\n  uArr: '⇑',\n  rArr: '⇒',\n  dArr: '⇓',\n  hArr: '⇔',\n  forall: '∀',\n  part: '∂',\n  exist: '∃',\n  empty: '∅',\n  nabla: '∇',\n  isin: '∈',\n  notin: '∉',\n  ni: '∋',\n  prod: '∏',\n  sum: '∑',\n  minus: '−',\n  lowast: '∗',\n  radic: '√',\n  prop: '∝',\n  infin: '∞',\n  ang: '∠',\n  and: '∧',\n  or: '∨',\n  cap: '∩',\n  cup: '∪',\n  int: '∫',\n  there4: '∴',\n  sim: '∼',\n  cong: '≅',\n  asymp: '≈',\n  ne: '≠',\n  equiv: '≡',\n  le: '≤',\n  ge: '≥',\n  sub: '⊂',\n  sup: '⊃',\n  nsub: '⊄',\n  sube: '⊆',\n  supe: '⊇',\n  oplus: '⊕',\n  otimes: '⊗',\n  perp: '⊥',\n  sdot: '⋅',\n  lceil: '⌈',\n  rceil: '⌉',\n  lfloor: '⌊',\n  rfloor: '⌋',\n  lang: '〈',\n  rang: '〉',\n  loz: '◊',\n  spades: '♠',\n  clubs: '♣',\n  hearts: '♥',\n  diams: '♦',\n  quot: '\"',\n  amp: '&',\n  lt: '<',\n  gt: '>',\n  OElig: 'Œ',\n  oelig: 'œ',\n  Scaron: 'Š',\n  scaron: 'š',\n  Yuml: 'Ÿ',\n  circ: 'ˆ',\n  tilde: '˜',\n  ensp: ' ',\n  emsp: ' ',\n  thinsp: ' ',\n  zwnj: '‌',\n  zwj: '‍',\n  lrm: '‎',\n  rlm: '‏',\n  ndash: '–',\n  mdash: '—',\n  lsquo: '‘',\n  rsquo: '’',\n  sbquo: '‚',\n  ldquo: '“',\n  rdquo: '”',\n  bdquo: '„',\n  dagger: '†',\n  Dagger: '‡',\n  permil: '‰',\n  lsaquo: '‹',\n  rsaquo: '›',\n  euro: '€'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/character-entities-html4/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/character-entities-legacy/index.js":
/*!*************************************************************!*\
  !*** ../../node_modules/character-entities-legacy/index.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   characterEntitiesLegacy: function() { return /* binding */ characterEntitiesLegacy; }\n/* harmony export */ });\n/**\n * List of legacy HTML named character references that don’t need a trailing semicolon.\n *\n * @type {Array<string>}\n */\nconst characterEntitiesLegacy = [\n  'AElig',\n  'AMP',\n  'Aacute',\n  'Acirc',\n  'Agrave',\n  'Aring',\n  'Atilde',\n  'Auml',\n  'COPY',\n  'Ccedil',\n  'ETH',\n  'Eacute',\n  'Ecirc',\n  'Egrave',\n  'Euml',\n  'GT',\n  'Iacute',\n  'Icirc',\n  'Igrave',\n  'Iuml',\n  'LT',\n  'Ntilde',\n  'Oacute',\n  'Ocirc',\n  'Ograve',\n  'Oslash',\n  'Otilde',\n  'Ouml',\n  'QUOT',\n  'REG',\n  'THORN',\n  'Uacute',\n  'Ucirc',\n  'Ugrave',\n  'Uuml',\n  'Yacute',\n  'aacute',\n  'acirc',\n  'acute',\n  'aelig',\n  'agrave',\n  'amp',\n  'aring',\n  'atilde',\n  'auml',\n  'brvbar',\n  'ccedil',\n  'cedil',\n  'cent',\n  'copy',\n  'curren',\n  'deg',\n  'divide',\n  'eacute',\n  'ecirc',\n  'egrave',\n  'eth',\n  'euml',\n  'frac12',\n  'frac14',\n  'frac34',\n  'gt',\n  'iacute',\n  'icirc',\n  'iexcl',\n  'igrave',\n  'iquest',\n  'iuml',\n  'laquo',\n  'lt',\n  'macr',\n  'micro',\n  'middot',\n  'nbsp',\n  'not',\n  'ntilde',\n  'oacute',\n  'ocirc',\n  'ograve',\n  'ordf',\n  'ordm',\n  'oslash',\n  'otilde',\n  'ouml',\n  'para',\n  'plusmn',\n  'pound',\n  'quot',\n  'raquo',\n  'reg',\n  'sect',\n  'shy',\n  'sup1',\n  'sup2',\n  'sup3',\n  'szlig',\n  'thorn',\n  'times',\n  'uacute',\n  'ucirc',\n  'ugrave',\n  'uml',\n  'uuml',\n  'yacute',\n  'yen',\n  'yuml'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/character-entities-legacy/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-html/lib/handle/comment.js":
/*!******************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/handle/comment.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   comment: function() { return /* binding */ comment; }\n/* harmony export */ });\n/* harmony import */ var stringify_entities__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stringify-entities */ \"(app-pages-browser)/../../node_modules/stringify-entities/lib/index.js\");\n/**\n * @typedef {import('../types.js').Comment} Comment\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n */\n\n\n\n/**\n * Serialize a comment.\n *\n * @param {Comment} node\n *   Node to handle.\n * @param {number | undefined} _1\n *   Index of `node` in `parent.\n * @param {Parent | undefined} _2\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction comment(node, _1, _2, state) {\n  // See: <https://html.spec.whatwg.org/multipage/syntax.html#comments>\n  return state.settings.bogusComments\n    ? '<?' +\n        (0,stringify_entities__WEBPACK_IMPORTED_MODULE_0__.stringifyEntities)(\n          node.value,\n          Object.assign({}, state.settings.characterReferences, {subset: ['>']})\n        ) +\n        '>'\n    : '<!--' + node.value.replace(/^>|^->|<!--|-->|--!>|<!-$/g, encode) + '-->'\n\n  /**\n   * @param {string} $0\n   */\n  function encode($0) {\n    return (0,stringify_entities__WEBPACK_IMPORTED_MODULE_0__.stringifyEntities)(\n      $0,\n      Object.assign({}, state.settings.characterReferences, {\n        subset: ['<', '>']\n      })\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-html/lib/handle/comment.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-html/lib/handle/doctype.js":
/*!******************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/handle/doctype.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   doctype: function() { return /* binding */ doctype; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').DocType} DocType\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n */\n\n/**\n * Serialize a doctype.\n *\n * @param {DocType} _1\n *   Node to handle.\n * @param {number | undefined} _2\n *   Index of `node` in `parent.\n * @param {Parent | undefined} _3\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction doctype(_1, _2, _3, state) {\n  return (\n    '<!' +\n    (state.settings.upperDoctype ? 'DOCTYPE' : 'doctype') +\n    (state.settings.tightDoctype ? '' : ' ') +\n    'html>'\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbGliL2hhbmRsZS9kb2N0eXBlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsK0JBQStCO0FBQzVDLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsNkJBQTZCO0FBQzFDOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8taHRtbC9saWIvaGFuZGxlL2RvY3R5cGUuanM/YWY3NyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuRG9jVHlwZX0gRG9jVHlwZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5QYXJlbnR9IFBhcmVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5TdGF0ZX0gU3RhdGVcbiAqL1xuXG4vKipcbiAqIFNlcmlhbGl6ZSBhIGRvY3R5cGUuXG4gKlxuICogQHBhcmFtIHtEb2NUeXBlfSBfMVxuICogICBOb2RlIHRvIGhhbmRsZS5cbiAqIEBwYXJhbSB7bnVtYmVyIHwgdW5kZWZpbmVkfSBfMlxuICogICBJbmRleCBvZiBgbm9kZWAgaW4gYHBhcmVudC5cbiAqIEBwYXJhbSB7UGFyZW50IHwgdW5kZWZpbmVkfSBfM1xuICogICBQYXJlbnQgb2YgYG5vZGVgLlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kIGFib3V0IHRoZSBjdXJyZW50IHN0YXRlLlxuICogQHJldHVybnMge3N0cmluZ31cbiAqICAgU2VyaWFsaXplZCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gZG9jdHlwZShfMSwgXzIsIF8zLCBzdGF0ZSkge1xuICByZXR1cm4gKFxuICAgICc8IScgK1xuICAgIChzdGF0ZS5zZXR0aW5ncy51cHBlckRvY3R5cGUgPyAnRE9DVFlQRScgOiAnZG9jdHlwZScpICtcbiAgICAoc3RhdGUuc2V0dGluZ3MudGlnaHREb2N0eXBlID8gJycgOiAnICcpICtcbiAgICAnaHRtbD4nXG4gIClcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-html/lib/handle/doctype.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-html/lib/handle/element.js":
/*!******************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/handle/element.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   element: function() { return /* binding */ element; }\n/* harmony export */ });\n/* harmony import */ var ccount__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ccount */ \"(app-pages-browser)/../../node_modules/ccount/index.js\");\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! comma-separated-tokens */ \"(app-pages-browser)/../../node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! property-information */ \"(app-pages-browser)/../../node_modules/property-information/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! property-information */ \"(app-pages-browser)/../../node_modules/property-information/lib/find.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! space-separated-tokens */ \"(app-pages-browser)/../../node_modules/space-separated-tokens/index.js\");\n/* harmony import */ var stringify_entities__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! stringify-entities */ \"(app-pages-browser)/../../node_modules/stringify-entities/lib/index.js\");\n/* harmony import */ var _omission_opening_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../omission/opening.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-html/lib/omission/opening.js\");\n/* harmony import */ var _omission_closing_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../omission/closing.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-html/lib/omission/closing.js\");\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Properties} Properties\n * @typedef {import('../types.js').PropertyValue} PropertyValue\n */\n\n\n\n\n\n\n\n\n\n/**\n * Maps of subsets.\n *\n * Each value is a matrix of tuples.\n * The value at `0` causes parse errors, the value at `1` is valid.\n * Of both, the value at `0` is unsafe, and the value at `1` is safe.\n *\n * @type {Record<'name' | 'unquoted' | 'single' | 'double', Array<[Array<string>, Array<string>]>>}\n */\nconst constants = {\n  // See: <https://html.spec.whatwg.org/#attribute-name-state>.\n  name: [\n    ['\\t\\n\\f\\r &/=>'.split(''), '\\t\\n\\f\\r \"&\\'/=>`'.split('')],\n    ['\\0\\t\\n\\f\\r \"&\\'/<=>'.split(''), '\\0\\t\\n\\f\\r \"&\\'/<=>`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(unquoted)-state>.\n  unquoted: [\n    ['\\t\\n\\f\\r &>'.split(''), '\\0\\t\\n\\f\\r \"&\\'<=>`'.split('')],\n    ['\\0\\t\\n\\f\\r \"&\\'<=>`'.split(''), '\\0\\t\\n\\f\\r \"&\\'<=>`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(single-quoted)-state>.\n  single: [\n    [\"&'\".split(''), '\"&\\'`'.split('')],\n    [\"\\0&'\".split(''), '\\0\"&\\'`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(double-quoted)-state>.\n  double: [\n    ['\"&'.split(''), '\"&\\'`'.split('')],\n    ['\\0\"&'.split(''), '\\0\"&\\'`'.split('')]\n  ]\n}\n\n/**\n * Serialize an element node.\n *\n * @param {Element} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parent | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\n// eslint-disable-next-line complexity\nfunction element(node, index, parent, state) {\n  const schema = state.schema\n  const omit = schema.space === 'svg' ? false : state.settings.omitOptionalTags\n  let selfClosing =\n    schema.space === 'svg'\n      ? state.settings.closeEmptyElements\n      : state.settings.voids.includes(node.tagName.toLowerCase())\n  /** @type {Array<string>} */\n  const parts = []\n  /** @type {string} */\n  let last\n\n  if (schema.space === 'html' && node.tagName === 'svg') {\n    state.schema = property_information__WEBPACK_IMPORTED_MODULE_0__.svg\n  }\n\n  const attrs = serializeAttributes(state, node.properties)\n\n  const content = state.all(\n    schema.space === 'html' && node.tagName === 'template' ? node.content : node\n  )\n\n  state.schema = schema\n\n  // If the node is categorised as void, but it has children, remove the\n  // categorisation.\n  // This enables for example `menuitem`s, which are void in W3C HTML but not\n  // void in WHATWG HTML, to be stringified properly.\n  if (content) selfClosing = false\n\n  if (attrs || !omit || !(0,_omission_opening_js__WEBPACK_IMPORTED_MODULE_1__.opening)(node, index, parent)) {\n    parts.push('<', node.tagName, attrs ? ' ' + attrs : '')\n\n    if (\n      selfClosing &&\n      (schema.space === 'svg' || state.settings.closeSelfClosing)\n    ) {\n      last = attrs.charAt(attrs.length - 1)\n      if (\n        !state.settings.tightSelfClosing ||\n        last === '/' ||\n        (last && last !== '\"' && last !== \"'\")\n      ) {\n        parts.push(' ')\n      }\n\n      parts.push('/')\n    }\n\n    parts.push('>')\n  }\n\n  parts.push(content)\n\n  if (!selfClosing && (!omit || !(0,_omission_closing_js__WEBPACK_IMPORTED_MODULE_2__.closing)(node, index, parent))) {\n    parts.push('</' + node.tagName + '>')\n  }\n\n  return parts.join('')\n}\n\n/**\n * @param {State} state\n * @param {Properties | null | undefined} props\n * @returns {string}\n */\nfunction serializeAttributes(state, props) {\n  /** @type {Array<string>} */\n  const values = []\n  let index = -1\n  /** @type {string} */\n  let key\n\n  if (props) {\n    for (key in props) {\n      if (props[key] !== undefined && props[key] !== null) {\n        const value = serializeAttribute(state, key, props[key])\n        if (value) values.push(value)\n      }\n    }\n  }\n\n  while (++index < values.length) {\n    const last = state.settings.tightAttributes\n      ? values[index].charAt(values[index].length - 1)\n      : null\n\n    // In tight mode, don’t add a space after quoted attributes.\n    if (index !== values.length - 1 && last !== '\"' && last !== \"'\") {\n      values[index] += ' '\n    }\n  }\n\n  return values.join('')\n}\n\n/**\n * @param {State} state\n * @param {string} key\n * @param {PropertyValue} value\n * @returns {string}\n */\n// eslint-disable-next-line complexity\nfunction serializeAttribute(state, key, value) {\n  const info = (0,property_information__WEBPACK_IMPORTED_MODULE_3__.find)(state.schema, key)\n  const x =\n    state.settings.allowParseErrors && state.schema.space === 'html' ? 0 : 1\n  const y = state.settings.allowDangerousCharacters ? 0 : 1\n  let quote = state.quote\n  /** @type {string | undefined} */\n  let result\n\n  if (info.overloadedBoolean && (value === info.attribute || value === '')) {\n    value = true\n  } else if (\n    info.boolean ||\n    (info.overloadedBoolean && typeof value !== 'string')\n  ) {\n    value = Boolean(value)\n  }\n\n  if (\n    value === undefined ||\n    value === null ||\n    value === false ||\n    (typeof value === 'number' && Number.isNaN(value))\n  ) {\n    return ''\n  }\n\n  const name = (0,stringify_entities__WEBPACK_IMPORTED_MODULE_4__.stringifyEntities)(\n    info.attribute,\n    Object.assign({}, state.settings.characterReferences, {\n      // Always encode without parse errors in non-HTML.\n      subset: constants.name[x][y]\n    })\n  )\n\n  // No value.\n  // There is currently only one boolean property in SVG: `[download]` on\n  // `<a>`.\n  // This property does not seem to work in browsers (Firefox, Safari, Chrome),\n  // so I can’t test if dropping the value works.\n  // But I assume that it should:\n  //\n  // ```html\n  // <!doctype html>\n  // <svg viewBox=\"0 0 100 100\">\n  //   <a href=https://example.com download>\n  //     <circle cx=50 cy=40 r=35 />\n  //   </a>\n  // </svg>\n  // ```\n  //\n  // See: <https://github.com/wooorm/property-information/blob/main/lib/svg.js>\n  if (value === true) return name\n\n  // `spaces` doesn’t accept a second argument, but it’s given here just to\n  // keep the code cleaner.\n  value = Array.isArray(value)\n    ? (info.commaSeparated ? comma_separated_tokens__WEBPACK_IMPORTED_MODULE_5__.stringify : space_separated_tokens__WEBPACK_IMPORTED_MODULE_6__.stringify)(value, {\n        padLeft: !state.settings.tightCommaSeparatedLists\n      })\n    : String(value)\n\n  if (state.settings.collapseEmptyAttributes && !value) return name\n\n  // Check unquoted value.\n  if (state.settings.preferUnquoted) {\n    result = (0,stringify_entities__WEBPACK_IMPORTED_MODULE_4__.stringifyEntities)(\n      value,\n      Object.assign({}, state.settings.characterReferences, {\n        subset: constants.unquoted[x][y],\n        attribute: true\n      })\n    )\n  }\n\n  // If we don’t want unquoted, or if `value` contains character references when\n  // unquoted…\n  if (result !== value) {\n    // If the alternative is less common than `quote`, switch.\n    if (\n      state.settings.quoteSmart &&\n      (0,ccount__WEBPACK_IMPORTED_MODULE_7__.ccount)(value, quote) > (0,ccount__WEBPACK_IMPORTED_MODULE_7__.ccount)(value, state.alternative)\n    ) {\n      quote = state.alternative\n    }\n\n    result =\n      quote +\n      (0,stringify_entities__WEBPACK_IMPORTED_MODULE_4__.stringifyEntities)(\n        value,\n        Object.assign({}, state.settings.characterReferences, {\n          // Always encode without parse errors in non-HTML.\n          subset: (quote === \"'\" ? constants.single : constants.double)[x][y],\n          attribute: true\n        })\n      ) +\n      quote\n  }\n\n  // Don’t add a `=` for unquoted empties.\n  return name + (result ? '=' + result : result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-html/lib/handle/element.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-html/lib/handle/index.js":
/*!****************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/handle/index.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handle: function() { return /* binding */ handle; }\n/* harmony export */ });\n/* harmony import */ var zwitch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zwitch */ \"(app-pages-browser)/../../node_modules/zwitch/index.js\");\n/* harmony import */ var _comment_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./comment.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-html/lib/handle/comment.js\");\n/* harmony import */ var _doctype_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./doctype.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-html/lib/handle/doctype.js\");\n/* harmony import */ var _element_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./element.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-html/lib/handle/element.js\");\n/* harmony import */ var _raw_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./raw.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-html/lib/handle/raw.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./root.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-html/lib/handle/root.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./text.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-html/lib/handle/text.js\");\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Node} Node\n * @typedef {import('../types.js').Parent} Parent\n */\n\n\n\n\n\n\n\n\n\n/**\n * @type {(node: Node, index: number | undefined, parent: Parent | undefined, state: State) => string}\n */\nconst handle = (0,zwitch__WEBPACK_IMPORTED_MODULE_0__.zwitch)('type', {\n  invalid,\n  unknown,\n  handlers: {comment: _comment_js__WEBPACK_IMPORTED_MODULE_1__.comment, doctype: _doctype_js__WEBPACK_IMPORTED_MODULE_2__.doctype, element: _element_js__WEBPACK_IMPORTED_MODULE_3__.element, raw: _raw_js__WEBPACK_IMPORTED_MODULE_4__.raw, root: _root_js__WEBPACK_IMPORTED_MODULE_5__.root, text: _text_js__WEBPACK_IMPORTED_MODULE_6__.text}\n})\n\n/**\n * Fail when a non-node is found in the tree.\n *\n * @param {unknown} node\n *   Unknown value.\n * @returns {never}\n *   Never.\n */\nfunction invalid(node) {\n  throw new Error('Expected node, not `' + node + '`')\n}\n\n/**\n * Fail when a node with an unknown type is found in the tree.\n *\n * @param {unknown} node\n *  Unknown node.\n * @returns {never}\n *   Never.\n */\nfunction unknown(node) {\n  // @ts-expect-error: `type` is defined.\n  throw new Error('Cannot compile unknown node `' + node.type + '`')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-html/lib/handle/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-html/lib/handle/raw.js":
/*!**************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/handle/raw.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   raw: function() { return /* binding */ raw; }\n/* harmony export */ });\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./text.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-html/lib/handle/text.js\");\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').Raw} Raw\n */\n\n\n\n/**\n * Serialize a raw node.\n *\n * @param {Raw} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parent | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction raw(node, index, parent, state) {\n  return state.settings.allowDangerousHtml\n    ? node.value\n    : (0,_text_js__WEBPACK_IMPORTED_MODULE_0__.text)(node, index, parent, state)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbGliL2hhbmRsZS9yYXcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEsNkJBQTZCO0FBQzFDLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsMkJBQTJCO0FBQ3hDOztBQUU4Qjs7QUFFOUI7QUFDQTtBQUNBO0FBQ0EsV0FBVyxLQUFLO0FBQ2hCO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0EsTUFBTSw4Q0FBSTtBQUNWIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbGliL2hhbmRsZS9yYXcuanM/OTNlZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuU3RhdGV9IFN0YXRlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlBhcmVudH0gUGFyZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlJhd30gUmF3XG4gKi9cblxuaW1wb3J0IHt0ZXh0fSBmcm9tICcuL3RleHQuanMnXG5cbi8qKlxuICogU2VyaWFsaXplIGEgcmF3IG5vZGUuXG4gKlxuICogQHBhcmFtIHtSYXd9IG5vZGVcbiAqICAgTm9kZSB0byBoYW5kbGUuXG4gKiBAcGFyYW0ge251bWJlciB8IHVuZGVmaW5lZH0gaW5kZXhcbiAqICAgSW5kZXggb2YgYG5vZGVgIGluIGBwYXJlbnQuXG4gKiBAcGFyYW0ge1BhcmVudCB8IHVuZGVmaW5lZH0gcGFyZW50XG4gKiAgIFBhcmVudCBvZiBgbm9kZWAuXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQgYWJvdXQgdGhlIGN1cnJlbnQgc3RhdGUuXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICogICBTZXJpYWxpemVkIG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiByYXcobm9kZSwgaW5kZXgsIHBhcmVudCwgc3RhdGUpIHtcbiAgcmV0dXJuIHN0YXRlLnNldHRpbmdzLmFsbG93RGFuZ2Vyb3VzSHRtbFxuICAgID8gbm9kZS52YWx1ZVxuICAgIDogdGV4dChub2RlLCBpbmRleCwgcGFyZW50LCBzdGF0ZSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-html/lib/handle/raw.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-html/lib/handle/root.js":
/*!***************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/handle/root.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: function() { return /* binding */ root; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Root} Root\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n */\n\n/**\n * Serialize a root.\n *\n * @param {Root} node\n *   Node to handle.\n * @param {number | undefined} _1\n *   Index of `node` in `parent.\n * @param {Parent | undefined} _2\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction root(node, _1, _2, state) {\n  return state.all(node)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbGliL2hhbmRsZS9yb290LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsNEJBQTRCO0FBQ3pDLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsNkJBQTZCO0FBQzFDOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsTUFBTTtBQUNqQjtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1odG1sL2xpYi9oYW5kbGUvcm9vdC5qcz9hYWRlIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5Sb290fSBSb290XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlBhcmVudH0gUGFyZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbi8qKlxuICogU2VyaWFsaXplIGEgcm9vdC5cbiAqXG4gKiBAcGFyYW0ge1Jvb3R9IG5vZGVcbiAqICAgTm9kZSB0byBoYW5kbGUuXG4gKiBAcGFyYW0ge251bWJlciB8IHVuZGVmaW5lZH0gXzFcbiAqICAgSW5kZXggb2YgYG5vZGVgIGluIGBwYXJlbnQuXG4gKiBAcGFyYW0ge1BhcmVudCB8IHVuZGVmaW5lZH0gXzJcbiAqICAgUGFyZW50IG9mIGBub2RlYC5cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZCBhYm91dCB0aGUgY3VycmVudCBzdGF0ZS5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIFNlcmlhbGl6ZWQgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHJvb3Qobm9kZSwgXzEsIF8yLCBzdGF0ZSkge1xuICByZXR1cm4gc3RhdGUuYWxsKG5vZGUpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-html/lib/handle/root.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-html/lib/handle/text.js":
/*!***************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/handle/text.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: function() { return /* binding */ text; }\n/* harmony export */ });\n/* harmony import */ var stringify_entities__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stringify-entities */ \"(app-pages-browser)/../../node_modules/stringify-entities/lib/index.js\");\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').Raw} Raw\n * @typedef {import('../types.js').Text} Text\n */\n\n\n\n/**\n * Serialize a text node.\n *\n * @param {Text | Raw} node\n *   Node to handle.\n * @param {number | undefined} _\n *   Index of `node` in `parent.\n * @param {Parent | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction text(node, _, parent, state) {\n  // Check if content of `node` should be escaped.\n  return parent &&\n    parent.type === 'element' &&\n    (parent.tagName === 'script' || parent.tagName === 'style')\n    ? node.value\n    : (0,stringify_entities__WEBPACK_IMPORTED_MODULE_0__.stringifyEntities)(\n        node.value,\n        Object.assign({}, state.settings.characterReferences, {\n          subset: ['<', '&']\n        })\n      )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-html/lib/handle/text.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-html/lib/index.js":
/*!*********************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/index.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   all: function() { return /* binding */ all; },\n/* harmony export */   toHtml: function() { return /* binding */ toHtml; }\n/* harmony export */ });\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(app-pages-browser)/../../node_modules/property-information/index.js\");\n/* harmony import */ var html_void_elements__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! html-void-elements */ \"(app-pages-browser)/../../node_modules/html-void-elements/index.js\");\n/* harmony import */ var _handle_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./handle/index.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-html/lib/handle/index.js\");\n/**\n * @typedef {import('./types.js').Node} Node\n * @typedef {import('./types.js').Parent} Parent\n * @typedef {import('./types.js').Content} Content\n * @typedef {import('./types.js').Options} Options\n * @typedef {import('./types.js').State} State\n */\n\n\n\n\n\n/**\n * Serialize hast as HTML.\n *\n * @param {Node | Array<Content>} tree\n *   Tree to serialize.\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {string}\n *   Serialized HTML.\n */\n// eslint-disable-next-line complexity\nfunction toHtml(tree, options) {\n  const options_ = options || {}\n  const quote = options_.quote || '\"'\n  const alternative = quote === '\"' ? \"'\" : '\"'\n\n  if (quote !== '\"' && quote !== \"'\") {\n    throw new Error('Invalid quote `' + quote + '`, expected `\\'` or `\"`')\n  }\n\n  /** @type {State} */\n  const state = {\n    one,\n    all,\n    settings: {\n      omitOptionalTags: options_.omitOptionalTags || false,\n      allowParseErrors: options_.allowParseErrors || false,\n      allowDangerousCharacters: options_.allowDangerousCharacters || false,\n      quoteSmart: options_.quoteSmart || false,\n      preferUnquoted: options_.preferUnquoted || false,\n      tightAttributes: options_.tightAttributes || false,\n      upperDoctype: options_.upperDoctype || false,\n      tightDoctype: options_.tightDoctype || false,\n      bogusComments: options_.bogusComments || false,\n      tightCommaSeparatedLists: options_.tightCommaSeparatedLists || false,\n      tightSelfClosing: options_.tightSelfClosing || false,\n      collapseEmptyAttributes: options_.collapseEmptyAttributes || false,\n      allowDangerousHtml: options_.allowDangerousHtml || false,\n      voids: options_.voids || html_void_elements__WEBPACK_IMPORTED_MODULE_0__.htmlVoidElements,\n      characterReferences:\n        options_.characterReferences || options_.entities || {},\n      closeSelfClosing: options_.closeSelfClosing || false,\n      closeEmptyElements: options_.closeEmptyElements || false\n    },\n    schema: options_.space === 'svg' ? property_information__WEBPACK_IMPORTED_MODULE_1__.svg : property_information__WEBPACK_IMPORTED_MODULE_1__.html,\n    quote,\n    alternative\n  }\n\n  return state.one(\n    Array.isArray(tree) ? {type: 'root', children: tree} : tree,\n    undefined,\n    undefined\n  )\n}\n\n/**\n * Serialize a node.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Node} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parent | undefined} parent\n *   Parent of `node`.\n * @returns {string}\n *   Serialized node.\n */\nfunction one(node, index, parent) {\n  return (0,_handle_index_js__WEBPACK_IMPORTED_MODULE_2__.handle)(node, index, parent, this)\n}\n\n/**\n * Serialize all children of `parent`.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Parent | undefined} parent\n *   Parent whose children to serialize.\n * @returns {string}\n */\nfunction all(parent) {\n  /** @type {Array<string>} */\n  const results = []\n  const children = (parent && parent.children) || []\n  let index = -1\n\n  while (++index < children.length) {\n    results[index] = this.one(children[index], index, parent)\n  }\n\n  return results.join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-html/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-html/lib/omission/closing.js":
/*!********************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/omission/closing.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closing: function() { return /* binding */ closing; }\n/* harmony export */ });\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-whitespace */ \"(app-pages-browser)/../../node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js\");\n/* harmony import */ var _util_siblings_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/siblings.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-html/lib/omission/util/siblings.js\");\n/* harmony import */ var _omission_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./omission.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-html/lib/omission/omission.js\");\n/**\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Parent} Parent\n */\n\n\n\n\n\nconst closing = (0,_omission_js__WEBPACK_IMPORTED_MODULE_0__.omission)({\n  html,\n  head: headOrColgroupOrCaption,\n  body,\n  p,\n  li,\n  dt,\n  dd,\n  rt: rubyElement,\n  rp: rubyElement,\n  optgroup,\n  option,\n  menuitem,\n  colgroup: headOrColgroupOrCaption,\n  caption: headOrColgroupOrCaption,\n  thead,\n  tbody,\n  tfoot,\n  tr,\n  td: cells,\n  th: cells\n})\n\n/**\n * Macro for `</head>`, `</colgroup>`, and `</caption>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction headOrColgroupOrCaption(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index, true)\n  return (\n    !next ||\n    (next.type !== 'comment' &&\n      !(next.type === 'text' && (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__.whitespace)(next.value.charAt(0))))\n  )\n}\n\n/**\n * Whether to omit `</html>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction html(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || next.type !== 'comment'\n}\n\n/**\n * Whether to omit `</body>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction body(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || next.type !== 'comment'\n}\n\n/**\n * Whether to omit `</p>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\n// eslint-disable-next-line complexity\nfunction p(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return next\n    ? next.type === 'element' &&\n        (next.tagName === 'address' ||\n          next.tagName === 'article' ||\n          next.tagName === 'aside' ||\n          next.tagName === 'blockquote' ||\n          next.tagName === 'details' ||\n          next.tagName === 'div' ||\n          next.tagName === 'dl' ||\n          next.tagName === 'fieldset' ||\n          next.tagName === 'figcaption' ||\n          next.tagName === 'figure' ||\n          next.tagName === 'footer' ||\n          next.tagName === 'form' ||\n          next.tagName === 'h1' ||\n          next.tagName === 'h2' ||\n          next.tagName === 'h3' ||\n          next.tagName === 'h4' ||\n          next.tagName === 'h5' ||\n          next.tagName === 'h6' ||\n          next.tagName === 'header' ||\n          next.tagName === 'hgroup' ||\n          next.tagName === 'hr' ||\n          next.tagName === 'main' ||\n          next.tagName === 'menu' ||\n          next.tagName === 'nav' ||\n          next.tagName === 'ol' ||\n          next.tagName === 'p' ||\n          next.tagName === 'pre' ||\n          next.tagName === 'section' ||\n          next.tagName === 'table' ||\n          next.tagName === 'ul')\n    : !parent ||\n        // Confusing parent.\n        !(\n          parent.type === 'element' &&\n          (parent.tagName === 'a' ||\n            parent.tagName === 'audio' ||\n            parent.tagName === 'del' ||\n            parent.tagName === 'ins' ||\n            parent.tagName === 'map' ||\n            parent.tagName === 'noscript' ||\n            parent.tagName === 'video')\n        )\n}\n\n/**\n * Whether to omit `</li>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction li(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'li')\n}\n\n/**\n * Whether to omit `</dt>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction dt(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    next &&\n    next.type === 'element' &&\n    (next.tagName === 'dt' || next.tagName === 'dd')\n  )\n}\n\n/**\n * Whether to omit `</dd>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction dd(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'dt' || next.tagName === 'dd'))\n  )\n}\n\n/**\n * Whether to omit `</rt>` or `</rp>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction rubyElement(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'rp' || next.tagName === 'rt'))\n  )\n}\n\n/**\n * Whether to omit `</optgroup>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction optgroup(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'optgroup')\n}\n\n/**\n * Whether to omit `</option>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction option(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'option' || next.tagName === 'optgroup'))\n  )\n}\n\n/**\n * Whether to omit `</menuitem>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction menuitem(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'menuitem' ||\n        next.tagName === 'hr' ||\n        next.tagName === 'menu'))\n  )\n}\n\n/**\n * Whether to omit `</thead>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction thead(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    next &&\n    next.type === 'element' &&\n    (next.tagName === 'tbody' || next.tagName === 'tfoot')\n  )\n}\n\n/**\n * Whether to omit `</tbody>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tbody(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'tbody' || next.tagName === 'tfoot'))\n  )\n}\n\n/**\n * Whether to omit `</tfoot>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tfoot(_, index, parent) {\n  return !(0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n}\n\n/**\n * Whether to omit `</tr>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tr(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'tr')\n}\n\n/**\n * Whether to omit `</td>` or `</th>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction cells(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'td' || next.tagName === 'th'))\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-html/lib/omission/closing.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-html/lib/omission/omission.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/omission/omission.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   omission: function() { return /* binding */ omission; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').OmitHandle} OmitHandle\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * Factory to check if a given node can have a tag omitted.\n *\n * @param {Record<string, OmitHandle>} handlers\n *   Omission handlers, where each key is a tag name, and each value is the\n *   corresponding handler.\n * @returns {OmitHandle}\n *   Whether to omit a tag of an element.\n */\nfunction omission(handlers) {\n  return omit\n\n  /**\n   * Check if a given node can have a tag omitted.\n   *\n   * @type {OmitHandle}\n   */\n  function omit(node, index, parent) {\n    return (\n      own.call(handlers, node.tagName) &&\n      handlers[node.tagName](node, index, parent)\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbGliL29taXNzaW9uL29taXNzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsa0NBQWtDO0FBQy9DOztBQUVBLGNBQWM7O0FBRWQ7QUFDQTtBQUNBO0FBQ0EsV0FBVyw0QkFBNEI7QUFDdkM7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1odG1sL2xpYi9vbWlzc2lvbi9vbWlzc2lvbi5qcz9iMjBjIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5PbWl0SGFuZGxlfSBPbWl0SGFuZGxlXG4gKi9cblxuY29uc3Qgb3duID0ge30uaGFzT3duUHJvcGVydHlcblxuLyoqXG4gKiBGYWN0b3J5IHRvIGNoZWNrIGlmIGEgZ2l2ZW4gbm9kZSBjYW4gaGF2ZSBhIHRhZyBvbWl0dGVkLlxuICpcbiAqIEBwYXJhbSB7UmVjb3JkPHN0cmluZywgT21pdEhhbmRsZT59IGhhbmRsZXJzXG4gKiAgIE9taXNzaW9uIGhhbmRsZXJzLCB3aGVyZSBlYWNoIGtleSBpcyBhIHRhZyBuYW1lLCBhbmQgZWFjaCB2YWx1ZSBpcyB0aGVcbiAqICAgY29ycmVzcG9uZGluZyBoYW5kbGVyLlxuICogQHJldHVybnMge09taXRIYW5kbGV9XG4gKiAgIFdoZXRoZXIgdG8gb21pdCBhIHRhZyBvZiBhbiBlbGVtZW50LlxuICovXG5leHBvcnQgZnVuY3Rpb24gb21pc3Npb24oaGFuZGxlcnMpIHtcbiAgcmV0dXJuIG9taXRcblxuICAvKipcbiAgICogQ2hlY2sgaWYgYSBnaXZlbiBub2RlIGNhbiBoYXZlIGEgdGFnIG9taXR0ZWQuXG4gICAqXG4gICAqIEB0eXBlIHtPbWl0SGFuZGxlfVxuICAgKi9cbiAgZnVuY3Rpb24gb21pdChub2RlLCBpbmRleCwgcGFyZW50KSB7XG4gICAgcmV0dXJuIChcbiAgICAgIG93bi5jYWxsKGhhbmRsZXJzLCBub2RlLnRhZ05hbWUpICYmXG4gICAgICBoYW5kbGVyc1tub2RlLnRhZ05hbWVdKG5vZGUsIGluZGV4LCBwYXJlbnQpXG4gICAgKVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-html/lib/omission/omission.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-html/lib/omission/opening.js":
/*!********************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/omission/opening.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   opening: function() { return /* binding */ opening; }\n/* harmony export */ });\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-whitespace */ \"(app-pages-browser)/../../node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js\");\n/* harmony import */ var _util_siblings_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/siblings.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-html/lib/omission/util/siblings.js\");\n/* harmony import */ var _closing_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./closing.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-html/lib/omission/closing.js\");\n/* harmony import */ var _omission_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./omission.js */ \"(app-pages-browser)/../../node_modules/hast-util-to-html/lib/omission/omission.js\");\n/**\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').Content} Content\n */\n\n\n\n\n\n\nconst opening = (0,_omission_js__WEBPACK_IMPORTED_MODULE_0__.omission)({\n  html,\n  head,\n  body,\n  colgroup,\n  tbody\n})\n\n/**\n * Whether to omit `<html>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction html(node) {\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1)\n  return !head || head.type !== 'comment'\n}\n\n/**\n * Whether to omit `<head>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction head(node) {\n  const children = node.children\n  /** @type {Array<string>} */\n  const seen = []\n  let index = -1\n\n  while (++index < children.length) {\n    const child = children[index]\n    if (\n      child.type === 'element' &&\n      (child.tagName === 'title' || child.tagName === 'base')\n    ) {\n      if (seen.includes(child.tagName)) return false\n      seen.push(child.tagName)\n    }\n  }\n\n  return children.length > 0\n}\n\n/**\n * Whether to omit `<body>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction body(node) {\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1, true)\n\n  return (\n    !head ||\n    (head.type !== 'comment' &&\n      !(head.type === 'text' && (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__.whitespace)(head.value.charAt(0))) &&\n      !(\n        head.type === 'element' &&\n        (head.tagName === 'meta' ||\n          head.tagName === 'link' ||\n          head.tagName === 'script' ||\n          head.tagName === 'style' ||\n          head.tagName === 'template')\n      ))\n  )\n}\n\n/**\n * Whether to omit `<colgroup>`.\n * The spec describes some logic for the opening tag, but it’s easier to\n * implement in the closing tag, to the same effect, so we handle it there\n * instead.\n *\n * @param {Element} node\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction colgroup(node, index, parent) {\n  const previous = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingBefore)(parent, index)\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1, true)\n\n  // Previous colgroup was already omitted.\n  if (\n    parent &&\n    previous &&\n    previous.type === 'element' &&\n    previous.tagName === 'colgroup' &&\n    (0,_closing_js__WEBPACK_IMPORTED_MODULE_3__.closing)(previous, parent.children.indexOf(previous), parent)\n  ) {\n    return false\n  }\n\n  return head && head.type === 'element' && head.tagName === 'col'\n}\n\n/**\n * Whether to omit `<tbody>`.\n *\n * @param {Element} node\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction tbody(node, index, parent) {\n  const previous = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingBefore)(parent, index)\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1)\n\n  // Previous table section was already omitted.\n  if (\n    parent &&\n    previous &&\n    previous.type === 'element' &&\n    (previous.tagName === 'thead' || previous.tagName === 'tbody') &&\n    (0,_closing_js__WEBPACK_IMPORTED_MODULE_3__.closing)(previous, parent.children.indexOf(previous), parent)\n  ) {\n    return false\n  }\n\n  return head && head.type === 'element' && head.tagName === 'tr'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-html/lib/omission/opening.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-html/lib/omission/util/siblings.js":
/*!**************************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/omission/util/siblings.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   siblingAfter: function() { return /* binding */ siblingAfter; },\n/* harmony export */   siblingBefore: function() { return /* binding */ siblingBefore; }\n/* harmony export */ });\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-whitespace */ \"(app-pages-browser)/../../node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js\");\n/**\n * @typedef {import('../../types.js').Parent} Parent\n * @typedef {import('../../types.js').Content} Content\n */\n\n\n\nconst siblingAfter = siblings(1)\nconst siblingBefore = siblings(-1)\n\n/**\n * Factory to check siblings in a direction.\n *\n * @param {number} increment\n */\nfunction siblings(increment) {\n  return sibling\n\n  /**\n   * Find applicable siblings in a direction.\n   *\n   * @param {Parent | null | undefined} parent\n   * @param {number | null | undefined} index\n   * @param {boolean | null | undefined} [includeWhitespace=false]\n   * @returns {Content}\n   */\n  function sibling(parent, index, includeWhitespace) {\n    const siblings = parent ? parent.children : []\n    let offset = (index || 0) + increment\n    let next = siblings && siblings[offset]\n\n    if (!includeWhitespace) {\n      while (next && (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_0__.whitespace)(next)) {\n        offset += increment\n        next = siblings[offset]\n      }\n    }\n\n    return next\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-html/lib/omission/util/siblings.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js":
/*!***************************************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js ***!
  \***************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   whitespace: function() { return /* binding */ whitespace; }\n/* harmony export */ });\n/**\n * Check if the given value is *inter-element whitespace*.\n *\n * @param {unknown} thing\n *   Thing to check (typically `Node` or `string`).\n * @returns {boolean}\n *   Whether the `value` is inter-element whitespace (`boolean`): consisting of\n *   zero or more of space, tab (`\\t`), line feed (`\\n`), carriage return\n *   (`\\r`), or form feed (`\\f`).\n *   If a node is passed it must be a `Text` node, whose `value` field is\n *   checked.\n */\nfunction whitespace(thing) {\n  /** @type {string} */\n  const value =\n    // @ts-expect-error looks like a node.\n    thing && typeof thing === 'object' && thing.type === 'text'\n      ? // @ts-expect-error looks like a text.\n        thing.value || ''\n      : thing\n\n  // HTML whitespace expression.\n  // See <https://infra.spec.whatwg.org/#ascii-whitespace>.\n  return typeof value === 'string' && value.replace(/[ \\t\\n\\f\\r]/g, '') === ''\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC13aGl0ZXNwYWNlL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCxhQUFhLFFBQVE7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC13aGl0ZXNwYWNlL2luZGV4LmpzPzA4MjkiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDaGVjayBpZiB0aGUgZ2l2ZW4gdmFsdWUgaXMgKmludGVyLWVsZW1lbnQgd2hpdGVzcGFjZSouXG4gKlxuICogQHBhcmFtIHt1bmtub3dufSB0aGluZ1xuICogICBUaGluZyB0byBjaGVjayAodHlwaWNhbGx5IGBOb2RlYCBvciBgc3RyaW5nYCkuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqICAgV2hldGhlciB0aGUgYHZhbHVlYCBpcyBpbnRlci1lbGVtZW50IHdoaXRlc3BhY2UgKGBib29sZWFuYCk6IGNvbnNpc3Rpbmcgb2ZcbiAqICAgemVybyBvciBtb3JlIG9mIHNwYWNlLCB0YWIgKGBcXHRgKSwgbGluZSBmZWVkIChgXFxuYCksIGNhcnJpYWdlIHJldHVyblxuICogICAoYFxccmApLCBvciBmb3JtIGZlZWQgKGBcXGZgKS5cbiAqICAgSWYgYSBub2RlIGlzIHBhc3NlZCBpdCBtdXN0IGJlIGEgYFRleHRgIG5vZGUsIHdob3NlIGB2YWx1ZWAgZmllbGQgaXNcbiAqICAgY2hlY2tlZC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHdoaXRlc3BhY2UodGhpbmcpIHtcbiAgLyoqIEB0eXBlIHtzdHJpbmd9ICovXG4gIGNvbnN0IHZhbHVlID1cbiAgICAvLyBAdHMtZXhwZWN0LWVycm9yIGxvb2tzIGxpa2UgYSBub2RlLlxuICAgIHRoaW5nICYmIHR5cGVvZiB0aGluZyA9PT0gJ29iamVjdCcgJiYgdGhpbmcudHlwZSA9PT0gJ3RleHQnXG4gICAgICA/IC8vIEB0cy1leHBlY3QtZXJyb3IgbG9va3MgbGlrZSBhIHRleHQuXG4gICAgICAgIHRoaW5nLnZhbHVlIHx8ICcnXG4gICAgICA6IHRoaW5nXG5cbiAgLy8gSFRNTCB3aGl0ZXNwYWNlIGV4cHJlc3Npb24uXG4gIC8vIFNlZSA8aHR0cHM6Ly9pbmZyYS5zcGVjLndoYXR3Zy5vcmcvI2FzY2lpLXdoaXRlc3BhY2U+LlxuICByZXR1cm4gdHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJyAmJiB2YWx1ZS5yZXBsYWNlKC9bIFxcdFxcblxcZlxccl0vZywgJycpID09PSAnJ1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/html-void-elements/index.js":
/*!******************************************************!*\
  !*** ../../node_modules/html-void-elements/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   htmlVoidElements: function() { return /* binding */ htmlVoidElements; }\n/* harmony export */ });\n/**\n * List of HTML void tag names.\n *\n * @type {Array<string>}\n */\nconst htmlVoidElements = [\n  'area',\n  'base',\n  'basefont',\n  'bgsound',\n  'br',\n  'col',\n  'command',\n  'embed',\n  'frame',\n  'hr',\n  'image',\n  'img',\n  'input',\n  'isindex',\n  'keygen',\n  'link',\n  'menuitem',\n  'meta',\n  'nextid',\n  'param',\n  'source',\n  'track',\n  'wbr'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaHRtbC12b2lkLWVsZW1lbnRzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL2h0bWwtdm9pZC1lbGVtZW50cy9pbmRleC5qcz84MzAwIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogTGlzdCBvZiBIVE1MIHZvaWQgdGFnIG5hbWVzLlxuICpcbiAqIEB0eXBlIHtBcnJheTxzdHJpbmc+fVxuICovXG5leHBvcnQgY29uc3QgaHRtbFZvaWRFbGVtZW50cyA9IFtcbiAgJ2FyZWEnLFxuICAnYmFzZScsXG4gICdiYXNlZm9udCcsXG4gICdiZ3NvdW5kJyxcbiAgJ2JyJyxcbiAgJ2NvbCcsXG4gICdjb21tYW5kJyxcbiAgJ2VtYmVkJyxcbiAgJ2ZyYW1lJyxcbiAgJ2hyJyxcbiAgJ2ltYWdlJyxcbiAgJ2ltZycsXG4gICdpbnB1dCcsXG4gICdpc2luZGV4JyxcbiAgJ2tleWdlbicsXG4gICdsaW5rJyxcbiAgJ21lbnVpdGVtJyxcbiAgJ21ldGEnLFxuICAnbmV4dGlkJyxcbiAgJ3BhcmFtJyxcbiAgJ3NvdXJjZScsXG4gICd0cmFjaycsXG4gICd3YnInXG5dXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/html-void-elements/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/rehype-stringify/index.js":
/*!****************************************************!*\
  !*** ../../node_modules/rehype-stringify/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* reexport safe */ _lib_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _lib_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/index.js */ \"(app-pages-browser)/../../node_modules/rehype-stringify/lib/index.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvcmVoeXBlLXN0cmluZ2lmeS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFzQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL3JlaHlwZS1zdHJpbmdpZnkvaW5kZXguanM/ZDU0ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge2RlZmF1bHR9IGZyb20gJy4vbGliL2luZGV4LmpzJ1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/rehype-stringify/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/rehype-stringify/lib/index.js":
/*!********************************************************!*\
  !*** ../../node_modules/rehype-stringify/lib/index.js ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ rehypeStringify; }\n/* harmony export */ });\n/* harmony import */ var hast_util_to_html__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-to-html */ \"(app-pages-browser)/../../node_modules/hast-util-to-html/lib/index.js\");\n/**\n * @typedef {import('hast').Root} Root\n * @typedef {Root|Root['children'][number]} Node\n * @typedef {import('hast-util-to-html').Options} Options\n */\n\n\n\n/**\n * @this {import('unified').Processor}\n * @type {import('unified').Plugin<[Options?]|Array<void>, Node, string>}\n */\nfunction rehypeStringify(config) {\n  const processorSettings = /** @type {Options} */ (this.data('settings'))\n  const settings = Object.assign({}, processorSettings, config)\n\n  Object.assign(this, {Compiler: compiler})\n\n  /**\n   * @type {import('unified').CompilerFunction<Node, string>}\n   */\n  function compiler(tree) {\n    return (0,hast_util_to_html__WEBPACK_IMPORTED_MODULE_0__.toHtml)(tree, settings)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvcmVoeXBlLXN0cmluZ2lmeS9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEscUJBQXFCO0FBQ2xDLGFBQWEsK0JBQStCO0FBQzVDLGFBQWEscUNBQXFDO0FBQ2xEOztBQUV3Qzs7QUFFeEM7QUFDQSxVQUFVO0FBQ1YsVUFBVTtBQUNWO0FBQ2U7QUFDZix1Q0FBdUMsU0FBUztBQUNoRCxtQ0FBbUM7O0FBRW5DLHVCQUF1QixtQkFBbUI7O0FBRTFDO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQSxXQUFXLHlEQUFNO0FBQ2pCO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy9yZWh5cGUtc3RyaW5naWZ5L2xpYi9pbmRleC5qcz9kN2NjIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLlJvb3R9IFJvb3RcbiAqIEB0eXBlZGVmIHtSb290fFJvb3RbJ2NoaWxkcmVuJ11bbnVtYmVyXX0gTm9kZVxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdC11dGlsLXRvLWh0bWwnKS5PcHRpb25zfSBPcHRpb25zXG4gKi9cblxuaW1wb3J0IHt0b0h0bWx9IGZyb20gJ2hhc3QtdXRpbC10by1odG1sJ1xuXG4vKipcbiAqIEB0aGlzIHtpbXBvcnQoJ3VuaWZpZWQnKS5Qcm9jZXNzb3J9XG4gKiBAdHlwZSB7aW1wb3J0KCd1bmlmaWVkJykuUGx1Z2luPFtPcHRpb25zP118QXJyYXk8dm9pZD4sIE5vZGUsIHN0cmluZz59XG4gKi9cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHJlaHlwZVN0cmluZ2lmeShjb25maWcpIHtcbiAgY29uc3QgcHJvY2Vzc29yU2V0dGluZ3MgPSAvKiogQHR5cGUge09wdGlvbnN9ICovICh0aGlzLmRhdGEoJ3NldHRpbmdzJykpXG4gIGNvbnN0IHNldHRpbmdzID0gT2JqZWN0LmFzc2lnbih7fSwgcHJvY2Vzc29yU2V0dGluZ3MsIGNvbmZpZylcblxuICBPYmplY3QuYXNzaWduKHRoaXMsIHtDb21waWxlcjogY29tcGlsZXJ9KVxuXG4gIC8qKlxuICAgKiBAdHlwZSB7aW1wb3J0KCd1bmlmaWVkJykuQ29tcGlsZXJGdW5jdGlvbjxOb2RlLCBzdHJpbmc+fVxuICAgKi9cbiAgZnVuY3Rpb24gY29tcGlsZXIodHJlZSkge1xuICAgIHJldHVybiB0b0h0bWwodHJlZSwgc2V0dGluZ3MpXG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/rehype-stringify/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/stringify-entities/lib/constant/dangerous.js":
/*!***********************************************************************!*\
  !*** ../../node_modules/stringify-entities/lib/constant/dangerous.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dangerous: function() { return /* binding */ dangerous; }\n/* harmony export */ });\n/**\n * List of legacy (that don’t need a trailing `;`) named references which could,\n * depending on what follows them, turn into a different meaning\n *\n * @type {Array<string>}\n */\nconst dangerous = [\n  'cent',\n  'copy',\n  'divide',\n  'gt',\n  'lt',\n  'not',\n  'para',\n  'times'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvc3RyaW5naWZ5LWVudGl0aWVzL2xpYi9jb25zdGFudC9kYW5nZXJvdXMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsZ0RBQWdEO0FBQ2hEO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL3N0cmluZ2lmeS1lbnRpdGllcy9saWIvY29uc3RhbnQvZGFuZ2Vyb3VzLmpzPzkyYjYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBMaXN0IG9mIGxlZ2FjeSAodGhhdCBkb27igJl0IG5lZWQgYSB0cmFpbGluZyBgO2ApIG5hbWVkIHJlZmVyZW5jZXMgd2hpY2ggY291bGQsXG4gKiBkZXBlbmRpbmcgb24gd2hhdCBmb2xsb3dzIHRoZW0sIHR1cm4gaW50byBhIGRpZmZlcmVudCBtZWFuaW5nXG4gKlxuICogQHR5cGUge0FycmF5PHN0cmluZz59XG4gKi9cbmV4cG9ydCBjb25zdCBkYW5nZXJvdXMgPSBbXG4gICdjZW50JyxcbiAgJ2NvcHknLFxuICAnZGl2aWRlJyxcbiAgJ2d0JyxcbiAgJ2x0JyxcbiAgJ25vdCcsXG4gICdwYXJhJyxcbiAgJ3RpbWVzJ1xuXVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/stringify-entities/lib/constant/dangerous.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/stringify-entities/lib/core.js":
/*!*********************************************************!*\
  !*** ../../node_modules/stringify-entities/lib/core.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   core: function() { return /* binding */ core; }\n/* harmony export */ });\n/**\n * @typedef CoreOptions\n * @property {ReadonlyArray<string>} [subset=[]]\n *   Whether to only escape the given subset of characters.\n * @property {boolean} [escapeOnly=false]\n *   Whether to only escape possibly dangerous characters.\n *   Those characters are `\"`, `&`, `'`, `<`, `>`, and `` ` ``.\n *\n * @typedef FormatOptions\n * @property {(code: number, next: number, options: CoreWithFormatOptions) => string} format\n *   Format strategy.\n *\n * @typedef {CoreOptions & FormatOptions & import('./util/format-smart.js').FormatSmartOptions} CoreWithFormatOptions\n */\n\nconst defaultSubsetRegex = /[\"&'<>`]/g\nconst surrogatePairsRegex = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g\nconst controlCharactersRegex =\n  // eslint-disable-next-line no-control-regex, unicorn/no-hex-escape\n  /[\\x01-\\t\\v\\f\\x0E-\\x1F\\x7F\\x81\\x8D\\x8F\\x90\\x9D\\xA0-\\uFFFF]/g\nconst regexEscapeRegex = /[|\\\\{}()[\\]^$+*?.]/g\n\n/** @type {WeakMap<ReadonlyArray<string>, RegExp>} */\nconst subsetToRegexCache = new WeakMap()\n\n/**\n * Encode certain characters in `value`.\n *\n * @param {string} value\n * @param {CoreWithFormatOptions} options\n * @returns {string}\n */\nfunction core(value, options) {\n  value = value.replace(\n    options.subset\n      ? charactersToExpressionCached(options.subset)\n      : defaultSubsetRegex,\n    basic\n  )\n\n  if (options.subset || options.escapeOnly) {\n    return value\n  }\n\n  return (\n    value\n      // Surrogate pairs.\n      .replace(surrogatePairsRegex, surrogate)\n      // BMP control characters (C0 except for LF, CR, SP; DEL; and some more\n      // non-ASCII ones).\n      .replace(controlCharactersRegex, basic)\n  )\n\n  /**\n   * @param {string} pair\n   * @param {number} index\n   * @param {string} all\n   */\n  function surrogate(pair, index, all) {\n    return options.format(\n      (pair.charCodeAt(0) - 0xd800) * 0x400 +\n        pair.charCodeAt(1) -\n        0xdc00 +\n        0x10000,\n      all.charCodeAt(index + 2),\n      options\n    )\n  }\n\n  /**\n   * @param {string} character\n   * @param {number} index\n   * @param {string} all\n   */\n  function basic(character, index, all) {\n    return options.format(\n      character.charCodeAt(0),\n      all.charCodeAt(index + 1),\n      options\n    )\n  }\n}\n\n/**\n * A wrapper function that caches the result of `charactersToExpression` with a WeakMap.\n * This can improve performance when tooling calls `charactersToExpression` repeatedly\n * with the same subset.\n *\n * @param {ReadonlyArray<string>} subset\n * @returns {RegExp}\n */\nfunction charactersToExpressionCached(subset) {\n  let cached = subsetToRegexCache.get(subset)\n\n  if (!cached) {\n    cached = charactersToExpression(subset)\n    subsetToRegexCache.set(subset, cached)\n  }\n\n  return cached\n}\n\n/**\n * @param {ReadonlyArray<string>} subset\n * @returns {RegExp}\n */\nfunction charactersToExpression(subset) {\n  /** @type {Array<string>} */\n  const groups = []\n  let index = -1\n\n  while (++index < subset.length) {\n    groups.push(subset[index].replace(regexEscapeRegex, '\\\\$&'))\n  }\n\n  return new RegExp('(?:' + groups.join('|') + ')', 'g')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/stringify-entities/lib/core.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/stringify-entities/lib/index.js":
/*!**********************************************************!*\
  !*** ../../node_modules/stringify-entities/lib/index.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stringifyEntities: function() { return /* binding */ stringifyEntities; },\n/* harmony export */   stringifyEntitiesLight: function() { return /* binding */ stringifyEntitiesLight; }\n/* harmony export */ });\n/* harmony import */ var _core_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core.js */ \"(app-pages-browser)/../../node_modules/stringify-entities/lib/core.js\");\n/* harmony import */ var _util_format_smart_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/format-smart.js */ \"(app-pages-browser)/../../node_modules/stringify-entities/lib/util/format-smart.js\");\n/* harmony import */ var _util_format_basic_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/format-basic.js */ \"(app-pages-browser)/../../node_modules/stringify-entities/lib/util/format-basic.js\");\n/**\n * @typedef {import('./core.js').CoreOptions & import('./util/format-smart.js').FormatSmartOptions} Options\n * @typedef {import('./core.js').CoreOptions} LightOptions\n */\n\n\n\n\n\n/**\n * Encode special characters in `value`.\n *\n * @param {string} value\n *   Value to encode.\n * @param {Options} [options]\n *   Configuration.\n * @returns {string}\n *   Encoded value.\n */\nfunction stringifyEntities(value, options) {\n  return (0,_core_js__WEBPACK_IMPORTED_MODULE_0__.core)(value, Object.assign({format: _util_format_smart_js__WEBPACK_IMPORTED_MODULE_1__.formatSmart}, options))\n}\n\n/**\n * Encode special characters in `value` as hexadecimals.\n *\n * @param {string} value\n *   Value to encode.\n * @param {LightOptions} [options]\n *   Configuration.\n * @returns {string}\n *   Encoded value.\n */\nfunction stringifyEntitiesLight(value, options) {\n  return (0,_core_js__WEBPACK_IMPORTED_MODULE_0__.core)(value, Object.assign({format: _util_format_basic_js__WEBPACK_IMPORTED_MODULE_2__.formatBasic}, options))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/stringify-entities/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/stringify-entities/lib/util/format-basic.js":
/*!**********************************************************************!*\
  !*** ../../node_modules/stringify-entities/lib/util/format-basic.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatBasic: function() { return /* binding */ formatBasic; }\n/* harmony export */ });\n/**\n * The smallest way to encode a character.\n *\n * @param {number} code\n * @returns {string}\n */\nfunction formatBasic(code) {\n  return '&#x' + code.toString(16).toUpperCase() + ';'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvc3RyaW5naWZ5LWVudGl0aWVzL2xpYi91dGlsL2Zvcm1hdC1iYXNpYy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNPO0FBQ1AscURBQXFEO0FBQ3JEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvc3RyaW5naWZ5LWVudGl0aWVzL2xpYi91dGlsL2Zvcm1hdC1iYXNpYy5qcz84MmQyIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogVGhlIHNtYWxsZXN0IHdheSB0byBlbmNvZGUgYSBjaGFyYWN0ZXIuXG4gKlxuICogQHBhcmFtIHtudW1iZXJ9IGNvZGVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXRCYXNpYyhjb2RlKSB7XG4gIHJldHVybiAnJiN4JyArIGNvZGUudG9TdHJpbmcoMTYpLnRvVXBwZXJDYXNlKCkgKyAnOydcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/stringify-entities/lib/util/format-basic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/stringify-entities/lib/util/format-smart.js":
/*!**********************************************************************!*\
  !*** ../../node_modules/stringify-entities/lib/util/format-smart.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatSmart: function() { return /* binding */ formatSmart; }\n/* harmony export */ });\n/* harmony import */ var _to_hexadecimal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./to-hexadecimal.js */ \"(app-pages-browser)/../../node_modules/stringify-entities/lib/util/to-hexadecimal.js\");\n/* harmony import */ var _to_decimal_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./to-decimal.js */ \"(app-pages-browser)/../../node_modules/stringify-entities/lib/util/to-decimal.js\");\n/* harmony import */ var _to_named_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./to-named.js */ \"(app-pages-browser)/../../node_modules/stringify-entities/lib/util/to-named.js\");\n/**\n * @typedef FormatSmartOptions\n * @property {boolean} [useNamedReferences=false]\n *   Prefer named character references (`&amp;`) where possible.\n * @property {boolean} [useShortestReferences=false]\n *   Prefer the shortest possible reference, if that results in less bytes.\n *   **Note**: `useNamedReferences` can be omitted when using `useShortestReferences`.\n * @property {boolean} [omitOptionalSemicolons=false]\n *   Whether to omit semicolons when possible.\n *   **Note**: This creates what HTML calls “parse errors” but is otherwise still valid HTML — don’t use this except when building a minifier.\n *   Omitting semicolons is possible for certain named and numeric references in some cases.\n * @property {boolean} [attribute=false]\n *   Create character references which don’t fail in attributes.\n *   **Note**: `attribute` only applies when operating dangerously with\n *   `omitOptionalSemicolons: true`.\n */\n\n\n\n\n\n/**\n * Configurable ways to encode a character yielding pretty or small results.\n *\n * @param {number} code\n * @param {number} next\n * @param {FormatSmartOptions} options\n * @returns {string}\n */\nfunction formatSmart(code, next, options) {\n  let numeric = (0,_to_hexadecimal_js__WEBPACK_IMPORTED_MODULE_0__.toHexadecimal)(code, next, options.omitOptionalSemicolons)\n  /** @type {string|undefined} */\n  let named\n\n  if (options.useNamedReferences || options.useShortestReferences) {\n    named = (0,_to_named_js__WEBPACK_IMPORTED_MODULE_1__.toNamed)(\n      code,\n      next,\n      options.omitOptionalSemicolons,\n      options.attribute\n    )\n  }\n\n  // Use the shortest numeric reference when requested.\n  // A simple algorithm would use decimal for all code points under 100, as\n  // those are shorter than hexadecimal:\n  //\n  // * `&#99;` vs `&#x63;` (decimal shorter)\n  // * `&#100;` vs `&#x64;` (equal)\n  //\n  // However, because we take `next` into consideration when `omit` is used,\n  // And it would be possible that decimals are shorter on bigger values as\n  // well if `next` is hexadecimal but not decimal, we instead compare both.\n  if (\n    (options.useShortestReferences || !named) &&\n    options.useShortestReferences\n  ) {\n    const decimal = (0,_to_decimal_js__WEBPACK_IMPORTED_MODULE_2__.toDecimal)(code, next, options.omitOptionalSemicolons)\n\n    if (decimal.length < numeric.length) {\n      numeric = decimal\n    }\n  }\n\n  return named &&\n    (!options.useShortestReferences || named.length < numeric.length)\n    ? named\n    : numeric\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/stringify-entities/lib/util/format-smart.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/stringify-entities/lib/util/to-decimal.js":
/*!********************************************************************!*\
  !*** ../../node_modules/stringify-entities/lib/util/to-decimal.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toDecimal: function() { return /* binding */ toDecimal; }\n/* harmony export */ });\nconst decimalRegex = /\\d/\n\n/**\n * Configurable ways to encode characters as decimal references.\n *\n * @param {number} code\n * @param {number} next\n * @param {boolean|undefined} omit\n * @returns {string}\n */\nfunction toDecimal(code, next, omit) {\n  const value = '&#' + String(code)\n  return omit && next && !decimalRegex.test(String.fromCharCode(next))\n    ? value\n    : value + ';'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvc3RyaW5naWZ5LWVudGl0aWVzL2xpYi91dGlsL3RvLWRlY2ltYWwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLFFBQVE7QUFDbkIsV0FBVyxtQkFBbUI7QUFDOUIsYUFBYTtBQUNiO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy9zdHJpbmdpZnktZW50aXRpZXMvbGliL3V0aWwvdG8tZGVjaW1hbC5qcz9iMTBhIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGRlY2ltYWxSZWdleCA9IC9cXGQvXG5cbi8qKlxuICogQ29uZmlndXJhYmxlIHdheXMgdG8gZW5jb2RlIGNoYXJhY3RlcnMgYXMgZGVjaW1hbCByZWZlcmVuY2VzLlxuICpcbiAqIEBwYXJhbSB7bnVtYmVyfSBjb2RlXG4gKiBAcGFyYW0ge251bWJlcn0gbmV4dFxuICogQHBhcmFtIHtib29sZWFufHVuZGVmaW5lZH0gb21pdFxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHRvRGVjaW1hbChjb2RlLCBuZXh0LCBvbWl0KSB7XG4gIGNvbnN0IHZhbHVlID0gJyYjJyArIFN0cmluZyhjb2RlKVxuICByZXR1cm4gb21pdCAmJiBuZXh0ICYmICFkZWNpbWFsUmVnZXgudGVzdChTdHJpbmcuZnJvbUNoYXJDb2RlKG5leHQpKVxuICAgID8gdmFsdWVcbiAgICA6IHZhbHVlICsgJzsnXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/stringify-entities/lib/util/to-decimal.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/stringify-entities/lib/util/to-hexadecimal.js":
/*!************************************************************************!*\
  !*** ../../node_modules/stringify-entities/lib/util/to-hexadecimal.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toHexadecimal: function() { return /* binding */ toHexadecimal; }\n/* harmony export */ });\nconst hexadecimalRegex = /[\\dA-Fa-f]/\n\n/**\n * Configurable ways to encode characters as hexadecimal references.\n *\n * @param {number} code\n * @param {number} next\n * @param {boolean|undefined} omit\n * @returns {string}\n */\nfunction toHexadecimal(code, next, omit) {\n  const value = '&#x' + code.toString(16).toUpperCase()\n  return omit && next && !hexadecimalRegex.test(String.fromCharCode(next))\n    ? value\n    : value + ';'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvc3RyaW5naWZ5LWVudGl0aWVzL2xpYi91dGlsL3RvLWhleGFkZWNpbWFsLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxRQUFRO0FBQ25CLFdBQVcsbUJBQW1CO0FBQzlCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvc3RyaW5naWZ5LWVudGl0aWVzL2xpYi91dGlsL3RvLWhleGFkZWNpbWFsLmpzPzJhMzIiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgaGV4YWRlY2ltYWxSZWdleCA9IC9bXFxkQS1GYS1mXS9cblxuLyoqXG4gKiBDb25maWd1cmFibGUgd2F5cyB0byBlbmNvZGUgY2hhcmFjdGVycyBhcyBoZXhhZGVjaW1hbCByZWZlcmVuY2VzLlxuICpcbiAqIEBwYXJhbSB7bnVtYmVyfSBjb2RlXG4gKiBAcGFyYW0ge251bWJlcn0gbmV4dFxuICogQHBhcmFtIHtib29sZWFufHVuZGVmaW5lZH0gb21pdFxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHRvSGV4YWRlY2ltYWwoY29kZSwgbmV4dCwgb21pdCkge1xuICBjb25zdCB2YWx1ZSA9ICcmI3gnICsgY29kZS50b1N0cmluZygxNikudG9VcHBlckNhc2UoKVxuICByZXR1cm4gb21pdCAmJiBuZXh0ICYmICFoZXhhZGVjaW1hbFJlZ2V4LnRlc3QoU3RyaW5nLmZyb21DaGFyQ29kZShuZXh0KSlcbiAgICA/IHZhbHVlXG4gICAgOiB2YWx1ZSArICc7J1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/stringify-entities/lib/util/to-hexadecimal.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/stringify-entities/lib/util/to-named.js":
/*!******************************************************************!*\
  !*** ../../node_modules/stringify-entities/lib/util/to-named.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toNamed: function() { return /* binding */ toNamed; }\n/* harmony export */ });\n/* harmony import */ var character_entities_legacy__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! character-entities-legacy */ \"(app-pages-browser)/../../node_modules/character-entities-legacy/index.js\");\n/* harmony import */ var character_entities_html4__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! character-entities-html4 */ \"(app-pages-browser)/../../node_modules/character-entities-html4/index.js\");\n/* harmony import */ var _constant_dangerous_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../constant/dangerous.js */ \"(app-pages-browser)/../../node_modules/stringify-entities/lib/constant/dangerous.js\");\n\n\n\n\nconst own = {}.hasOwnProperty\n\n/**\n * `characterEntitiesHtml4` but inverted.\n *\n * @type {Record<string, string>}\n */\nconst characters = {}\n\n/** @type {string} */\nlet key\n\nfor (key in character_entities_html4__WEBPACK_IMPORTED_MODULE_0__.characterEntitiesHtml4) {\n  if (own.call(character_entities_html4__WEBPACK_IMPORTED_MODULE_0__.characterEntitiesHtml4, key)) {\n    characters[character_entities_html4__WEBPACK_IMPORTED_MODULE_0__.characterEntitiesHtml4[key]] = key\n  }\n}\n\nconst notAlphanumericRegex = /[^\\dA-Za-z]/\n\n/**\n * Configurable ways to encode characters as named references.\n *\n * @param {number} code\n * @param {number} next\n * @param {boolean|undefined} omit\n * @param {boolean|undefined} attribute\n * @returns {string}\n */\nfunction toNamed(code, next, omit, attribute) {\n  const character = String.fromCharCode(code)\n\n  if (own.call(characters, character)) {\n    const name = characters[character]\n    const value = '&' + name\n\n    if (\n      omit &&\n      character_entities_legacy__WEBPACK_IMPORTED_MODULE_1__.characterEntitiesLegacy.includes(name) &&\n      !_constant_dangerous_js__WEBPACK_IMPORTED_MODULE_2__.dangerous.includes(name) &&\n      (!attribute ||\n        (next &&\n          next !== 61 /* `=` */ &&\n          notAlphanumericRegex.test(String.fromCharCode(next))))\n    ) {\n      return value\n    }\n\n    return value + ';'\n  }\n\n  return ''\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/stringify-entities/lib/util/to-named.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/zwitch/index.js":
/*!******************************************!*\
  !*** ../../node_modules/zwitch/index.js ***!
  \******************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   zwitch: function() { return /* binding */ zwitch; }\n/* harmony export */ });\n/**\n * @callback Handler\n *   Handle a value, with a certain ID field set to a certain value.\n *   The ID field is passed to `zwitch`, and it’s value is this function’s\n *   place on the `handlers` record.\n * @param {...any} parameters\n *   Arbitrary parameters passed to the zwitch.\n *   The first will be an object with a certain ID field set to a certain value.\n * @returns {any}\n *   Anything!\n */\n\n/**\n * @callback UnknownHandler\n *   Handle values that do have a certain ID field, but it’s set to a value\n *   that is not listed in the `handlers` record.\n * @param {unknown} value\n *   An object with a certain ID field set to an unknown value.\n * @param {...any} rest\n *   Arbitrary parameters passed to the zwitch.\n * @returns {any}\n *   Anything!\n */\n\n/**\n * @callback InvalidHandler\n *   Handle values that do not have a certain ID field.\n * @param {unknown} value\n *   Any unknown value.\n * @param {...any} rest\n *   Arbitrary parameters passed to the zwitch.\n * @returns {void|null|undefined|never}\n *   This should crash or return nothing.\n */\n\n/**\n * @template {InvalidHandler} [Invalid=InvalidHandler]\n * @template {UnknownHandler} [Unknown=UnknownHandler]\n * @template {Record<string, Handler>} [Handlers=Record<string, Handler>]\n * @typedef Options\n *   Configuration (required).\n * @property {Invalid} [invalid]\n *   Handler to use for invalid values.\n * @property {Unknown} [unknown]\n *   Handler to use for unknown values.\n * @property {Handlers} [handlers]\n *   Handlers to use.\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * Handle values based on a field.\n *\n * @template {InvalidHandler} [Invalid=InvalidHandler]\n * @template {UnknownHandler} [Unknown=UnknownHandler]\n * @template {Record<string, Handler>} [Handlers=Record<string, Handler>]\n * @param {string} key\n *   Field to switch on.\n * @param {Options<Invalid, Unknown, Handlers>} [options]\n *   Configuration (required).\n * @returns {{unknown: Unknown, invalid: Invalid, handlers: Handlers, (...parameters: Parameters<Handlers[keyof Handlers]>): ReturnType<Handlers[keyof Handlers]>, (...parameters: Parameters<Unknown>): ReturnType<Unknown>}}\n */\nfunction zwitch(key, options) {\n  const settings = options || {}\n\n  /**\n   * Handle one value.\n   *\n   * Based on the bound `key`, a respective handler will be called.\n   * If `value` is not an object, or doesn’t have a `key` property, the special\n   * “invalid” handler will be called.\n   * If `value` has an unknown `key`, the special “unknown” handler will be\n   * called.\n   *\n   * All arguments, and the context object, are passed through to the handler,\n   * and it’s result is returned.\n   *\n   * @this {unknown}\n   *   Any context object.\n   * @param {unknown} [value]\n   *   Any value.\n   * @param {...unknown} parameters\n   *   Arbitrary parameters passed to the zwitch.\n   * @property {Handler} invalid\n   *   Handle for values that do not have a certain ID field.\n   * @property {Handler} unknown\n   *   Handle values that do have a certain ID field, but it’s set to a value\n   *   that is not listed in the `handlers` record.\n   * @property {Handlers} handlers\n   *   Record of handlers.\n   * @returns {unknown}\n   *   Anything.\n   */\n  function one(value, ...parameters) {\n    /** @type {Handler|undefined} */\n    let fn = one.invalid\n    const handlers = one.handlers\n\n    if (value && own.call(value, key)) {\n      // @ts-expect-error Indexable.\n      const id = String(value[key])\n      // @ts-expect-error Indexable.\n      fn = own.call(handlers, id) ? handlers[id] : one.unknown\n    }\n\n    if (fn) {\n      return fn.call(this, value, ...parameters)\n    }\n  }\n\n  one.handlers = settings.handlers || {}\n  one.invalid = settings.invalid\n  one.unknown = settings.unknown\n\n  // @ts-expect-error: matches!\n  return one\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/zwitch/index.js\n"));

/***/ })

}]);