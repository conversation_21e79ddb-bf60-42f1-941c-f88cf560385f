/*!****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[3]!./src/components/artifacts/CodeRenderer.module.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************/
.CodeRenderer_codeMirrorCustom__xHgG_ {
  height: 100vh !important;
  overflow: hidden;
}

.CodeRenderer_codeMirrorCustom__xHgG_ .cm-editor {
  height: 100% !important;
  border: none !important;
}

.CodeRenderer_codeMirrorCustom__xHgG_ .cm-scroller {
  overflow: auto;
}

.CodeRenderer_codeMirrorCustom__xHgG_ .cm-gutters {
  height: 100% !important;
  border-right: none !important;
}

.CodeRenderer_codeMirrorCustom__xHgG_ .cm-focused {
  outline: none !important;
}

/*!**********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!../../node_modules/@blocknote/core/src/fonts/inter.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************/
/* Generated using https://google-webfonts-helper.herokuapp.com/fonts/inter?subsets=latin */

/* inter-100 - latin */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 100;
  src: local(""),
    url(/_next/static/media/inter-v12-latin-100.ac803252.woff2) format("woff2"),
    /* Chrome 26+, Opera 23+, Firefox 39+ */
      url(/_next/static/media/inter-v12-latin-100.ecd9c1ab.woff) format("woff"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
/* inter-200 - latin */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 200;
  src: local(""),
    url(/_next/static/media/inter-v12-latin-200.71082441.woff2) format("woff2"),
    /* Chrome 26+, Opera 23+, Firefox 39+ */
      url(/_next/static/media/inter-v12-latin-200.1e4cfb59.woff) format("woff"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
/* inter-300 - latin */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 300;
  src: local(""),
    url(/_next/static/media/inter-v12-latin-300.9c0edf75.woff2) format("woff2"),
    /* Chrome 26+, Opera 23+, Firefox 39+ */
      url(/_next/static/media/inter-v12-latin-300.66721718.woff) format("woff"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
/* inter-regular - latin */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 400;
  src: local(""),
    url(/_next/static/media/inter-v12-latin-regular.493934f7.woff2) format("woff2"),
    /* Chrome 26+, Opera 23+, Firefox 39+ */
      url(/_next/static/media/inter-v12-latin-regular.64aa2fed.woff) format("woff"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
/* inter-500 - latin */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 500;
  src: local(""),
    url(/_next/static/media/inter-v12-latin-500.b7be75b9.woff2) format("woff2"),
    /* Chrome 26+, Opera 23+, Firefox 39+ */
      url(/_next/static/media/inter-v12-latin-500.32f7e84c.woff) format("woff"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
/* inter-600 - latin */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 600;
  src: local(""),
    url(/_next/static/media/inter-v12-latin-600.a3e93aa0.woff2) format("woff2"),
    /* Chrome 26+, Opera 23+, Firefox 39+ */
      url(/_next/static/media/inter-v12-latin-600.30783081.woff) format("woff"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
/* inter-700 - latin */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 700;
  src: local(""),
    url(/_next/static/media/inter-v12-latin-700.7ddf3c11.woff2) format("woff2"),
    /* Chrome 26+, Opera 23+, Firefox 39+ */
      url(/_next/static/media/inter-v12-latin-700.14747af5.woff) format("woff"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
/* inter-800 - latin */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 800;
  src: local(""),
    url(/_next/static/media/inter-v12-latin-800.1c3ff413.woff2) format("woff2"),
    /* Chrome 26+, Opera 23+, Firefox 39+ */
      url(/_next/static/media/inter-v12-latin-800.405370de.woff) format("woff"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
/* inter-900 - latin */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 900;
  src: local(""),
    url(/_next/static/media/inter-v12-latin-900.307c1a48.woff2) format("woff2"),
    /* Chrome 26+, Opera 23+, Firefox 39+ */
      url(/_next/static/media/inter-v12-latin-900.945c1e63.woff) format("woff"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!../../node_modules/@blocknote/shadcn/dist/style.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
.bn-shadcn *,.bn-shadcn :before,.bn-shadcn :after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}.bn-shadcn :before,.bn-shadcn :after{--tw-content: ""}.bn-shadcn html,.bn-shadcn :host{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}.bn-shadcn body{margin:0;line-height:inherit}.bn-shadcn hr{height:0;color:inherit;border-top-width:1px}.bn-shadcn abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}.bn-shadcn h1,.bn-shadcn h2,.bn-shadcn h3,.bn-shadcn h4,.bn-shadcn h5,.bn-shadcn h6{font-size:inherit;font-weight:inherit}.bn-shadcn a{color:inherit;text-decoration:inherit}.bn-shadcn b,.bn-shadcn strong{font-weight:bolder}.bn-shadcn code,.bn-shadcn kbd,.bn-shadcn samp,.bn-shadcn pre{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}.bn-shadcn small{font-size:80%}.bn-shadcn sub,.bn-shadcn sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}.bn-shadcn sub{bottom:-.25em}.bn-shadcn sup{top:-.5em}.bn-shadcn table{text-indent:0;border-color:inherit;border-collapse:collapse}.bn-shadcn button,.bn-shadcn input,.bn-shadcn optgroup,.bn-shadcn select,.bn-shadcn textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}.bn-shadcn button,.bn-shadcn select{text-transform:none}.bn-shadcn button,.bn-shadcn input:where([type=button]),.bn-shadcn input:where([type=reset]),.bn-shadcn input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}.bn-shadcn :-moz-focusring{outline:auto}.bn-shadcn :-moz-ui-invalid{box-shadow:none}.bn-shadcn progress{vertical-align:baseline}.bn-shadcn ::-webkit-inner-spin-button,.bn-shadcn ::-webkit-outer-spin-button{height:auto}.bn-shadcn [type=search]{-webkit-appearance:textfield;outline-offset:-2px}.bn-shadcn ::-webkit-search-decoration{-webkit-appearance:none}.bn-shadcn ::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}.bn-shadcn summary{display:list-item}.bn-shadcn blockquote,.bn-shadcn dl,.bn-shadcn dd,.bn-shadcn h1,.bn-shadcn h2,.bn-shadcn h3,.bn-shadcn h4,.bn-shadcn h5,.bn-shadcn h6,.bn-shadcn hr,.bn-shadcn figure,.bn-shadcn p,.bn-shadcn pre{margin:0}.bn-shadcn fieldset{margin:0;padding:0}.bn-shadcn legend{padding:0}.bn-shadcn ol,.bn-shadcn ul,.bn-shadcn menu{list-style:none;margin:0;padding:0}.bn-shadcn dialog{padding:0}.bn-shadcn textarea{resize:vertical}.bn-shadcn input::-moz-placeholder,.bn-shadcn textarea::-moz-placeholder{opacity:1;color:#9ca3af}.bn-shadcn input::placeholder,.bn-shadcn textarea::placeholder{opacity:1;color:#9ca3af}.bn-shadcn button,.bn-shadcn [role=button]{cursor:pointer}.bn-shadcn :disabled{cursor:default}.bn-shadcn img,.bn-shadcn svg,.bn-shadcn video,.bn-shadcn canvas,.bn-shadcn audio,.bn-shadcn iframe,.bn-shadcn embed,.bn-shadcn object{display:block;vertical-align:middle}.bn-shadcn img,.bn-shadcn video{max-width:100%;height:auto}.bn-shadcn [hidden]{display:none}.bn-block-outer{line-height:1.5;transition:margin .2s}.bn-block{display:flex;flex-direction:column}.bn-block-content{display:flex;padding:3px 0;transition:font-size .2s;width:100%}.bn-block-content:before{transition:all .2s}.bn-block-content.ProseMirror-selectednode>*,.ProseMirror-selectednode>.bn-block-content>*{border-radius:4px;outline:4px solid rgb(100,160,255)}.bn-block-group .bn-block-group{margin-left:1.5em}.bn-block-group .bn-block-group>.bn-block-outer{position:relative}.bn-block-group .bn-block-group>.bn-block-outer:not([data-prev-depth-changed]):before{content:" ";display:inline;position:absolute;left:-20px;height:100%;transition:all .2s .1s}.bn-block-group .bn-block-group>.bn-block-outer[data-prev-depth-change="-2"]:before{height:0}.bn-inline-content code{font-family:monospace}[data-prev-depth-change="1"]{--x: 1}[data-prev-depth-change="2"]{--x: 2}[data-prev-depth-change="3"]{--x: 3}[data-prev-depth-change="4"]{--x: 4}[data-prev-depth-change="5"]{--x: 5}[data-prev-depth-change="-1"]{--x: -1}[data-prev-depth-change="-2"]{--x: -2}[data-prev-depth-change="-3"]{--x: -3}[data-prev-depth-change="-4"]{--x: -4}[data-prev-depth-change="-5"]{--x: -5}.bn-block-outer[data-prev-depth-change]{margin-left:calc(10px * var(--x))}.bn-block-outer[data-prev-depth-change] .bn-block-outer[data-prev-depth-change]{margin-left:0}[data-level="1"]{--level: 3em}[data-level="2"]{--level: 2em}[data-level="3"]{--level: 1.3em}[data-prev-level="1"]{--prev-level: 3em}[data-prev-level="2"]{--prev-level: 2em}[data-prev-level="3"]{--prev-level: 1.3em}.bn-block-outer[data-prev-type=heading]>.bn-block>.bn-block-content{font-size:var(--prev-level);font-weight:700}.bn-block-outer:not([data-prev-type])>.bn-block>.bn-block-content[data-content-type=heading]{font-size:var(--level);font-weight:700}.bn-block-content:before{margin-right:0;content:""}.bn-block-content[data-content-type=numberedListItem]{display:flex;gap:.5em}[data-content-type=numberedListItem]{--index: attr(data-index)}[data-prev-type=numberedListItem]{--prev-index: attr(data-prev-index)}.bn-block-outer[data-prev-type=numberedListItem]:not([data-prev-index=none])>.bn-block>.bn-block-content:before{content:var(--prev-index) "."}.bn-block-outer:not([data-prev-type])>.bn-block>.bn-block-content[data-content-type=numberedListItem]:before{content:var(--index) "."}.bn-block-content[data-content-type=bulletListItem]{display:flex;gap:.5em}.bn-block-content[data-content-type=checkListItem]>div{display:flex}.bn-block-content[data-content-type=checkListItem]>div>div>input{margin:0;margin-inline-end:.5em;cursor:pointer}.bn-block-content[data-content-type=checkListItem][data-checked=true] .bn-inline-content{text-decoration:line-through}.bn-block-content[data-text-alignment=center]{justify-content:center}.bn-block-content[data-text-alignment=right]{justify-content:flex-end}.bn-block-outer[data-prev-type=bulletListItem]>.bn-block>.bn-block-content:before{content:"•"}.bn-block-outer:not([data-prev-type])>.bn-block>.bn-block-content[data-content-type=bulletListItem]:before{content:"•"}[data-content-type=bulletListItem]~.bn-block-group>.bn-block-outer[data-prev-type=bulletListItem]>.bn-block>.bn-block-content:before{content:"◦"}[data-content-type=bulletListItem]~.bn-block-group>.bn-block-outer:not([data-prev-type])>.bn-block>.bn-block-content[data-content-type=bulletListItem]:before{content:"◦"}[data-content-type=bulletListItem]~.bn-block-group [data-content-type=bulletListItem]~.bn-block-group>.bn-block-outer[data-prev-type=bulletListItem]>.bn-block>.bn-block-content:before{content:"▪"}[data-content-type=bulletListItem]~.bn-block-group [data-content-type=bulletListItem]~.bn-block-group>.bn-block-outer:not([data-prev-type])>.bn-block>.bn-block-content[data-content-type=bulletListItem]:before{content:"▪"}[data-file-block] .bn-file-block-content-wrapper:has(.bn-add-file-button),[data-file-block] .bn-file-block-content-wrapper:has(.bn-file-default-preview){width:100%}[data-file-block] .bn-file-block-content-wrapper{cursor:pointer;display:flex;flex-direction:column;justify-content:stretch;-webkit-user-select:none;-moz-user-select:none;user-select:none}[data-file-block] .bn-add-file-button{align-items:center;background-color:#f2f1ee;border-radius:4px;color:#7d797a;display:flex;flex-direction:row;gap:10px;padding:12px;width:100%}.bn-editor[contenteditable=true] [data-file-block] .bn-add-file-button:hover{background-color:#e1e1e1}[data-file-block] .bn-add-file-button-icon{width:24px;height:24px}[data-file-block] .bn-add-file-button .bn-add-file-button-text{font-size:.9rem}[data-file-block] .bn-file-and-caption-wrapper{display:flex;flex-direction:column;border-radius:4px}[data-file-block] .bn-file-default-preview{align-items:center;border-radius:4px;display:flex;flex-direction:row;gap:4px;padding:4px;width:100%}[data-file-block] .bn-file-default-preview:hover,.ProseMirror-selectednode .bn-file-default-preview{background-color:#e1e1e1}[data-file-block] .bn-file-default-preview-icon{width:24px;height:24px}[data-file-block] .bn-visual-media-wrapper{display:flex;flex-direction:row;align-items:center;position:relative;width:-moz-fit-content;width:fit-content}[data-file-block] .bn-visual-media{border-radius:4px;max-width:100%}[data-file-block] .bn-visual-media-resize-handle{position:absolute;width:8px;height:30px;background-color:#000;border:1px solid white;border-radius:4px;cursor:ew-resize}[data-content-type=audio]>.bn-file-block-content-wrapper,.bn-audio{width:100%}[data-file-block] .bn-file-caption{font-size:.8em;padding-block:4px}[data-file-block] .bn-file-caption:empty{padding-block:0}.bn-inline-content:has(>.ProseMirror-trailingBreak:only-child):before{pointer-events:none;height:0;position:absolute;font-style:italic}[data-text-color=gray]{color:#9b9a97}[data-text-color=brown]{color:#64473a}[data-text-color=red]{color:#e03e3e}[data-text-color=orange]{color:#d9730d}[data-text-color=yellow]{color:#dfab01}[data-text-color=green]{color:#4d6461}[data-text-color=blue]{color:#0b6e99}[data-text-color=purple]{color:#6940a5}[data-text-color=pink]{color:#ad1a72}[data-background-color=gray]{background-color:#ebeced}[data-background-color=brown]{background-color:#e9e5e3}[data-background-color=red]{background-color:#fbe4e4}[data-background-color=orange]{background-color:#faebdd}[data-background-color=yellow]{background-color:#fbf3db}[data-background-color=green]{background-color:#ddedea}[data-background-color=blue]{background-color:#ddebf1}[data-background-color=purple]{background-color:#eae4f2}[data-background-color=pink]{background-color:#f4dfeb}[data-text-alignment=left]{justify-content:flex-start;text-align:left}[data-text-alignment=center]{justify-content:center;text-align:center}[data-text-alignment=right]{justify-content:flex-end;text-align:right}[data-text-alignment=justify]{justify-content:flex-start;text-align:justify}.ProseMirror .tableWrapper{overflow-x:auto}.ProseMirror table{border-collapse:collapse;table-layout:fixed;width:100%;overflow:hidden}.ProseMirror td,.ProseMirror th{vertical-align:top;box-sizing:border-box;position:relative}.ProseMirror .column-resize-handle{position:absolute;right:-2px;top:0;bottom:0;width:4px;z-index:20;background-color:#adf;pointer-events:none}.ProseMirror.resize-cursor{cursor:ew-resize;cursor:col-resize}.ProseMirror .selectedCell:after{z-index:2;position:absolute;content:"";left:0;right:0;top:0;bottom:0;background:#c8c8ff66;pointer-events:none}.bn-editor{outline:none;padding-inline:54px;--N800: #172b4d;--N40: #dfe1e6}.bn-root{box-sizing:border-box}.bn-root *,.bn-root *:before,.bn-root *:after{box-sizing:inherit}.bn-default-styles p,.bn-default-styles h1,.bn-default-styles h2,.bn-default-styles h3,.bn-default-styles li{margin:0;padding:0;font-size:inherit;min-width:2px!important}.bn-default-styles{font-size:16px;font-weight:400;font-family:Inter,SF Pro Display,-apple-system,BlinkMacSystemFont,Open Sans,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.bn-table-drop-cursor{position:absolute;z-index:20;background-color:#adf;pointer-events:none}.bn-drag-preview{position:absolute;top:0;left:0;padding:10px;opacity:.001}.collaboration-cursor__caret{border-left:1px solid #0d0d0d;border-right:1px solid #0d0d0d;margin-left:-1px;margin-right:-1px;pointer-events:none;position:relative;word-break:normal}.collaboration-cursor__label{border-radius:3px 3px 3px 0;color:#0d0d0d;font-size:12px;font-style:normal;font-weight:600;left:-1px;line-height:normal;padding:.1rem .3rem;position:absolute;top:-1.4em;-webkit-user-select:none;-moz-user-select:none;user-select:none;white-space:nowrap}.bn-editor table{width:auto!important}.bn-editor th,.bn-editor td{min-width:1em;border:1px solid #ddd;padding:3px 5px}.bn-editor .tableWrapper{margin:1em 0}.bn-editor th{font-weight:700;text-align:left}.bn-container{--bn-colors-editor-text: #3f3f3f;--bn-colors-editor-background: #ffffff;--bn-colors-menu-text: #3f3f3f;--bn-colors-menu-background: #ffffff;--bn-colors-tooltip-text: #3f3f3f;--bn-colors-tooltip-background: #efefef;--bn-colors-hovered-text: #3f3f3f;--bn-colors-hovered-background: #efefef;--bn-colors-selected-text: #ffffff;--bn-colors-selected-background: #3f3f3f;--bn-colors-disabled-text: #afafaf;--bn-colors-disabled-background: #efefef;--bn-colors-shadow: #cfcfcf;--bn-colors-border: #efefef;--bn-colors-side-menu: #cfcfcf;--bn-colors-highlights-gray-text: #9b9a97;--bn-colors-highlights-gray-background: #ebeced;--bn-colors-highlights-brown-text: #64473a;--bn-colors-highlights-brown-background: #e9e5e3;--bn-colors-highlights-red-text: #e03e3e;--bn-colors-highlights-red-background: #fbe4e4;--bn-colors-highlights-orange-text: #d9730d;--bn-colors-highlights-orange-background: #f6e9d9;--bn-colors-highlights-yellow-text: #dfab01;--bn-colors-highlights-yellow-background: #fbf3db;--bn-colors-highlights-green-text: #4d6461;--bn-colors-highlights-green-background: #ddedea;--bn-colors-highlights-blue-text: #0b6e99;--bn-colors-highlights-blue-background: #ddebf1;--bn-colors-highlights-purple-text: #6940a5;--bn-colors-highlights-purple-background: #eae4f2;--bn-colors-highlights-pink-text: #ad1a72;--bn-colors-highlights-pink-background: #f4dfeb;--bn-font-family: "Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, "Open Sans", "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;--bn-border-radius: 6px;--bn-shadow-medium: 0 4px 12px var(--bn-colors-shadow);--bn-shadow-light: 0 2px 6px var(--bn-colors-border);--bn-border: 1px solid var(--bn-colors-border);--bn-border-radius-small: max(var(--bn-border-radius) - 2px, 1px);--bn-border-radius-medium: var(--bn-border-radius);--bn-border-radius-large: max(var(--bn-border-radius) + 2px, 1px)}.bn-container[data-color-scheme=dark]{--bn-colors-editor-text: #cfcfcf;--bn-colors-editor-background: #1f1f1f;--bn-colors-menu-text: #cfcfcf;--bn-colors-menu-background: #1f1f1f;--bn-colors-tooltip-text: #cfcfcf;--bn-colors-tooltip-background: #161616;--bn-colors-hovered-text: #cfcfcf;--bn-colors-hovered-background: #161616;--bn-colors-selected-text: #cfcfcf;--bn-colors-selected-background: #0f0f0f;--bn-colors-disabled-text: #3f3f3f;--bn-colors-disabled-background: #161616;--bn-colors-shadow: #0f0f0f;--bn-colors-border: #161616;--bn-colors-side-menu: #7f7f7f;--bn-colors-highlights-gray-text: #bebdb8;--bn-colors-highlights-gray-background: #9b9a97;--bn-colors-highlights-brown-text: #8e6552;--bn-colors-highlights-brown-background: #64473a;--bn-colors-highlights-red-text: #ec4040;--bn-colors-highlights-red-background: #be3434;--bn-colors-highlights-orange-text: #e3790d;--bn-colors-highlights-orange-background: #b7600a;--bn-colors-highlights-yellow-text: #dfab01;--bn-colors-highlights-yellow-background: #b58b00;--bn-colors-highlights-green-text: #6b8b87;--bn-colors-highlights-green-background: #4d6461;--bn-colors-highlights-blue-text: #0e87bc;--bn-colors-highlights-blue-background: #0b6e99;--bn-colors-highlights-purple-text: #8552d7;--bn-colors-highlights-purple-background: #6940a5;--bn-colors-highlights-pink-text: #da208f;--bn-colors-highlights-pink-background: #ad1a72}.bn-container{font-family:var(--bn-font-family)}.bn-editor{background-color:var(--bn-colors-editor-background);border-radius:var(--bn-border-radius-large);color:var(--bn-colors-editor-text)}.bn-react-node-view-renderer{display:flex;flex-direction:column;width:100%}.bn-block-group .bn-block-group .bn-block-outer:not([data-prev-depth-changed]):before{border-left:1px solid var(--bn-colors-side-menu)}.bn-inline-content:has(>.ProseMirror-trailingBreak):before{color:var(--bn-colors-side-menu)}.bn-container .bn-color-icon{align-items:center;border:var(--bn-border);border-radius:var(--bn-border-radius-small);display:flex;justify-content:center}.bn-error-text{color:red;font-size:12px}[data-text-color=gray]{color:var(--bn-colors-highlights-gray-text)}[data-text-color=brown]{color:var(--bn-colors-highlights-brown-text)}[data-text-color=red]{color:var(--bn-colors-highlights-red-text)}[data-text-color=orange]{color:var(--bn-colors-highlights-orange-text)}[data-text-color=yellow]{color:var(--bn-colors-highlights-yellow-text)}[data-text-color=green]{color:var(--bn-colors-highlights-green-text)}[data-text-color=blue]{color:var(--bn-colors-highlights-blue-text)}[data-text-color=purple]{color:var(--bn-colors-highlights-purple-text)}[data-text-color=pink]{color:var(--bn-colors-highlights-pink-text)}[data-background-color=gray]{background-color:var(--bn-colors-highlights-gray-background)}[data-background-color=brown]{background-color:var(--bn-colors-highlights-brown-background)}[data-background-color=red]{background-color:var(--bn-colors-highlights-red-background)}[data-background-color=orange]{background-color:var(--bn-colors-highlights-orange-background)}[data-background-color=yellow]{background-color:var(--bn-colors-highlights-yellow-background)}[data-background-color=green]{background-color:var(--bn-colors-highlights-green-background)}[data-background-color=blue]{background-color:var(--bn-colors-highlights-blue-background)}[data-background-color=purple]{background-color:var(--bn-colors-highlights-purple-background)}[data-background-color=pink]{background-color:var(--bn-colors-highlights-pink-background)}.bn-side-menu{height:30px}.bn-side-menu[data-block-type=heading][data-level="1"]{height:78px}.bn-side-menu[data-block-type=heading][data-level="2"]{height:54px}.bn-side-menu[data-block-type=heading][data-level="3"]{height:37px}.bn-side-menu[data-block-type=file]{height:38px}.bn-side-menu[data-block-type=audio]{height:60px}.bn-side-menu[data-url=false]{height:54px}.bn-container :is(.bn-absolute){position:absolute}.bn-container :is(.bn-relative){position:relative}.bn-container :is(.bn-left-2){left:.5rem}.bn-container :is(.bn-z-50){z-index:50}.bn-container :is(.bn--mx-1){margin-left:-.25rem;margin-right:-.25rem}.bn-container :is(.bn-my-1){margin-top:.25rem;margin-bottom:.25rem}.bn-container :is(.bn-ml-auto){margin-left:auto}.bn-container :is(.bn-mt-2){margin-top:.5rem}.bn-container :is(.bn-flex){display:flex}.bn-container :is(.bn-inline-flex){display:inline-flex}.bn-container :is(.bn-h-10){height:2.5rem}.bn-container :is(.bn-h-11){height:2.75rem}.bn-container :is(.bn-h-2){height:.5rem}.bn-container :is(.bn-h-3){height:.75rem}.bn-container :is(.bn-h-3\.5){height:.875rem}.bn-container :is(.bn-h-4){height:1rem}.bn-container :is(.bn-h-9){height:2.25rem}.bn-container :is(.bn-h-\[var\(--radix-select-trigger-height\)\]){height:var(--radix-select-trigger-height)}.bn-container :is(.bn-h-fit){height:-moz-fit-content;height:fit-content}.bn-container :is(.bn-h-px){height:1px}.bn-container :is(.bn-max-h-96){max-height:24rem}.bn-container :is(.bn-w-10){width:2.5rem}.bn-container :is(.bn-w-2){width:.5rem}.bn-container :is(.bn-w-3){width:.75rem}.bn-container :is(.bn-w-3\.5){width:.875rem}.bn-container :is(.bn-w-4){width:1rem}.bn-container :is(.bn-w-72){width:18rem}.bn-container :is(.bn-w-80){width:20rem}.bn-container :is(.bn-w-fit){width:-moz-fit-content;width:fit-content}.bn-container :is(.bn-w-full){width:100%}.bn-container :is(.bn-min-w-\[8rem\]){min-width:8rem}.bn-container :is(.bn-min-w-\[var\(--radix-select-trigger-width\)\]){min-width:var(--radix-select-trigger-width)}.bn-container :is(.bn-max-w-none){max-width:none}.bn-container :is(.bn-flex-1){flex:1 1 0%}.bn-container :is(.bn-cursor-default){cursor:default}.bn-container :is(.bn-cursor-pointer){cursor:pointer}.bn-container :is(.bn-select-none){-webkit-user-select:none;-moz-user-select:none;user-select:none}.bn-container :is(.bn-flex-col){flex-direction:column}.bn-container :is(.bn-items-start){align-items:flex-start}.bn-container :is(.bn-items-center){align-items:center}.bn-container :is(.bn-justify-center){justify-content:center}.bn-container :is(.bn-justify-between){justify-content:space-between}.bn-container :is(.bn-gap-1){gap:.25rem}.bn-container :is(.bn-gap-2){gap:.5rem}.bn-container :is(.bn-space-y-1>:not([hidden])~:not([hidden])){--tw-space-y-reverse: 0;margin-top:calc(.25rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.25rem * var(--tw-space-y-reverse))}.bn-container :is(.bn-space-y-1\.5>:not([hidden])~:not([hidden])){--tw-space-y-reverse: 0;margin-top:calc(.375rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.375rem * var(--tw-space-y-reverse))}.bn-container :is(.bn-space-y-2>:not([hidden])~:not([hidden])){--tw-space-y-reverse: 0;margin-top:calc(.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.5rem * var(--tw-space-y-reverse))}.bn-container :is(.bn-overflow-auto){overflow:auto}.bn-container :is(.bn-overflow-hidden){overflow:hidden}.bn-container :is(.bn-whitespace-nowrap){white-space:nowrap}.bn-container :is(.bn-rounded-full){border-radius:9999px}.bn-container :is(.bn-rounded-lg){border-radius:var(--radius)}.bn-container :is(.bn-rounded-md){border-radius:calc(var(--radius) - 2px)}.bn-container :is(.bn-rounded-sm){border-radius:calc(var(--radius) - 4px)}.bn-container :is(.bn-border){border-width:1px}.bn-container :is(.bn-border-none){border-style:none}.bn-container :is(.bn-border-input){border-color:hsl(var(--input))}.bn-container :is(.bn-border-transparent){border-color:transparent}.bn-container :is(.bn-bg-background){background-color:hsl(var(--background))}.bn-container :is(.bn-bg-card){background-color:hsl(var(--card))}.bn-container :is(.bn-bg-destructive){background-color:hsl(var(--destructive))}.bn-container :is(.bn-bg-muted){background-color:hsl(var(--muted))}.bn-container :is(.bn-bg-popover){background-color:hsl(var(--popover))}.bn-container :is(.bn-bg-primary){background-color:hsl(var(--primary))}.bn-container :is(.bn-bg-secondary){background-color:hsl(var(--secondary))}.bn-container :is(.bn-bg-transparent){background-color:transparent}.bn-container :is(.bn-fill-current){fill:currentColor}.bn-container :is(.bn-p-0){padding:0}.bn-container :is(.bn-p-1){padding:.25rem}.bn-container :is(.bn-p-2){padding:.5rem}.bn-container :is(.bn-p-3){padding:.75rem}.bn-container :is(.bn-p-4){padding:1rem}.bn-container :is(.bn-p-6){padding:1.5rem}.bn-container :is(.bn-px-2){padding-left:.5rem;padding-right:.5rem}.bn-container :is(.bn-px-2\.5){padding-left:.625rem;padding-right:.625rem}.bn-container :is(.bn-px-3){padding-left:.75rem;padding-right:.75rem}.bn-container :is(.bn-px-4){padding-left:1rem;padding-right:1rem}.bn-container :is(.bn-px-5){padding-left:1.25rem;padding-right:1.25rem}.bn-container :is(.bn-px-8){padding-left:2rem;padding-right:2rem}.bn-container :is(.bn-py-0){padding-top:0;padding-bottom:0}.bn-container :is(.bn-py-0\.5){padding-top:.125rem;padding-bottom:.125rem}.bn-container :is(.bn-py-1){padding-top:.25rem;padding-bottom:.25rem}.bn-container :is(.bn-py-1\.5){padding-top:.375rem;padding-bottom:.375rem}.bn-container :is(.bn-py-2){padding-top:.5rem;padding-bottom:.5rem}.bn-container :is(.bn-pl-8){padding-left:2rem}.bn-container :is(.bn-pr-2){padding-right:.5rem}.bn-container :is(.bn-pt-0){padding-top:0}.bn-container :is(.bn-text-2xl){font-size:1.5rem;line-height:2rem}.bn-container :is(.bn-text-base){font-size:1rem;line-height:1.5rem}.bn-container :is(.bn-text-sm){font-size:.875rem;line-height:1.25rem}.bn-container :is(.bn-text-xs){font-size:.75rem;line-height:1rem}.bn-container :is(.bn-font-medium){font-weight:500}.bn-container :is(.bn-font-semibold){font-weight:600}.bn-container :is(.bn-leading-none){line-height:1}.bn-container :is(.bn-tracking-tight){letter-spacing:-.025em}.bn-container :is(.bn-tracking-widest){letter-spacing:.1em}.bn-container :is(.bn-text-card-foreground){color:hsl(var(--card-foreground))}.bn-container :is(.bn-text-destructive){color:hsl(var(--destructive))}.bn-container :is(.bn-text-destructive-foreground){color:hsl(var(--destructive-foreground))}.bn-container :is(.bn-text-foreground){color:hsl(var(--foreground))}.bn-container :is(.bn-text-gray-400){--tw-text-opacity: 1;color:rgb(156 163 175 / var(--tw-text-opacity))}.bn-container :is(.bn-text-muted-foreground){color:hsl(var(--muted-foreground))}.bn-container :is(.bn-text-popover-foreground){color:hsl(var(--popover-foreground))}.bn-container :is(.bn-text-primary){color:hsl(var(--primary))}.bn-container :is(.bn-text-primary-foreground){color:hsl(var(--primary-foreground))}.bn-container :is(.bn-text-secondary-foreground){color:hsl(var(--secondary-foreground))}.bn-container :is(.bn-underline-offset-4){text-underline-offset:4px}.bn-container :is(.bn-opacity-50){opacity:.5}.bn-container :is(.bn-opacity-60){opacity:.6}.bn-container :is(.bn-shadow-lg){--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.bn-container :is(.bn-shadow-md){--tw-shadow: 0 4px 6px -1px rgb(0 0 0 / .1), 0 2px 4px -2px rgb(0 0 0 / .1);--tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.bn-container :is(.bn-shadow-none){--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.bn-container :is(.bn-shadow-sm){--tw-shadow: 0 1px 2px 0 rgb(0 0 0 / .05);--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.bn-container :is(.bn-outline-none){outline:2px solid transparent;outline-offset:2px}.bn-container :is(.bn-ring-offset-background){--tw-ring-offset-color: hsl(var(--background))}.bn-container :is(.bn-transition-all){transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.bn-container :is(.bn-transition-colors){transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}@keyframes enter{0%{opacity:var(--tw-enter-opacity, 1);transform:translate3d(var(--tw-enter-translate-x, 0),var(--tw-enter-translate-y, 0),0) scale3d(var(--tw-enter-scale, 1),var(--tw-enter-scale, 1),var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0))}}@keyframes exit{to{opacity:var(--tw-exit-opacity, 1);transform:translate3d(var(--tw-exit-translate-x, 0),var(--tw-exit-translate-y, 0),0) scale3d(var(--tw-exit-scale, 1),var(--tw-exit-scale, 1),var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0))}}.bn-container :is(.bn-animate-in){animation-name:enter;animation-duration:.15s;--tw-enter-opacity: initial;--tw-enter-scale: initial;--tw-enter-rotate: initial;--tw-enter-translate-x: initial;--tw-enter-translate-y: initial}.bn-container :is(.bn-fade-in-0){--tw-enter-opacity: 0}.bn-container :is(.bn-zoom-in-95){--tw-enter-scale: .95}.bn-shadcn{--background: 0 0% 100%;--foreground: 222.2 84% 4.9%;--card: 0 0% 100%;--card-foreground: 222.2 84% 4.9%;--popover: 0 0% 100%;--popover-foreground: 222.2 84% 4.9%;--primary: 222.2 47.4% 11.2%;--primary-foreground: 210 40% 98%;--secondary: 210 40% 96.1%;--secondary-foreground: 222.2 47.4% 11.2%;--muted: 210 40% 96.1%;--muted-foreground: 215.4 16.3% 46.9%;--accent: 210 40% 96.1%;--accent-foreground: 222.2 47.4% 11.2%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 210 40% 98%;--border: 214.3 31.8% 91.4%;--input: 214.3 31.8% 91.4%;--ring: 222.2 84% 4.9%;--radius: .5rem}.bn-shadcn.dark{--background: 222.2 84% 4.9%;--foreground: 210 40% 98%;--card: 222.2 84% 4.9%;--card-foreground: 210 40% 98%;--popover: 222.2 84% 4.9%;--popover-foreground: 210 40% 98%;--primary: 210 40% 98%;--primary-foreground: 222.2 47.4% 11.2%;--secondary: 217.2 32.6% 17.5%;--secondary-foreground: 210 40% 98%;--muted: 217.2 32.6% 17.5%;--muted-foreground: 215 20.2% 65.1%;--accent: 217.2 32.6% 17.5%;--accent-foreground: 210 40% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 210 40% 98%;--border: 217.2 32.6% 17.5%;--input: 217.2 32.6% 17.5%;--ring: 212.7 26.8% 83.9%}.bn-shadcn *{border-color:hsl(var(--border))}.bn-shadcn .bn-editor{background-color:hsl(var(--background));color:hsl(var(--foreground))}.bn-shadcn [data-radix-popper-content-wrapper]{z-index:99999!important}.bn-shadcn .bn-editor:focus-visible{outline:none}.bn-shadcn .bn-side-menu{align-items:center;display:flex;justify-content:center}.bn-shadcn .bn-side-menu .bn-button{padding:0;height:24px}.bn-shadcn .bn-select{max-height:var(--radix-select-content-available-height)}.bn-shadcn .bn-menu-dropdown{max-height:var(--radix-dropdown-menu-content-available-height)}.bn-shadcn .bn-color-picker-dropdown{overflow:auto}.bn-shadcn .bn-suggestion-menu{height:-moz-fit-content;height:fit-content;max-height:100%}.bn-shadcn .bn-suggestion-menu-item[aria-selected=true],.bn-shadcn .bn-suggestion-menu-item:hover{background-color:hsl(var(--accent))}.bn-shadcn .bn-grid-suggestion-menu{background:var(--bn-colors-menu-background);border-radius:var(--bn-border-radius-large);box-shadow:var(--bn-shadow-medium);display:grid;gap:7px;height:-moz-fit-content;height:fit-content;justify-items:center;max-height:min(500px,100%);overflow-y:auto;padding:20px}.bn-shadcn .bn-grid-suggestion-menu-item{align-items:center;border-radius:var(--bn-border-radius-large);cursor:pointer;display:flex;font-size:24px;height:32px;justify-content:center;margin:2px;padding:4px;width:32px}.bn-shadcn .bn-grid-suggestion-menu-item[aria-selected=true],.bn-shadcn .bn-grid-suggestion-menu-item:hover{background-color:var(--bn-colors-hovered-background)}.bn-shadcn .bn-grid-suggestion-menu-empty-item,.bn-shadcn .bn-grid-suggestion-menu-loader{align-items:center;color:var(--bn-colors-menu-text);display:flex;font-size:14px;font-weight:500;height:32px;justify-content:center}.bn-shadcn .bn-grid-suggestion-menu-loader span{background-color:hsl(var(--accent))}.bn-container :is(.file\:bn-border-0)::file-selector-button{border-width:0px}.bn-container :is(.file\:bn-bg-transparent)::file-selector-button{background-color:transparent}.bn-container :is(.file\:bn-text-sm)::file-selector-button{font-size:.875rem;line-height:1.25rem}.bn-container :is(.file\:bn-font-medium)::file-selector-button{font-weight:500}.bn-container :is(.placeholder\:bn-text-muted-foreground)::-moz-placeholder{color:hsl(var(--muted-foreground))}.bn-container :is(.placeholder\:bn-text-muted-foreground)::placeholder{color:hsl(var(--muted-foreground))}.bn-container :is(.hover\:bn-bg-accent:hover){background-color:hsl(var(--accent))}.bn-container :is(.hover\:bn-bg-destructive\/80:hover){background-color:hsl(var(--destructive) / .8)}.bn-container :is(.hover\:bn-bg-destructive\/90:hover){background-color:hsl(var(--destructive) / .9)}.bn-container :is(.hover\:bn-bg-muted:hover){background-color:hsl(var(--muted))}.bn-container :is(.hover\:bn-bg-primary\/80:hover){background-color:hsl(var(--primary) / .8)}.bn-container :is(.hover\:bn-bg-primary\/90:hover){background-color:hsl(var(--primary) / .9)}.bn-container :is(.hover\:bn-bg-secondary\/80:hover){background-color:hsl(var(--secondary) / .8)}.bn-container :is(.hover\:bn-text-accent-foreground:hover){color:hsl(var(--accent-foreground))}.bn-container :is(.hover\:bn-text-muted-foreground:hover){color:hsl(var(--muted-foreground))}.bn-container :is(.hover\:bn-underline:hover){text-decoration-line:underline}.bn-container :is(.focus\:bn-bg-accent:focus){background-color:hsl(var(--accent))}.bn-container :is(.focus\:bn-text-accent-foreground:focus){color:hsl(var(--accent-foreground))}.bn-container :is(.focus\:bn-outline-none:focus){outline:2px solid transparent;outline-offset:2px}.bn-container :is(.focus\:bn-ring-2:focus){--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.bn-container :is(.focus\:bn-ring-ring:focus){--tw-ring-color: hsl(var(--ring))}.bn-container :is(.focus\:bn-ring-offset-2:focus){--tw-ring-offset-width: 2px}.bn-container :is(.focus-visible\:bn-outline-none:focus-visible){outline:2px solid transparent;outline-offset:2px}.bn-container :is(.focus-visible\:bn-ring-2:focus-visible){--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.bn-container :is(.focus-visible\:bn-ring-ring:focus-visible){--tw-ring-color: hsl(var(--ring))}.bn-container :is(.focus-visible\:bn-ring-offset-2:focus-visible){--tw-ring-offset-width: 2px}.bn-container :is(.disabled\:bn-pointer-events-none:disabled){pointer-events:none}.bn-container :is(.disabled\:bn-cursor-not-allowed:disabled){cursor:not-allowed}.bn-container :is(.disabled\:bn-opacity-50:disabled){opacity:.5}.bn-container :is(.bn-peer:disabled~.peer-disabled\:bn-cursor-not-allowed){cursor:not-allowed}.bn-container :is(.bn-peer:disabled~.peer-disabled\:bn-opacity-70){opacity:.7}.bn-container :is(.data-\[disabled\]\:bn-pointer-events-none[data-disabled]){pointer-events:none}.bn-container :is(.data-\[side\=bottom\]\:bn-translate-y-1[data-side=bottom]){--tw-translate-y: .25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.bn-container :is(.data-\[side\=left\]\:bn--translate-x-1[data-side=left]){--tw-translate-x: -.25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.bn-container :is(.data-\[side\=right\]\:bn-translate-x-1[data-side=right]){--tw-translate-x: .25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.bn-container :is(.data-\[side\=top\]\:bn--translate-y-1[data-side=top]){--tw-translate-y: -.25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.bn-container :is(.data-\[state\=active\]\:bn-bg-background[data-state=active]){background-color:hsl(var(--background))}.bn-container :is(.data-\[state\=on\]\:bn-bg-accent[data-state=on]){background-color:hsl(var(--accent))}.bn-container :is(.data-\[state\=open\]\:bn-bg-accent[data-state=open]){background-color:hsl(var(--accent))}.bn-container :is(.data-\[state\=active\]\:bn-text-foreground[data-state=active]){color:hsl(var(--foreground))}.bn-container :is(.data-\[state\=on\]\:bn-text-accent-foreground[data-state=on]){color:hsl(var(--accent-foreground))}.bn-container :is(.data-\[disabled\]\:bn-opacity-50[data-disabled]){opacity:.5}.bn-container :is(.data-\[state\=active\]\:bn-shadow-sm[data-state=active]){--tw-shadow: 0 1px 2px 0 rgb(0 0 0 / .05);--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.bn-container :is(.data-\[state\=open\]\:bn-animate-in[data-state=open]){animation-name:enter;animation-duration:.15s;--tw-enter-opacity: initial;--tw-enter-scale: initial;--tw-enter-rotate: initial;--tw-enter-translate-x: initial;--tw-enter-translate-y: initial}.bn-container :is(.data-\[state\=closed\]\:bn-animate-out[data-state=closed]){animation-name:exit;animation-duration:.15s;--tw-exit-opacity: initial;--tw-exit-scale: initial;--tw-exit-rotate: initial;--tw-exit-translate-x: initial;--tw-exit-translate-y: initial}.bn-container :is(.data-\[state\=closed\]\:bn-fade-out-0[data-state=closed]){--tw-exit-opacity: 0}.bn-container :is(.data-\[state\=open\]\:bn-fade-in-0[data-state=open]){--tw-enter-opacity: 0}.bn-container :is(.data-\[state\=closed\]\:bn-zoom-out-95[data-state=closed]){--tw-exit-scale: .95}.bn-container :is(.data-\[state\=open\]\:bn-zoom-in-95[data-state=open]){--tw-enter-scale: .95}.bn-container :is(.data-\[side\=bottom\]\:bn-slide-in-from-top-2[data-side=bottom]){--tw-enter-translate-y: -.5rem}.bn-container :is(.data-\[side\=left\]\:bn-slide-in-from-right-2[data-side=left]){--tw-enter-translate-x: .5rem}.bn-container :is(.data-\[side\=right\]\:bn-slide-in-from-left-2[data-side=right]){--tw-enter-translate-x: -.5rem}.bn-container :is(.data-\[side\=top\]\:bn-slide-in-from-bottom-2[data-side=top]){--tw-enter-translate-y: .5rem}.bn-container :is(.\[\&\>span\]\:bn-line-clamp-1>span){overflow:hidden;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1}

/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/components/word-editor/editor/assets/css/control/select.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************/
.ce-select-control-popup {
  max-width: 160px;
  min-width: 69px;
  max-height: 225px;
  position: absolute;
  z-index: 1;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
  box-sizing: border-box;
  margin: 5px 0;
  overflow-y: auto;
}

.ce-select-control-popup ul {
  list-style: none;
  padding: 3px 0;
  margin: 0;
  box-sizing: border-box;
}

.ce-select-control-popup ul li {
  font-size: 13px;
  padding: 0 20px;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #666;
  height: 36px;
  line-height: 36px;
  box-sizing: border-box;
  cursor: pointer;
}

.ce-select-control-popup ul li:hover {
  background-color: #EEF2FD;
}

.ce-select-control-popup ul li.active {
  color: var(--COLOR-HOVER, #5175f4);
  font-weight: 700;
}
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/components/word-editor/editor/assets/css/date/datePicker.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************/
.ce-date-container {
  display: none;
  width: 300px;
  overflow: hidden;
  left: 0;
  right: 0;
  position: absolute;
  z-index: 1;
  color: #606266;
  background: #ffffff;
  border-radius: 4px;
  padding: 10px;
  user-select: none;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}

.ce-date-container.active {
  display: block;
}

.ce-date-wrap {
  display: none;
}

.ce-date-wrap.active {
  display: block;
}

.ce-date-title {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: #606266;
  font-size: 16px;
}

.ce-date-title>span {
  display: inline-block;
}

.ce-date-title>span:not(.ce-date-title__now) {
  font-family: cursive;
  cursor: pointer;
}

.ce-date-title>span:not(.ce-date-title__now):hover {
  color: #5175F4;
}

.ce-date-title .ce-date-title__pre-year {
  width: 15%;
}

.ce-date-title .ce-date-title__pre-month {
  width: 15%;
}

.ce-date-title .ce-date-title__now {
  width: 40%;
}

.ce-date-title .ce-date-title__next-year {
  width: 15%;
}

.ce-date-title .ce-date-title__next-month {
  width: 15%;
}

.ce-date-week {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 15px;
  padding-bottom: 5px;
  border-bottom: 1px solid #e4e7ed;
}

.ce-date-week>span {
  list-style: none;
  width: calc(100%/7);
  text-align: center;
  color: #606266;
  font-size: 14px;
}

.ce-date-day {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 5px;
}

.ce-date-day>div {
  width: calc(100%/7);
  height: 40px;
  text-align: center;
  color: #606266;
  font-size: 14px;
  cursor: pointer;
  line-height: 40px;
  border-radius: 4px;
}

.ce-date-day>div:hover {
  color: #5175F4;
  opacity: .8;
}

.ce-date-day>div.active {
  color: #5175F4;
  font-weight: 700;
}

.ce-date-day>div.disable {
  color: #c0c4cc;
}

.ce-date-day>div.select {
  color: #fff;
  background-color: #5175F4;
}

.ce-time-wrap {
  display: none;
  padding: 10px;
  height: 286px;
}

.ce-time-wrap ::-webkit-scrollbar {
  width: 0;
}

.ce-time-wrap.active {
  display: flex;
}

.ce-time-wrap li {
  list-style: none;
}

.ce-time-wrap>li {
  width: 33.3%;
  height: 100%;
  text-align: center;
}

.ce-time-wrap>li>span {
  transform: translateY(-5px);
  display: inline-block;
}

.ce-time-wrap>li>ol {
  height: calc(100% - 20px);
  overflow-y: auto;
  border: 1px solid #e2e2e2;
  position: relative;
}

.ce-time-wrap>li:first-child>ol {
  border-right: 0;
}

.ce-time-wrap>li:last-child>ol {
  border-left: 0;
}

.ce-time-wrap>li>ol>li {
  line-height: 30px;
  cursor: pointer;
  transition: all .3s;
}

.ce-time-wrap>li>ol>li:hover {
  background-color: #eaeaea;
}

.ce-time-wrap>li>ol>li.active {
  color: #ffffff;
  background: #5175F4;
}

.ce-date-menu {
  width: 100%;
  height: 28px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-top: 10px;
  position: relative;
  border-top: 1px solid #e4e7ed;
}

.ce-date-menu button {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  border: 1px solid #dcdfe6;
  color: #606266;
  appearance: none;
  text-align: center;
  box-sizing: border-box;
  outline: none;
  margin: 0;
  transition: .1s;
  font-weight: 500;
  user-select: none;
  padding: 7px 15px;
  font-size: 12px;
  border-radius: 3px;
  margin-left: 10px;
}

.ce-date-menu button:hover {
  color: #5175F4;
  border-color: #5175F4;
}

.ce-date-menu button.ce-date-menu__time {
  border: 1px solid transparent;
  position: absolute;
  left: 0;
  margin-left: 0;
}

.ce-date-menu button.ce-date-menu__time:hover {
  color: #5175F4;
}
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/components/word-editor/editor/assets/css/block/block.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************/
.ce-block-item {
  position: absolute;
  z-index: 0;
  overflow: hidden;
  border-radius: 8px;
  background-color: #ffffff;
  border: 1px solid rgb(235 236 240);
}
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/components/word-editor/editor/assets/css/table/table.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************/
.ce-table-tool__row {
  position: absolute;
  width: 12px;
  border-radius: 6.5px;
  overflow: hidden;
  background-color: #E2E6ED;
}

.ce-table-tool__row .ce-table-tool__row__item {
  width: 100%;
  position: relative;
  cursor: pointer;
  transition: all .3s;
}

.ce-table-tool__row .ce-table-tool__row__item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 2px;
  width: 8px;
  height: 1px;
  background-color: #C0C6CF;
}

.ce-table-tool__row .ce-table-tool__row__item:hover {
  background-color: #dadce0;
}

.ce-table-tool__row .ce-table-tool__row__item:last-child:after {
  display: none;
}

.ce-table-tool__quick__add {
  width: 16px;
  height: 16px;
  position: absolute;
  border-radius: 50%;
  background-color: #E2E6ED;
  cursor: pointer;
}

.ce-table-tool__quick__add::after {
  content: '+';
  color: #ffffff;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -55%);
}

.ce-table-tool__select {
  width: 16px;
  height: 18px;
  position: absolute;
  border-radius: 3px;
  cursor: pointer;
}

.ce-table-tool__select:hover {
  background-color: #E2E6ED;
}

.ce-table-tool__select::after {
  content: ':::';
  color: #AAAAAB;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-75%, -50%) rotate(-90deg);
}

.ce-table-tool__col {
  position: absolute;
  height: 12px;
  border-radius: 6.5px;
  overflow: hidden;
  background-color: #E2E6ED;
  display: flex;
}

.ce-table-tool__col .ce-table-tool__col__item {
  height: 100%;
  position: relative;
  cursor: pointer;
  transition: all .3s;
}

.ce-table-tool__col .ce-table-tool__col__item::after {
  content: '';
  position: absolute;
  top: 2px;
  left: -1px;
  width: 1px;
  height: 8px;
  z-index: 1;
  background-color: #C0C6CF;
}

.ce-table-tool__col .ce-table-tool__col__item:hover {
  background-color: #dadce0;
}

.ce-table-tool__col .ce-table-tool__col__item:first-child:after {
  display: none;
}

.ce-table-tool__row .ce-table-tool__row__item.active,
.ce-table-tool__col .ce-table-tool__col__item.active {
  background-color: #C4D7FA;
}

.ce-table-tool__col .ce-table-tool__anchor {
  right: -5px;
  width: 10px;
  height: 12px;
  z-index: 9;
  position: absolute;
  cursor: col-resize;
}

.ce-table-tool__row .ce-table-tool__anchor {
  bottom: -5px;
  left: 0;
  width: 12px;
  height: 10px;
  z-index: 9;
  position: absolute;
  cursor: row-resize;
}

.ce-table-anchor__line {
  z-index: 9;
  position: absolute;
  border: 1px dotted #000000;
}

.ce-table-tool__border {
  position: absolute;
  z-index: 1;
  background: transparent;
  pointer-events: none;
}

.ce-table-tool__border__row {
  position: absolute;
  cursor: row-resize;
  pointer-events: auto;
}

.ce-table-tool__border__col {
  position: absolute;
  cursor: col-resize;
  pointer-events: auto;
}
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/components/word-editor/editor/assets/css/resizer/resizer.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************/
.ce-resizer-selection {
  position: absolute;
  border: 1px solid;
  pointer-events: none;
}

.ce-resizer-selection .resizer-handle {
  position: absolute;
  z-index: 9;
  width: 10px;
  height: 10px;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 30%);
  border-radius: 5px;
  border: 2px solid #ffffff;
  box-sizing: border-box;
  pointer-events: initial;
}

.ce-resizer-selection .handle-0 {
  cursor: nw-resize;
}

.ce-resizer-selection .handle-1 {
  cursor: n-resize;
}

.ce-resizer-selection .handle-2 {
  cursor: ne-resize;
}

.ce-resizer-selection .handle-3 {
  cursor: e-resize;
}

.ce-resizer-selection .handle-4 {
  cursor: se-resize;
}

.ce-resizer-selection .handle-5 {
  cursor: s-resize;
}

.ce-resizer-selection .handle-6 {
  cursor: sw-resize;
}

.ce-resizer-selection .handle-7 {
  cursor: w-resize;
}

.ce-resizer-size-view {
  display: flex;
  align-items: center;
  height: 20px;
  white-space: nowrap;
  position: absolute;
  z-index: 9;
  top: -30px;
  left: 0;
  opacity: .9;
  background-color: #000000;
  padding: 0 5px;
  border-radius: 4px;
}

.ce-resizer-size-view span {
  color: #ffffff;
  font-size: 12px;
}

.ce-resizer-image {
  position: absolute;
  opacity: 0.5;
}
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/components/word-editor/editor/assets/css/previewer/previewer.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************/
.ce-image-previewer {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #f2f4f7;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: previewerAnimation .3s;
}

@keyframes previewerAnimation {
  0% {
    opacity: 0.1;
  }

  100% {
    opacity: 1;
  }
}

.ce-image-previewer .image-close {
  width: 24px;
  height: 24px;
  display: inline-block;
  position: absolute;
  right: 50px;
  top: 30px;
  z-index: 99;
  cursor: pointer;
  background: url(/_next/static/media/close.5f642148.svg) no-repeat;
  background-size: 100% 100%;
  transition: all .3s;
  border-radius: 50%;
}

.ce-image-previewer .image-close:hover {
  background-color: #e2e6ed;
}

.ce-image-previewer .ce-image-container {
  position: relative;
}

.ce-image-previewer .ce-image-container img {
  cursor: move;
  position: relative;
}

.ce-image-previewer .ce-image-menu {
  height: 50px;
  position: absolute;
  bottom: 50px;
  z-index: 99;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ce-image-previewer .ce-image-menu i {
  width: 32px;
  height: 32px;
  margin: 0 8px;
  cursor: pointer;
  display: inline-block;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  transition: all .3s;
  border-radius: 50%;
}

.ce-image-previewer .ce-image-menu i:hover {
  background-color: #e2e6ed;
}

.ce-image-previewer .ce-image-menu i.zoom-in {
  background-image: url(/_next/static/media/zoom-in.dfff70ae.svg);
}

.ce-image-previewer .ce-image-menu i.zoom-out {
  background-image: url(/_next/static/media/zoom-out.678b0095.svg);
}

.ce-image-previewer .ce-image-menu i.rotate {
  background-image: url(/_next/static/media/rotate.f8dd393c.svg);
}

.ce-image-previewer .ce-image-menu i.original-size {
  background-image: url(/_next/static/media/original-size.7904df90.svg);
}

.ce-image-previewer .ce-image-menu i.image-download {
  background-image: url(/_next/static/media/image-download.93b5e81d.svg);
}

.ce-image-previewer .ce-image-menu .image-navigate {
  display: flex;
  align-items: center;
  justify-content: center;
}

.ce-image-previewer .ce-image-menu i.image-pre {
  background-image: url(/_next/static/media/image-pre.061d22d7.svg);
}

.ce-image-previewer .ce-image-menu i.image-next {
  background-image: url(/_next/static/media/image-next.48835108.svg);
}

.ce-image-previewer .ce-image-menu .image-count {
  color: #000000;
  font-size: 20px;
}

.ce-image-previewer .ce-image-menu i.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/components/word-editor/editor/assets/css/contextmenu/contextmenu.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************/
.ce-contextmenu-container {
  z-index: 9;
  position: fixed;
  display: none;
  padding: 4px;
  overflow-x: hidden;
  overflow-y: auto;
  background: #fff;
  box-shadow: 0 2px 12px 0 rgb(56 56 56 / 20%);
  border: 1px solid #e2e6ed;
  border-radius: 2px;
}

.ce-contextmenu-content {
  display: flex;
  flex-direction: column;
}

.ce-contextmenu-content .ce-contextmenu-sub-item::after {
  position: absolute;
  content: "";
  width: 16px;
  height: 16px;
  right: 12px;
  background: url(/_next/static/media/submenu-dropdown.c4c1462c.svg);
}

.ce-contextmenu-content .ce-contextmenu-item {
  min-width: 140px;
  padding: 0 32px 0 16px;
  height: 30px;
  display: flex;
  align-items: center;
  white-space: nowrap;
  box-sizing: border-box;
  cursor: pointer;
}

.ce-contextmenu-content .ce-contextmenu-item.hover {
  background: rgba(25, 55, 88, .04);
}

.ce-contextmenu-content .ce-contextmenu-item span {
  max-width: 300px;
  font-size: 12px;
  color: #3d4757;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.ce-contextmenu-content .ce-contextmenu-item span.ce-shortcut {
  color: #767c85;
  height: 30px;
  flex: 1;
  text-align: right;
  line-height: 30px;
  margin-left: 20px;
}

.ce-contextmenu-content .ce-contextmenu-item i {
  width: 16px;
  height: 16px;
  vertical-align: middle;
  display: inline-block;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  flex-shrink: 0;
  margin-right: 8px;
}

.ce-contextmenu-divider {
  background-color: #e2e6ed;
  margin: 4px 16px;
  height: 1px;
}

.ce-contextmenu-print {
  background-image: url(/_next/static/media/print.5d4519e0.svg);
}

.ce-contextmenu-image {
  background-image: url(/_next/static/media/image.53c67a0a.svg);
}

.ce-contextmenu-image-change {
  background-image: url(/_next/static/media/image-change.f5c6388e.svg);
}

.ce-contextmenu-insert-row-col {
  background-image: url(/_next/static/media/insert-row-col.1bd30eba.svg);
}

.ce-contextmenu-insert-top-row {
  background-image: url(/_next/static/media/insert-top-row.c0916a81.svg);
}

.ce-contextmenu-insert-bottom-row {
  background-image: url(/_next/static/media/insert-bottom-row.a5e6d131.svg);
}

.ce-contextmenu-insert-left-col {
  background-image: url(/_next/static/media/insert-left-col.b29b5845.svg);
}

.ce-contextmenu-insert-right-col {
  background-image: url(/_next/static/media/insert-right-col.3171b7b9.svg);
}

.ce-contextmenu-delete-row-col {
  background-image: url(/_next/static/media/delete-row-col.05e730cd.svg);
}

.ce-contextmenu-delete-row {
  background-image: url(/_next/static/media/delete-row.5470aff6.svg);
}

.ce-contextmenu-delete-col {
  background-image: url(/_next/static/media/delete-col.7a7e7d55.svg);
}

.ce-contextmenu-delete-table {
  background-image: url(/_next/static/media/delete-table.cc0c827d.svg);
}

.ce-contextmenu-merge-cell {
  background-image: url(/_next/static/media/merge-cell.6c57bc14.svg);
}

.ce-contextmenu-merge-cancel-cell {
  background-image: url(/_next/static/media/merge-cancel-cell.5ebec481.svg);
}

.ce-contextmenu-vertical-align {
  background-image: url(/_next/static/media/vertical-align.34596f51.svg);
}

.ce-contextmenu-vertical-align-top {
  background-image: url(/_next/static/media/vertical-align-top.21aaf188.svg);
}

.ce-contextmenu-vertical-align-middle {
  background-image: url(/_next/static/media/vertical-align-middle.934e6759.svg);
}

.ce-contextmenu-vertical-align-bottom {
  background-image: url(/_next/static/media/vertical-align-bottom.5396b506.svg);
}

.ce-contextmenu-border-all {
  background-image: url(/_next/static/media/table-border-all.e7336a14.svg);
}

.ce-contextmenu-border-empty {
  background-image: url(/_next/static/media/table-border-empty.4a6bf519.svg);
}

.ce-contextmenu-border-dash {
  background-image: url(/_next/static/media/table-border-dash.b012b734.svg);
}

.ce-contextmenu-border-external {
  background-image: url(/_next/static/media/table-border-external.987bf0b4.svg);
}

.ce-contextmenu-border-internal {
  background-image: url(/_next/static/media/table-border-internal.70d80655.svg);
}

.ce-contextmenu-border-td {
  background-image: url(/_next/static/media/table-border-td.bc5928da.svg);
}

.ce-contextmenu-border-td-top {
  background-image: url(/_next/static/media/table-border-td-top.8b73fc13.svg);
}

.ce-contextmenu-border-td-left {
  background-image: url(/_next/static/media/table-border-td-left.79f41805.svg);
}

.ce-contextmenu-border-td-bottom {
  background-image: url(/_next/static/media/table-border-td-bottom.151a7a7f.svg);
}

.ce-contextmenu-border-td-right {
  background-image: url(/_next/static/media/table-border-td-right.41bd790c.svg);
}

.ce-contextmenu-border-td-forward {
  background-image: url(/_next/static/media/table-border-td-forward.eb7af6fe.svg);
}

.ce-contextmenu-border-td-back {
  background-image: url(/_next/static/media/table-border-td-back.6a526144.svg);
}
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/components/word-editor/editor/assets/css/hyperlink/hyperlink.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************/
.ce-hyperlink-popup {
  background: #fff;
  box-shadow: 0 2px 12px 0 rgb(98 107 132 / 20%);
  border-radius: 2px;
  color: #3d4757;
  padding: 12px 16px;
  position: absolute;
  z-index: 1;
  text-align: center;
  display: none;
}

.ce-hyperlink-popup a {
  min-width: 100px;
  max-width: 300px;
  font-size: 12px;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  text-decoration: none;
  border-bottom-width: 1px;
  border-bottom-style: solid;
  color: #0000ff;
}
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/components/word-editor/editor/assets/css/zone/zone.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************/
.ce-zone-indicator>div {
  padding: 3px 6px;
  color: #000000;
  font-size: 12px;
  background: rgb(218 231 252);
  position: absolute;
  transform-origin: 0 0;
}

.ce-zone-indicator-border__top,
.ce-zone-indicator-border__bottom,
.ce-zone-indicator-border__left,
.ce-zone-indicator-border__right {
  display: block;
  position: absolute;
  z-index: 0;
}

.ce-zone-indicator-border__top {
  border-top: 2px dashed rgb(238, 238, 238);
}

.ce-zone-indicator-border__bottom {
  border-top: 2px dashed rgb(238, 238, 238);
  width: 100%;
}

.ce-zone-indicator-border__left {
  border-left: 2px dashed rgb(238, 238, 238);
}

.ce-zone-indicator-border__right {
  border-right: 2px dashed rgb(238, 238, 238);
}

.ce-zone-tip {
  display: none;
  align-items: center;
  height: 30px;
  white-space: nowrap;
  position: fixed;
  opacity: .9;
  background-color: #000000;
  padding: 0 5px;
  border-radius: 4px;
  z-index: 9;
  transition: all .3s;
  outline: none;
  user-select: none;
  pointer-events: none;
  transform: translate(10px, 10px);
}

.ce-zone-tip.show {
  display: flex;
}

.ce-zone-tip span {
  color: #ffffff;
  font-size: 12px;
}
/*!************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/components/word-editor/editor/assets/css/index.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************/
.ce-inputarea {
  width: 100px;
  height: 30px;
  min-width: 0;
  min-height: 0;
  margin: 0;
  padding: 0;
  left: 0;
  top: 0;
  letter-spacing: 0;
  font-size: 12px;
  position: absolute;
  z-index: -1;
  outline: none;
  resize: none;
  border: none;
  overflow: hidden;
  color: transparent;
  user-select: none;
  caret-color: transparent;
  background-color: transparent;
}

.ce-cursor {
  width: 1px;
  height: 20px;
  left: 0;
  right: 0;
  position: absolute;
  outline: none;
  background-color: #000000;
  pointer-events: none;
}

.ce-cursor.ce-cursor--animation {
  animation-duration: 1s;
  animation-iteration-count: infinite;
  animation-name: cursorAnimation;
}

@keyframes cursorAnimation {
  from {
    opacity: 1
  }

  13% {
    opacity: 0
  }

  50% {
    opacity: 0
  }

  63% {
    opacity: 1
  }

  to {
    opacity: 1
  }
}

.ce-float-image {
  position: absolute;
  opacity: 0.5;
  pointer-events: none;
}
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/components/word-editor/style.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
::-webkit-scrollbar {
  height: 16px;
  width: 16px;
  overflow: visible
}

::-webkit-scrollbar-button {
  width: 0;
  height: 0
}

::-webkit-scrollbar-corner {
  background: transparent
}

::-webkit-scrollbar-thumb {
  background-color: #ddd;
  background-clip: padding-box;
  border: 4px solid #f2f4f7;
  border-radius: 8px;
  min-height: 24px
}

::-webkit-scrollbar-thumb:hover {
  background-color: #c9c9c9
}

::-webkit-scrollbar-track {
  background: #f2f4f7;
  background-clip: padding-box
}

* {
  margin: 0;
  padding: 0;
}

body {
  background-color: #F2F4F7;
}

ul {
  list-style: none;
}

.menu {
  width: 100%;
  height: 60px;
  top: 0;
  z-index: 9;
  position: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F2F4F7;
  box-shadow: 0 2px 4px 0 transparent;
}

.menu-divider {
  width: 1px;
  height: 16px;
  margin: 0 8px;
  display: inline-block;
  background-color: #cfd2d8;
}

.menu-item {
  height: 24px;
  display: flex;
  align-items: center;
  position: relative;
}

.menu-item>div {
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 2px;
}

.menu-item>div:hover {
  background: rgba(25, 55, 88, .04);
}

.menu-item>div.active {
  background: rgba(25, 55, 88, .08);
}

.menu-item i {
  width: 16px;
  height: 16px;
  display: inline-block;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.menu-item>div>span {
  width: 16px;
  height: 3px;
  display: inline-block;
  border: 1px solid #e2e6ed;
}

.menu-item .select {
  border: none;
  font-size: 12px;
  line-height: 24px;
  user-select: none;
}

.menu-item .select::after {
  position: absolute;
  content: "";
  top: 11px;
  width: 0;
  height: 0;
  right: 2px;
  border-color: #767c85 transparent transparent;
  border-style: solid solid none;
  border-width: 3px 3px 0;
}

.menu-item .options {
  width: 70px;
  position: absolute;
  left: 0;
  top: 25px;
  padding: 10px;
  background: #fff;
  font-size: 14px;
  box-shadow: 0 2px 12px 0 rgb(56 56 56 / 20%);
  border: 1px solid #e2e6ed;
  border-radius: 2px;
  display: none;
}

.menu-item .options.visible {
  display: block;
}

.menu-item .options li {
  padding: 5px;
  margin: 5px 0;
  user-select: none;
  transition: all .3s;
}

.menu-item .options li:hover {
  background-color: #ebecef;
}

.menu-item .options li.active {
  background-color: #e2e6ed;
}

.menu-item .menu-item__font {
  width: 65px;
  position: relative;
}

.menu-item .menu-item__size {
  width: 50px;
  text-align: center;
  position: relative;
}

.menu-item__font .select,
.menu-item__size .select {
  width: 100%;
  height: 100%;
}

.menu-item__undo.no-allow,
.menu-item__redo.no-allow,
.menu-item>div.disable {
  color: #c0c4cc;
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}

.menu-item__undo i {
  background-image: url(/_next/static/media/undo.06632592.svg);
}

.menu-item__redo i {
  background-image: url(/_next/static/media/redo.dd7f31d5.svg);
}

.menu-item__painter i {
  background-image: url(/_next/static/media/painter.6f2b778a.svg);
}

.menu-item__format i {
  background-image: url(/_next/static/media/format.bbc01806.svg);
}

.menu-item__size-add i {
  background-image: url(/_next/static/media/size-add.c60f6adb.svg);
}

.menu-item__size-minus i {
  background-image: url(/_next/static/media/size-minus.45d97914.svg);
}

.menu-item__bold i {
  background-image: url(/_next/static/media/bold.1980276d.svg);
}

.menu-item__italic i {
  background-image: url(/_next/static/media/italic.32730496.svg);
}

.menu-item .menu-item__underline {
  width: 30px;
  position: relative;
}

.menu-item__underline>i {
  flex-shrink: 0;
  background-image: url(/_next/static/media/underline.c9be0c76.svg);
}

.menu-item__underline .select {
  width: 100%;
  height: 100%;
}

.menu-item .menu-item__underline .options {
  width: 128px;
}

.menu-item .menu-item__underline li {
  padding: 1px 5px;
}

.menu-item__underline li i {
  pointer-events: none;
}

.menu-item__underline li[data-decoration-style="solid"] {
  background-image: url(/_next/static/media/line-single.86256450.svg);
}

.menu-item__underline li[data-decoration-style="double"] {
  background-image: url(/_next/static/media/line-double.ee6a53bb.svg)
}

.menu-item__underline li[data-decoration-style="dashed"] {
  background-image: url(/_next/static/media/line-dash-small-gap.4fd0a443.svg);
}

.menu-item__underline li[data-decoration-style="dotted"] {
  background-image: url(/_next/static/media/line-dot.528f2101.svg);
}

.menu-item__underline li[data-decoration-style="wavy"] {
  background-image: url(/_next/static/media/line-wavy.049012b5.svg);
}

.menu-item__strikeout i {
  background-image: url(/_next/static/media/strikeout.d91507a4.svg);
}

.menu-item__superscript i {
  background-image: url(/_next/static/media/superscript.b18ec158.svg);
}

.menu-item__subscript i {
  background-image: url(/_next/static/media/subscript.23e98400.svg);
}

.menu-item__color,
.menu-item__highlight {
  display: flex;
  flex-direction: column;
}

.menu-item__color #color,
.menu-item__highlight #highlight {
  width: 1px;
  height: 1px;
  visibility: hidden;
  outline: none;
  appearance: none;
}

.menu-item__color i {
  background-image: url(/_next/static/media/color.43be6a71.svg);
}

.menu-item__color span {
  background-color: #000000;
}

.menu-item__highlight i {
  background-image: url(/_next/static/media/highlight.e346467d.svg);
}

.menu-item__highlight span {
  background-color: #ffff00;
}

.menu-item .menu-item__title {
  width: 60px;
  position: relative;
}

.menu-item__title .select {
  width: calc(100% - 20px);
  height: 100%;
}

.menu-item__title i {
  transform: translateX(-5px);
  background-image: url(/_next/static/media/title.84b24c65.svg);
}

.menu-item__title .options {
  width: 80px;
}

.menu-item__left i {
  background-image: url(/_next/static/media/left.34596f51.svg);
}

.menu-item__center i {
  background-image: url(/_next/static/media/center.bcce53c0.svg);
}

.menu-item__right i {
  background-image: url(/_next/static/media/right.d0d3eccd.svg);
}

.menu-item__alignment i {
  background-image: url(/_next/static/media/alignment.0e1eaf1d.svg);
}

.menu-item__justify i {
  background-image: url(/_next/static/media/justify.da75287c.svg);
}

.menu-item__row-margin {
  position: relative;
}

.menu-item__row-margin i {
  background-image: url(/_next/static/media/row-margin.f8864859.svg);
}

.menu-item__list {
  position: relative;
}

.menu-item__list i {
  background-image: url(/_next/static/media/list.654d4f1f.svg);
}

.menu-item__list .options {
  width: 110px;
}

.menu-item__list .options>ul>li * {
  pointer-events: none;
}

.menu-item__list .options>ul>li li {
  margin-left: 18px;
}

.menu-item__list .options>ul>li[data-list-style='checkbox'] li::marker {
  font-size: 11px;
}

.menu-item__image i {
  background-image: url(/_next/static/media/image.53c67a0a.svg);
}

.menu-item__image input {
  display: none;
}

.menu-item__table {
  position: relative;
}

.menu-item__table i {
  background-image: url(/_next/static/media/table.3a9f30a7.svg);
}

.menu-item .menu-item__table__collapse {
  width: 270px;
  height: 310px;
  background: #fff;
  box-shadow: 0 2px 12px 0 rgb(56 56 56 / 20%);
  border: 1px solid #e2e6ed;
  box-sizing: border-box;
  border-radius: 2px;
  position: absolute;
  display: none;
  z-index: 99;
  top: 25px;
  left: 0;
  padding: 14px 27px;
  cursor: auto;
}

.menu-item .menu-item__table__collapse .table-close {
  position: absolute;
  right: 10px;
  top: 5px;
  cursor: pointer;
}

.menu-item .menu-item__table__collapse .table-close:hover {
  color: #7d7e80;
}

.menu-item .menu-item__table__collapse:hover {
  background: #fff;
}

.menu-item .menu-item__table__collapse .table-title {
  display: flex;
  justify-content: flex-start;
  padding-bottom: 5px;
  border-bottom: 1px solid #e2e6ed;
}

.table-title span {
  font-size: 12px;
  color: #3d4757;
  display: inline;
  margin: 0;
}

.table-panel {
  cursor: pointer;
}

.table-panel .table-row {
  display: flex;
  flex-wrap: nowrap;
  margin-top: 10px;
  pointer-events: none;
}

.table-panel .table-cel {
  width: 16px;
  height: 16px;
  box-sizing: border-box;
  border: 1px solid #e2e6ed;
  background: #fff;
  position: relative;
  margin-right: 6px;
  pointer-events: none;
}

.table-panel .table-cel.active {
  border: 1px solid rgba(73, 145, 242, .2);
  background: rgba(73, 145, 242, .15);
}

.table-panel .table-row .table-cel:last-child {
  margin-right: 0;
}

.menu-item__hyperlink i {
  background-image: url(/_next/static/media/hyperlink.ea322b72.svg);
}

.menu-item__separator {
  position: relative;
}

.menu-item__separator>i {
  background-image: url(/_next/static/media/separator.f275fba7.svg);
}

.menu-item .menu-item__separator .options {
  width: 128px;
}

.menu-item .menu-item__separator li {
  padding: 1px 5px;
}

.menu-item__separator li i {
  pointer-events: none;
}

.menu-item__separator li[data-separator="0,0"] {
  background-image: url(/_next/static/media/line-single.86256450.svg);
}

.menu-item__separator li[data-separator="1,1"] {
  background-image: url(/_next/static/media/line-dot.528f2101.svg);
}

.menu-item__separator li[data-separator="3,1"] {
  background-image: url(/_next/static/media/line-dash-small-gap.4fd0a443.svg);
}

.menu-item__separator li[data-separator="4,4"] {
  background-image: url(/_next/static/media/line-dash-large-gap.e3782cb9.svg);
}

.menu-item__separator li[data-separator="7,3,3,3"] {
  background-image: url(/_next/static/media/line-dash-dot.fa10bc7b.svg);
}

.menu-item__separator li[data-separator="6,2,2,2,2,2"] {
  background-image: url(/_next/static/media/line-dash-dot-dot.32b761bd.svg);
}

.menu-item__watermark>i {
  background-image: url(/_next/static/media/watermark.6aab16b4.svg);
}

.menu-item__watermark {
  position: relative;
}

.menu-item__codeblock i {
  background-image: url(/_next/static/media/codeblock.b55a1d36.svg);
}

.menu-item__page-break i {
  background-image: url(/_next/static/media/page-break.bff27b62.svg);
}

.menu-item__control {
  position: relative;
}

.menu-item__control i {
  background-image: url(/_next/static/media/control.36cf9049.svg);
}

.menu-item__checkbox i {
  background-image: url(/_next/static/media/checkbox.4ef0ec91.svg);
}

.menu-item__radio i {
  background-image: url(/_next/static/media/radio.74d433d0.svg);
}

.menu-item__latex i {
  background-image: url(/_next/static/media/latex.cbd93e9b.svg);
}

.menu-item__date {
  position: relative;
}

.menu-item__date i {
  background-image: url(/_next/static/media/date.b9d5712a.svg);
}

.menu-item__date .options {
  width: 160px;
}

.menu-item__block i {
  background-image: url(/_next/static/media/block.6a581e52.svg);
}

.menu-item .menu-item__control .options {
  width: 55px;
}

.menu-item__search {
  position: relative;
}

.menu-item__search i {
  background-image: url(/_next/static/media/search.958c26d5.svg);
}

.menu-item .menu-item__search__collapse {
  width: 260px;
  height: 72px;
  box-sizing: border-box;
  position: absolute;
  display: none;
  z-index: 99;
  top: 25px;
  left: 0;
  background: #ffffff;
  box-shadow: 0px 5px 5px #e3dfdf;
}

.menu-item .menu-item__search__collapse:hover {
  background: #ffffff;
}

.menu-item .menu-item__search__collapse>div {
  width: 250px;
  height: 36px;
  padding: 0 5px;
  line-height: 36px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 4px;
}

.menu-item .menu-item__search__collapse>div input {
  width: 205px;
  height: 27px;
  appearance: none;
  background-color: #fff;
  background-image: none;
  border-radius: 4px;
  border: 1px solid #ebebeb;
  box-sizing: border-box;
  color: #606266;
  display: inline-block;
  line-height: 27px;
  outline: none;
  padding: 0 5px;
}

.menu-item .menu-item__search__collapse>div span {
  height: 100%;
  color: #dcdfe6;
  font-size: 25px;
  display: inline-block;
  border: 0;
  padding: 0 10px;
}

.menu-item .menu-item__search__collapse__replace button {
  display: inline-block;
  border: 1px solid #e2e6ed;
  border-radius: 2px;
  background: #fff;
  line-height: 22px;
  padding: 0 6px;
  white-space: nowrap;
  margin-left: 4px;
  cursor: pointer;
  font-size: 12px;
}

.menu-item .menu-item__search__collapse__replace button:hover {
  background: rgba(25, 55, 88, .04);
}

.menu-item .menu-item__search__collapse__search {
  position: relative;
}

.menu-item .menu-item__search__collapse__search label {
  right: 110px;
  font-size: 12px;
  color: #3d4757;
  position: absolute;
}

.menu-item .menu-item__search__collapse__search>input {
  padding: 5px 90px 5px 5px !important;
}

.menu-item .menu-item__search__collapse__search>div {
  width: 28px;
  height: 27px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  border-left: 1px solid #e2e6ed;
  transition: all .5s;
}

.menu-item .menu-item__search__collapse__search>div:hover {
  background-color: rgba(25, 55, 88, .04);
}

.menu-item .menu-item__search__collapse__search i {
  width: 6px;
  height: 8px;
  transform: translateY(1px);
}

.menu-item .menu-item__search__collapse__search .arrow-left {
  right: 76px;
}

.menu-item .menu-item__search__collapse__search .arrow-left i {
  background: url(/_next/static/media/arrow-left.6c97e20f.svg) no-repeat;
}

.menu-item .menu-item__search__collapse__search .arrow-right {
  right: 48px;
}

.menu-item .menu-item__search__collapse__search .arrow-right i {
  background: url(/_next/static/media/arrow-right.e6a91314.svg) no-repeat;
}

.menu-item__print i {
  background-image: url(/_next/static/media/print.5d4519e0.svg);
}

.catalog {
  width: 250px;
  position: fixed;
  left: 0;
  bottom: 0;
  top: 100px;
  padding: 0 20px 40px 20px;
}

.catalog .catalog__header {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e2e6ed;
}

.catalog .catalog__header span {
  color: #3d4757;
  font-size: 14px;
  font-weight: bold;
}

.catalog .catalog__header i {
  width: 16px;
  height: 16px;
  cursor: pointer;
  display: inline-block;
  background: url(/_next/static/media/close.aac4ab49.svg) no-repeat;
  transition: all .2s;
}

.catalog .catalog__header>div:hover {
  background: rgba(235, 238, 241);
}

.catalog__main {
  height: calc(100% - 60px);
  padding: 10px 0;
  overflow-y: auto;
  overflow-x: hidden;
}

.catalog__main .catalog-item {
  width: 100%;
  padding-left: 10px;
  box-sizing: border-box;
}

.catalog__main>.catalog-item {
  padding-left: 0;
}

.catalog__main .catalog-item .catalog-item__content {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.catalog__main .catalog-item .catalog-item__content:hover>span {
  color: #4991f2;
}

.catalog__main .catalog-item .catalog-item__content span {
  color: #3d4757;
  line-height: 30px;
  font-size: 12px;
  white-space: nowrap;
  cursor: pointer;
  user-select: none;
}

.editor>div {
  margin: 80px auto;
}

.ce-page-container canvas {
  box-shadow: rgb(158 161 165 / 40%) 0px 2px 12px 0px;
}

.comment {
  width: 250px;
  height: 650px;
  position: fixed;
  transform: translateX(420px);
  top: 200px;
  left: 50%;
  overflow-y: auto;
}

.comment-item {
  background: #ffffff;
  border: 1px solid #e2e6ed;
  position: relative;
  border-radius: 8px;
  padding: 15px;
  font-size: 14px;
  margin-bottom: 20px;
  cursor: pointer;
  transition: all .5s;
}

.comment-item:hover {
  border-color: #c0c6cf;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
}

.comment-item.active {
  border-color: #E99D00;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
}

.comment-item__title {
  height: 22px;
  position: relative;
  display: flex;
  align-items: center;
  color: #c1c6ce;
}

.comment-item__title span:first-child {
  background-color: #dbdbdb;
  width: 4px;
  height: 16px;
  margin-right: 5px;
  display: inline-block;
  border-radius: 999px;
}

.comment-item__title span:nth-child(2) {
  width: 200px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.comment-item__title i {
  width: 16px;
  height: 16px;
  cursor: pointer;
  position: absolute;
  right: -8px;
  top: -8px;
  background: url(/_next/static/media/close.aac4ab49.svg) no-repeat;
}

.comment-item__title i:hover {
  opacity: 0.6;
}

.comment-item__info {
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.comment-item__info>span:first-child {
  font-weight: 600;
}

.comment-item__info>span:last-child {
  color: #c1c6ce;
}

.comment-item__content {
  line-height: 22px;
}

.footer {
  width: 100%;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f2f4f7;
  z-index: 9;
  position: fixed;
  bottom: 0;
  left: 0;
  font-size: 12px;
  padding: 0 4px 0 20px;
  box-sizing: border-box;
}

.footer>div:first-child {
  display: flex;
  align-items: center;
}

.footer .catalog-mode {
  padding: 1px;
  position: relative;
}

.footer .catalog-mode i {
  width: 16px;
  height: 16px;
  margin-right: 5px;
  cursor: pointer;
  display: inline-block;
  background-image: url(/_next/static/media/catalog.345727c1.svg);
}

.footer .page-mode {
  padding: 1px;
  position: relative;
}

.footer .page-mode i {
  width: 16px;
  height: 16px;
  margin-right: 5px;
  cursor: pointer;
  display: inline-block;
  background-image: url(/_next/static/media/page-mode.2e423e95.svg);
}

.footer .options {
  width: 70px;
  position: absolute;
  left: 0;
  bottom: 25px;
  padding: 10px;
  background: #fff;
  font-size: 14px;
  box-shadow: 0 2px 12px 0 rgb(56 56 56 / 20%);
  border: 1px solid #e2e6ed;
  border-radius: 2px;
  display: none;
}

.footer .options.visible {
  display: block;
}

.footer .options li {
  padding: 5px;
  margin: 5px 0;
  user-select: none;
  transition: all .3s;
  text-align: center;
  cursor: pointer;
}

.footer .options li:hover {
  background-color: #ebecef;
}

.footer .options li.active {
  background-color: #e2e6ed;
}

.footer>div:first-child>span {
  display: inline-block;
  margin-right: 5px;
  letter-spacing: 1px;
}

.footer>div:last-child {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.footer>div:last-child>div {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer>div:last-child>div:hover {
  background: rgba(25, 55, 88, .04);
}

.footer>div:last-child i {
  width: 16px;
  height: 16px;
  display: inline-block;
  cursor: pointer;
}

.footer .editor-option i {
  background-image: url(/_next/static/media/option.421f665d.svg);
}

.footer .page-scale-minus i {
  background-image: url(/_next/static/media/page-scale-minus.61103120.svg);
}

.footer .page-scale-add i {
  background-image: url(/_next/static/media/page-scale-add.e24450ff.svg);
}

.footer .page-scale-percentage {
  cursor: pointer;
  user-select: none;
}

.footer .fullscreen i {
  background-image: url(/_next/static/media/request-fullscreen.2dd954a8.svg);
}

.footer .fullscreen.exist i {
  background-image: url(/_next/static/media/exit-fullscreen.4fb66a36.svg);
}

.footer .paper-margin i {
  background-image: url(/_next/static/media/paper-margin.da53c9a0.svg);
}

.footer .editor-mode {
  cursor: pointer;
  user-select: none;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.footer .paper-size {
  position: relative;
}

.footer .paper-size i {
  background-image: url(/_next/static/media/paper-size.7c26f580.svg);
}

.footer .paper-size .options {
  right: 0;
  left: unset;
}

.footer .paper-direction {
  position: relative;
}

.footer .paper-direction i {
  background-image: url(/_next/static/media/paper-direction.5d257ed5.svg);
}

.footer .paper-direction .options {
  right: 0;
  left: unset;
}

.ce-contextmenu-signature {
  background-image: url(/_next/static/media/signature.cb890fc7.svg);
}

.ce-contextmenu-word-tool {
  background-image: url(/_next/static/media/word-tool.aa37ec1a.svg);
}

/* 新增的样式用于解决布局问题 */
.word-editor-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

.word-editor-content {
  display: flex;
  flex: 1;
  margin-top: 60px; /* 为顶部菜单栏留出空间 */
  margin-bottom: 30px; /* 为底部功能栏留出空间 */
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.editor-container {
  flex: 1;
  overflow: auto;
  padding: 20px;
  position: relative;
}

.editor-container .editor {
  height: 100%;
  min-height: 100%;
}

/* 调整目录位置 */
.catalog {
  position: relative;
  top: unset;
  bottom: unset;
  left: unset;
  width: 250px;
  height: 100%;
  padding: 0 20px 20px 20px;
  flex-shrink: 0;
}

.catalog__main {
  height: calc(100% - 60px);
  padding: 10px 0;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 调整底部功能栏 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  margin-top: auto;
  z-index: 10;
}

/* 调整批注位置 */
.comment {
  position: relative;
  top: unset;
  left: unset;
  transform: none;
  width: 250px;
  height: 100%;
  flex-shrink: 0;
  margin-left: 20px;
}
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/components/word-editor/components/dialog/dialog.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************/
.dialog-mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: .5;
  background: #000000;
  z-index: 99;
}

.dialog-container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  z-index: 999;
  margin: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dialog {
  position: absolute;
  padding: 0 30px 30px;
  background: #ffffff;
  box-shadow: 0 2px 12px 0 rgb(56 56 56 / 20%);
  border: 1px solid #e2e6ed;
  border-radius: 2px;
}

.dialog-title {
  position: relative;
  border-bottom: 1px solid #e2e6ed;
  margin-bottom: 30px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dialog-title i {
  width: 16px;
  height: 16px;
  cursor: pointer;
  display: inline-block;
  background: url(/_next/static/media/close.aac4ab49.svg);
}

.dialog-option__item {
  margin-bottom: 18px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dialog-option__item span {
  margin-right: 12px;
  font-size: 14px;
  color: #3d4757;
  position: relative;
}

.dialog-option__item input,
.dialog-option__item textarea,
.dialog-option__item select {
  width: 276px;
  height: 30px;
  border-radius: 2px;
  border: 1px solid #d3d3d3;
  min-height: 30px;
  padding: 5px;
  box-sizing: border-box;
  outline: none;
  appearance: none;
  user-select: none;
  font-family: inherit;
}

.dialog-option__item input:focus,
.dialog-option__item textarea:focus {
  border-color: #4991f2;
}

.dialog-option__item--require::before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
  position: absolute;
  left: -8px;
}

.dialog-menu {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.dialog-menu button {
  position: relative;
  display: inline-block;
  border: 1px solid #e2e6ed;
  border-radius: 2px;
  background: #ffffff;
  line-height: 22px;
  padding: 0 16px;
  white-space: nowrap;
  cursor: pointer;
}

.dialog-menu button:hover {
  background: rgba(25, 55, 88, .04);
}

.dialog-menu__cancel {
  margin-right: 16px;
}

.dialog-menu button[type='submit'] {
  color: #ffffff;
  background: #4991f2;
  border-color: #4991f2;
}

.dialog-menu button[type='submit']:hover {
  background: #5b9cf3;
  border-color: #5b9cf3;
}
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/components/word-editor/components/signature/signature.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************/
.signature-mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: .5;
  background: #000000;
  z-index: 99;
}

.signature-container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  z-index: 999;
  margin: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.signature {
  position: absolute;
  padding: 0 30px 30px;
  background: #ffffff;
  box-shadow: 0 2px 12px 0 rgb(56 56 56 / 20%);
  border: 1px solid #e2e6ed;
  border-radius: 2px;
}

.signature-title {
  position: relative;
  border-bottom: 1px solid #e2e6ed;
  margin-bottom: 15px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.signature-title i {
  width: 16px;
  height: 16px;
  cursor: pointer;
  display: inline-block;
  background: url(/_next/static/media/close.aac4ab49.svg);
}

.signature-operation>div {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  color: #3d4757;
  user-select: none;
}

.signature-operation>div:hover {
  color: #6e7175;
}

.signature-operation>div i {
  width: 24px;
  height: 24px;
  display: inline-block;
}

.signature-operation__undo {
  background: url(/_next/static/media/signature-undo.2cd2496c.svg) no-repeat;
}

.signature-operation__trash {
  background: url(/_next/static/media/trash.11f9f58a.svg) no-repeat;
}

.signature-operation>div span {
  font-size: 12px;
  margin: 0 5px;
}

.signature-canvas {
  margin: 15px 0;
  user-select: none;
}

.signature-canvas canvas {
  background: #f3f5f7;
}

.signature-menu {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.signature-menu button {
  position: relative;
  display: inline-block;
  border: 1px solid #e2e6ed;
  border-radius: 2px;
  background: #ffffff;
  line-height: 22px;
  padding: 0 16px;
  white-space: nowrap;
  cursor: pointer;
}

.signature-menu button:hover {
  background: rgba(25, 55, 88, .04);
}

.signature-menu__cancel {
  margin-right: 16px;
}

.signature-menu button[type='submit'] {
  color: #ffffff;
  background: #4991f2;
  border-color: #4991f2;
}

.signature-menu button[type='submit']:hover {
  background: #5b9cf3;
  border-color: #5b9cf3;
}

.overflow-hidden {
  overflow: hidden !important;
}
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[3]!./src/components/assistant-select/edit-delete-dropdown.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************/
.edit-delete-dropdown_dropdownContent__ZDVd7 {
  width: 48px !important;
  min-width: 48px !important;
  padding: 4px !important;
}

.edit-delete-dropdown_dropdownContent__ZDVd7 > [role="menuitem"] {
  width: 100% !important;
  padding: 4px !important;
  display: flex;
  justify-content: center;
}

/*!***********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!../../node_modules/katex/dist/katex.min.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************/
@font-face{font-family:KaTeX_AMS;font-style:normal;font-weight:400;src:url(/_next/static/media/KaTeX_AMS-Regular.a79f1c31.woff2) format("woff2"),url(/_next/static/media/KaTeX_AMS-Regular.1608a09b.woff) format("woff"),url(/_next/static/media/KaTeX_AMS-Regular.4aafdb68.ttf) format("truetype")}@font-face{font-family:KaTeX_Caligraphic;font-style:normal;font-weight:700;src:url(/_next/static/media/KaTeX_Caligraphic-Bold.ec17d132.woff2) format("woff2"),url(/_next/static/media/KaTeX_Caligraphic-Bold.b6770918.woff) format("woff"),url(/_next/static/media/KaTeX_Caligraphic-Bold.cce5b8ec.ttf) format("truetype")}@font-face{font-family:KaTeX_Caligraphic;font-style:normal;font-weight:400;src:url(/_next/static/media/KaTeX_Caligraphic-Regular.55fac258.woff2) format("woff2"),url(/_next/static/media/KaTeX_Caligraphic-Regular.dad44a7f.woff) format("woff"),url(/_next/static/media/KaTeX_Caligraphic-Regular.07ef19e7.ttf) format("truetype")}@font-face{font-family:KaTeX_Fraktur;font-style:normal;font-weight:700;src:url(/_next/static/media/KaTeX_Fraktur-Bold.d42a5579.woff2) format("woff2"),url(/_next/static/media/KaTeX_Fraktur-Bold.9f256b85.woff) format("woff"),url(/_next/static/media/KaTeX_Fraktur-Bold.b18f59e1.ttf) format("truetype")}@font-face{font-family:KaTeX_Fraktur;font-style:normal;font-weight:400;src:url(/_next/static/media/KaTeX_Fraktur-Regular.d3c882a6.woff2) format("woff2"),url(/_next/static/media/KaTeX_Fraktur-Regular.7c187121.woff) format("woff"),url(/_next/static/media/KaTeX_Fraktur-Regular.ed38e79f.ttf) format("truetype")}@font-face{font-family:KaTeX_Main;font-style:normal;font-weight:700;src:url(/_next/static/media/KaTeX_Main-Bold.c3fb5ac2.woff2) format("woff2"),url(/_next/static/media/KaTeX_Main-Bold.d181c465.woff) format("woff"),url(/_next/static/media/KaTeX_Main-Bold.b74a1a8b.ttf) format("truetype")}@font-face{font-family:KaTeX_Main;font-style:italic;font-weight:700;src:url(/_next/static/media/KaTeX_Main-BoldItalic.6f2bb1df.woff2) format("woff2"),url(/_next/static/media/KaTeX_Main-BoldItalic.e3f82f9d.woff) format("woff"),url(/_next/static/media/KaTeX_Main-BoldItalic.70d8b0a5.ttf) format("truetype")}@font-face{font-family:KaTeX_Main;font-style:italic;font-weight:400;src:url(/_next/static/media/KaTeX_Main-Italic.8916142b.woff2) format("woff2"),url(/_next/static/media/KaTeX_Main-Italic.9024d815.woff) format("woff"),url(/_next/static/media/KaTeX_Main-Italic.47373d1e.ttf) format("truetype")}@font-face{font-family:KaTeX_Main;font-style:normal;font-weight:400;src:url(/_next/static/media/KaTeX_Main-Regular.0462f03b.woff2) format("woff2"),url(/_next/static/media/KaTeX_Main-Regular.7f51fe03.woff) format("woff"),url(/_next/static/media/KaTeX_Main-Regular.b7f8fe9b.ttf) format("truetype")}@font-face{font-family:KaTeX_Math;font-style:italic;font-weight:700;src:url(/_next/static/media/KaTeX_Math-BoldItalic.572d331f.woff2) format("woff2"),url(/_next/static/media/KaTeX_Math-BoldItalic.f1035d8d.woff) format("woff"),url(/_next/static/media/KaTeX_Math-BoldItalic.a879cf83.ttf) format("truetype")}@font-face{font-family:KaTeX_Math;font-style:italic;font-weight:400;src:url(/_next/static/media/KaTeX_Math-Italic.f28c23ac.woff2) format("woff2"),url(/_next/static/media/KaTeX_Math-Italic.5295ba48.woff) format("woff"),url(/_next/static/media/KaTeX_Math-Italic.939bc644.ttf) format("truetype")}@font-face{font-family:"KaTeX_SansSerif";font-style:normal;font-weight:700;src:url(/_next/static/media/KaTeX_SansSerif-Bold.8c5b5494.woff2) format("woff2"),url(/_next/static/media/KaTeX_SansSerif-Bold.bf59d231.woff) format("woff"),url(/_next/static/media/KaTeX_SansSerif-Bold.94e1e8dc.ttf) format("truetype")}@font-face{font-family:"KaTeX_SansSerif";font-style:italic;font-weight:400;src:url(/_next/static/media/KaTeX_SansSerif-Italic.3b1e59b3.woff2) format("woff2"),url(/_next/static/media/KaTeX_SansSerif-Italic.7c9bc82b.woff) format("woff"),url(/_next/static/media/KaTeX_SansSerif-Italic.b4c20c84.ttf) format("truetype")}@font-face{font-family:"KaTeX_SansSerif";font-style:normal;font-weight:400;src:url(/_next/static/media/KaTeX_SansSerif-Regular.ba21ed5f.woff2) format("woff2"),url(/_next/static/media/KaTeX_SansSerif-Regular.74048478.woff) format("woff"),url(/_next/static/media/KaTeX_SansSerif-Regular.d4d7ba48.ttf) format("truetype")}@font-face{font-family:KaTeX_Script;font-style:normal;font-weight:400;src:url(/_next/static/media/KaTeX_Script-Regular.03e9641d.woff2) format("woff2"),url(/_next/static/media/KaTeX_Script-Regular.07505710.woff) format("woff"),url(/_next/static/media/KaTeX_Script-Regular.fe9cbbe1.ttf) format("truetype")}@font-face{font-family:KaTeX_Size1;font-style:normal;font-weight:400;src:url(/_next/static/media/KaTeX_Size1-Regular.eae34984.woff2) format("woff2"),url(/_next/static/media/KaTeX_Size1-Regular.e1e279cb.woff) format("woff"),url(/_next/static/media/KaTeX_Size1-Regular.fabc004a.ttf) format("truetype")}@font-face{font-family:KaTeX_Size2;font-style:normal;font-weight:400;src:url(/_next/static/media/KaTeX_Size2-Regular.5916a24f.woff2) format("woff2"),url(/_next/static/media/KaTeX_Size2-Regular.57727022.woff) format("woff"),url(/_next/static/media/KaTeX_Size2-Regular.d6b476ec.ttf) format("truetype")}@font-face{font-family:KaTeX_Size3;font-style:normal;font-weight:400;src:url(/_next/static/media/KaTeX_Size3-Regular.b4230e7e.woff2) format("woff2"),url(/_next/static/media/KaTeX_Size3-Regular.9acaf01c.woff) format("woff"),url(/_next/static/media/KaTeX_Size3-Regular.a144ef58.ttf) format("truetype")}@font-face{font-family:KaTeX_Size4;font-style:normal;font-weight:400;src:url(/_next/static/media/KaTeX_Size4-Regular.10d95fd3.woff2) format("woff2"),url(/_next/static/media/KaTeX_Size4-Regular.7a996c9d.woff) format("woff"),url(/_next/static/media/KaTeX_Size4-Regular.fbccdabe.ttf) format("truetype")}@font-face{font-family:KaTeX_Typewriter;font-style:normal;font-weight:400;src:url(/_next/static/media/KaTeX_Typewriter-Regular.a8709e36.woff2) format("woff2"),url(/_next/static/media/KaTeX_Typewriter-Regular.6258592b.woff) format("woff"),url(/_next/static/media/KaTeX_Typewriter-Regular.d97aaf4a.ttf) format("truetype")}.katex{font:normal 1.21em KaTeX_Main,Times New Roman,serif;line-height:1.2;text-indent:0;text-rendering:auto}.katex *{-ms-high-contrast-adjust:none!important;border-color:currentColor}.katex .katex-version:after{content:"0.16.21"}.katex .katex-mathml{clip:rect(1px,1px,1px,1px);border:0;height:1px;overflow:hidden;padding:0;position:absolute;width:1px}.katex .katex-html>.newline{display:block}.katex .base{position:relative;white-space:nowrap;width:-webkit-min-content;width:-moz-min-content;width:min-content}.katex .base,.katex .strut{display:inline-block}.katex .textbf{font-weight:700}.katex .textit{font-style:italic}.katex .textrm{font-family:KaTeX_Main}.katex .textsf{font-family:KaTeX_SansSerif}.katex .texttt{font-family:KaTeX_Typewriter}.katex .mathnormal{font-family:KaTeX_Math;font-style:italic}.katex .mathit{font-family:KaTeX_Main;font-style:italic}.katex .mathrm{font-style:normal}.katex .mathbf{font-family:KaTeX_Main;font-weight:700}.katex .boldsymbol{font-family:KaTeX_Math;font-style:italic;font-weight:700}.katex .amsrm,.katex .mathbb,.katex .textbb{font-family:KaTeX_AMS}.katex .mathcal{font-family:KaTeX_Caligraphic}.katex .mathfrak,.katex .textfrak{font-family:KaTeX_Fraktur}.katex .mathboldfrak,.katex .textboldfrak{font-family:KaTeX_Fraktur;font-weight:700}.katex .mathtt{font-family:KaTeX_Typewriter}.katex .mathscr,.katex .textscr{font-family:KaTeX_Script}.katex .mathsf,.katex .textsf{font-family:KaTeX_SansSerif}.katex .mathboldsf,.katex .textboldsf{font-family:KaTeX_SansSerif;font-weight:700}.katex .mathitsf,.katex .mathsfit,.katex .textitsf{font-family:KaTeX_SansSerif;font-style:italic}.katex .mainrm{font-family:KaTeX_Main;font-style:normal}.katex .vlist-t{border-collapse:collapse;display:inline-table;table-layout:fixed}.katex .vlist-r{display:table-row}.katex .vlist{display:table-cell;position:relative;vertical-align:bottom}.katex .vlist>span{display:block;height:0;position:relative}.katex .vlist>span>span{display:inline-block}.katex .vlist>span>.pstrut{overflow:hidden;width:0}.katex .vlist-t2{margin-right:-2px}.katex .vlist-s{display:table-cell;font-size:1px;min-width:2px;vertical-align:bottom;width:2px}.katex .vbox{align-items:baseline;display:inline-flex;flex-direction:column}.katex .hbox{width:100%}.katex .hbox,.katex .thinbox{display:inline-flex;flex-direction:row}.katex .thinbox{max-width:0;width:0}.katex .msupsub{text-align:left}.katex .mfrac>span>span{text-align:center}.katex .mfrac .frac-line{border-bottom-style:solid;display:inline-block;width:100%}.katex .hdashline,.katex .hline,.katex .mfrac .frac-line,.katex .overline .overline-line,.katex .rule,.katex .underline .underline-line{min-height:1px}.katex .mspace{display:inline-block}.katex .clap,.katex .llap,.katex .rlap{position:relative;width:0}.katex .clap>.inner,.katex .llap>.inner,.katex .rlap>.inner{position:absolute}.katex .clap>.fix,.katex .llap>.fix,.katex .rlap>.fix{display:inline-block}.katex .llap>.inner{right:0}.katex .clap>.inner,.katex .rlap>.inner{left:0}.katex .clap>.inner>span{margin-left:-50%;margin-right:50%}.katex .rule{border:0 solid;display:inline-block;position:relative}.katex .hline,.katex .overline .overline-line,.katex .underline .underline-line{border-bottom-style:solid;display:inline-block;width:100%}.katex .hdashline{border-bottom-style:dashed;display:inline-block;width:100%}.katex .sqrt>.root{margin-left:.2777777778em;margin-right:-.5555555556em}.katex .fontsize-ensurer.reset-size1.size1,.katex .sizing.reset-size1.size1{font-size:1em}.katex .fontsize-ensurer.reset-size1.size2,.katex .sizing.reset-size1.size2{font-size:1.2em}.katex .fontsize-ensurer.reset-size1.size3,.katex .sizing.reset-size1.size3{font-size:1.4em}.katex .fontsize-ensurer.reset-size1.size4,.katex .sizing.reset-size1.size4{font-size:1.6em}.katex .fontsize-ensurer.reset-size1.size5,.katex .sizing.reset-size1.size5{font-size:1.8em}.katex .fontsize-ensurer.reset-size1.size6,.katex .sizing.reset-size1.size6{font-size:2em}.katex .fontsize-ensurer.reset-size1.size7,.katex .sizing.reset-size1.size7{font-size:2.4em}.katex .fontsize-ensurer.reset-size1.size8,.katex .sizing.reset-size1.size8{font-size:2.88em}.katex .fontsize-ensurer.reset-size1.size9,.katex .sizing.reset-size1.size9{font-size:3.456em}.katex .fontsize-ensurer.reset-size1.size10,.katex .sizing.reset-size1.size10{font-size:4.148em}.katex .fontsize-ensurer.reset-size1.size11,.katex .sizing.reset-size1.size11{font-size:4.976em}.katex .fontsize-ensurer.reset-size2.size1,.katex .sizing.reset-size2.size1{font-size:.8333333333em}.katex .fontsize-ensurer.reset-size2.size2,.katex .sizing.reset-size2.size2{font-size:1em}.katex .fontsize-ensurer.reset-size2.size3,.katex .sizing.reset-size2.size3{font-size:1.1666666667em}.katex .fontsize-ensurer.reset-size2.size4,.katex .sizing.reset-size2.size4{font-size:1.3333333333em}.katex .fontsize-ensurer.reset-size2.size5,.katex .sizing.reset-size2.size5{font-size:1.5em}.katex .fontsize-ensurer.reset-size2.size6,.katex .sizing.reset-size2.size6{font-size:1.6666666667em}.katex .fontsize-ensurer.reset-size2.size7,.katex .sizing.reset-size2.size7{font-size:2em}.katex .fontsize-ensurer.reset-size2.size8,.katex .sizing.reset-size2.size8{font-size:2.4em}.katex .fontsize-ensurer.reset-size2.size9,.katex .sizing.reset-size2.size9{font-size:2.88em}.katex .fontsize-ensurer.reset-size2.size10,.katex .sizing.reset-size2.size10{font-size:3.4566666667em}.katex .fontsize-ensurer.reset-size2.size11,.katex .sizing.reset-size2.size11{font-size:4.1466666667em}.katex .fontsize-ensurer.reset-size3.size1,.katex .sizing.reset-size3.size1{font-size:.7142857143em}.katex .fontsize-ensurer.reset-size3.size2,.katex .sizing.reset-size3.size2{font-size:.8571428571em}.katex .fontsize-ensurer.reset-size3.size3,.katex .sizing.reset-size3.size3{font-size:1em}.katex .fontsize-ensurer.reset-size3.size4,.katex .sizing.reset-size3.size4{font-size:1.1428571429em}.katex .fontsize-ensurer.reset-size3.size5,.katex .sizing.reset-size3.size5{font-size:1.2857142857em}.katex .fontsize-ensurer.reset-size3.size6,.katex .sizing.reset-size3.size6{font-size:1.4285714286em}.katex .fontsize-ensurer.reset-size3.size7,.katex .sizing.reset-size3.size7{font-size:1.7142857143em}.katex .fontsize-ensurer.reset-size3.size8,.katex .sizing.reset-size3.size8{font-size:2.0571428571em}.katex .fontsize-ensurer.reset-size3.size9,.katex .sizing.reset-size3.size9{font-size:2.4685714286em}.katex .fontsize-ensurer.reset-size3.size10,.katex .sizing.reset-size3.size10{font-size:2.9628571429em}.katex .fontsize-ensurer.reset-size3.size11,.katex .sizing.reset-size3.size11{font-size:3.5542857143em}.katex .fontsize-ensurer.reset-size4.size1,.katex .sizing.reset-size4.size1{font-size:.625em}.katex .fontsize-ensurer.reset-size4.size2,.katex .sizing.reset-size4.size2{font-size:.75em}.katex .fontsize-ensurer.reset-size4.size3,.katex .sizing.reset-size4.size3{font-size:.875em}.katex .fontsize-ensurer.reset-size4.size4,.katex .sizing.reset-size4.size4{font-size:1em}.katex .fontsize-ensurer.reset-size4.size5,.katex .sizing.reset-size4.size5{font-size:1.125em}.katex .fontsize-ensurer.reset-size4.size6,.katex .sizing.reset-size4.size6{font-size:1.25em}.katex .fontsize-ensurer.reset-size4.size7,.katex .sizing.reset-size4.size7{font-size:1.5em}.katex .fontsize-ensurer.reset-size4.size8,.katex .sizing.reset-size4.size8{font-size:1.8em}.katex .fontsize-ensurer.reset-size4.size9,.katex .sizing.reset-size4.size9{font-size:2.16em}.katex .fontsize-ensurer.reset-size4.size10,.katex .sizing.reset-size4.size10{font-size:2.5925em}.katex .fontsize-ensurer.reset-size4.size11,.katex .sizing.reset-size4.size11{font-size:3.11em}.katex .fontsize-ensurer.reset-size5.size1,.katex .sizing.reset-size5.size1{font-size:.5555555556em}.katex .fontsize-ensurer.reset-size5.size2,.katex .sizing.reset-size5.size2{font-size:.6666666667em}.katex .fontsize-ensurer.reset-size5.size3,.katex .sizing.reset-size5.size3{font-size:.7777777778em}.katex .fontsize-ensurer.reset-size5.size4,.katex .sizing.reset-size5.size4{font-size:.8888888889em}.katex .fontsize-ensurer.reset-size5.size5,.katex .sizing.reset-size5.size5{font-size:1em}.katex .fontsize-ensurer.reset-size5.size6,.katex .sizing.reset-size5.size6{font-size:1.1111111111em}.katex .fontsize-ensurer.reset-size5.size7,.katex .sizing.reset-size5.size7{font-size:1.3333333333em}.katex .fontsize-ensurer.reset-size5.size8,.katex .sizing.reset-size5.size8{font-size:1.6em}.katex .fontsize-ensurer.reset-size5.size9,.katex .sizing.reset-size5.size9{font-size:1.92em}.katex .fontsize-ensurer.reset-size5.size10,.katex .sizing.reset-size5.size10{font-size:2.3044444444em}.katex .fontsize-ensurer.reset-size5.size11,.katex .sizing.reset-size5.size11{font-size:2.7644444444em}.katex .fontsize-ensurer.reset-size6.size1,.katex .sizing.reset-size6.size1{font-size:.5em}.katex .fontsize-ensurer.reset-size6.size2,.katex .sizing.reset-size6.size2{font-size:.6em}.katex .fontsize-ensurer.reset-size6.size3,.katex .sizing.reset-size6.size3{font-size:.7em}.katex .fontsize-ensurer.reset-size6.size4,.katex .sizing.reset-size6.size4{font-size:.8em}.katex .fontsize-ensurer.reset-size6.size5,.katex .sizing.reset-size6.size5{font-size:.9em}.katex .fontsize-ensurer.reset-size6.size6,.katex .sizing.reset-size6.size6{font-size:1em}.katex .fontsize-ensurer.reset-size6.size7,.katex .sizing.reset-size6.size7{font-size:1.2em}.katex .fontsize-ensurer.reset-size6.size8,.katex .sizing.reset-size6.size8{font-size:1.44em}.katex .fontsize-ensurer.reset-size6.size9,.katex .sizing.reset-size6.size9{font-size:1.728em}.katex .fontsize-ensurer.reset-size6.size10,.katex .sizing.reset-size6.size10{font-size:2.074em}.katex .fontsize-ensurer.reset-size6.size11,.katex .sizing.reset-size6.size11{font-size:2.488em}.katex .fontsize-ensurer.reset-size7.size1,.katex .sizing.reset-size7.size1{font-size:.4166666667em}.katex .fontsize-ensurer.reset-size7.size2,.katex .sizing.reset-size7.size2{font-size:.5em}.katex .fontsize-ensurer.reset-size7.size3,.katex .sizing.reset-size7.size3{font-size:.5833333333em}.katex .fontsize-ensurer.reset-size7.size4,.katex .sizing.reset-size7.size4{font-size:.6666666667em}.katex .fontsize-ensurer.reset-size7.size5,.katex .sizing.reset-size7.size5{font-size:.75em}.katex .fontsize-ensurer.reset-size7.size6,.katex .sizing.reset-size7.size6{font-size:.8333333333em}.katex .fontsize-ensurer.reset-size7.size7,.katex .sizing.reset-size7.size7{font-size:1em}.katex .fontsize-ensurer.reset-size7.size8,.katex .sizing.reset-size7.size8{font-size:1.2em}.katex .fontsize-ensurer.reset-size7.size9,.katex .sizing.reset-size7.size9{font-size:1.44em}.katex .fontsize-ensurer.reset-size7.size10,.katex .sizing.reset-size7.size10{font-size:1.7283333333em}.katex .fontsize-ensurer.reset-size7.size11,.katex .sizing.reset-size7.size11{font-size:2.0733333333em}.katex .fontsize-ensurer.reset-size8.size1,.katex .sizing.reset-size8.size1{font-size:.3472222222em}.katex .fontsize-ensurer.reset-size8.size2,.katex .sizing.reset-size8.size2{font-size:.4166666667em}.katex .fontsize-ensurer.reset-size8.size3,.katex .sizing.reset-size8.size3{font-size:.4861111111em}.katex .fontsize-ensurer.reset-size8.size4,.katex .sizing.reset-size8.size4{font-size:.5555555556em}.katex .fontsize-ensurer.reset-size8.size5,.katex .sizing.reset-size8.size5{font-size:.625em}.katex .fontsize-ensurer.reset-size8.size6,.katex .sizing.reset-size8.size6{font-size:.6944444444em}.katex .fontsize-ensurer.reset-size8.size7,.katex .sizing.reset-size8.size7{font-size:.8333333333em}.katex .fontsize-ensurer.reset-size8.size8,.katex .sizing.reset-size8.size8{font-size:1em}.katex .fontsize-ensurer.reset-size8.size9,.katex .sizing.reset-size8.size9{font-size:1.2em}.katex .fontsize-ensurer.reset-size8.size10,.katex .sizing.reset-size8.size10{font-size:1.4402777778em}.katex .fontsize-ensurer.reset-size8.size11,.katex .sizing.reset-size8.size11{font-size:1.7277777778em}.katex .fontsize-ensurer.reset-size9.size1,.katex .sizing.reset-size9.size1{font-size:.2893518519em}.katex .fontsize-ensurer.reset-size9.size2,.katex .sizing.reset-size9.size2{font-size:.3472222222em}.katex .fontsize-ensurer.reset-size9.size3,.katex .sizing.reset-size9.size3{font-size:.4050925926em}.katex .fontsize-ensurer.reset-size9.size4,.katex .sizing.reset-size9.size4{font-size:.462962963em}.katex .fontsize-ensurer.reset-size9.size5,.katex .sizing.reset-size9.size5{font-size:.5208333333em}.katex .fontsize-ensurer.reset-size9.size6,.katex .sizing.reset-size9.size6{font-size:.5787037037em}.katex .fontsize-ensurer.reset-size9.size7,.katex .sizing.reset-size9.size7{font-size:.6944444444em}.katex .fontsize-ensurer.reset-size9.size8,.katex .sizing.reset-size9.size8{font-size:.8333333333em}.katex .fontsize-ensurer.reset-size9.size9,.katex .sizing.reset-size9.size9{font-size:1em}.katex .fontsize-ensurer.reset-size9.size10,.katex .sizing.reset-size9.size10{font-size:1.2002314815em}.katex .fontsize-ensurer.reset-size9.size11,.katex .sizing.reset-size9.size11{font-size:1.4398148148em}.katex .fontsize-ensurer.reset-size10.size1,.katex .sizing.reset-size10.size1{font-size:.2410800386em}.katex .fontsize-ensurer.reset-size10.size2,.katex .sizing.reset-size10.size2{font-size:.2892960463em}.katex .fontsize-ensurer.reset-size10.size3,.katex .sizing.reset-size10.size3{font-size:.337512054em}.katex .fontsize-ensurer.reset-size10.size4,.katex .sizing.reset-size10.size4{font-size:.3857280617em}.katex .fontsize-ensurer.reset-size10.size5,.katex .sizing.reset-size10.size5{font-size:.4339440694em}.katex .fontsize-ensurer.reset-size10.size6,.katex .sizing.reset-size10.size6{font-size:.4821600771em}.katex .fontsize-ensurer.reset-size10.size7,.katex .sizing.reset-size10.size7{font-size:.5785920926em}.katex .fontsize-ensurer.reset-size10.size8,.katex .sizing.reset-size10.size8{font-size:.6943105111em}.katex .fontsize-ensurer.reset-size10.size9,.katex .sizing.reset-size10.size9{font-size:.8331726133em}.katex .fontsize-ensurer.reset-size10.size10,.katex .sizing.reset-size10.size10{font-size:1em}.katex .fontsize-ensurer.reset-size10.size11,.katex .sizing.reset-size10.size11{font-size:1.1996142719em}.katex .fontsize-ensurer.reset-size11.size1,.katex .sizing.reset-size11.size1{font-size:.2009646302em}.katex .fontsize-ensurer.reset-size11.size2,.katex .sizing.reset-size11.size2{font-size:.2411575563em}.katex .fontsize-ensurer.reset-size11.size3,.katex .sizing.reset-size11.size3{font-size:.2813504823em}.katex .fontsize-ensurer.reset-size11.size4,.katex .sizing.reset-size11.size4{font-size:.3215434084em}.katex .fontsize-ensurer.reset-size11.size5,.katex .sizing.reset-size11.size5{font-size:.3617363344em}.katex .fontsize-ensurer.reset-size11.size6,.katex .sizing.reset-size11.size6{font-size:.4019292605em}.katex .fontsize-ensurer.reset-size11.size7,.katex .sizing.reset-size11.size7{font-size:.4823151125em}.katex .fontsize-ensurer.reset-size11.size8,.katex .sizing.reset-size11.size8{font-size:.578778135em}.katex .fontsize-ensurer.reset-size11.size9,.katex .sizing.reset-size11.size9{font-size:.6945337621em}.katex .fontsize-ensurer.reset-size11.size10,.katex .sizing.reset-size11.size10{font-size:.8336012862em}.katex .fontsize-ensurer.reset-size11.size11,.katex .sizing.reset-size11.size11{font-size:1em}.katex .delimsizing.size1{font-family:KaTeX_Size1}.katex .delimsizing.size2{font-family:KaTeX_Size2}.katex .delimsizing.size3{font-family:KaTeX_Size3}.katex .delimsizing.size4{font-family:KaTeX_Size4}.katex .delimsizing.mult .delim-size1>span{font-family:KaTeX_Size1}.katex .delimsizing.mult .delim-size4>span{font-family:KaTeX_Size4}.katex .nulldelimiter{display:inline-block;width:.12em}.katex .delimcenter,.katex .op-symbol{position:relative}.katex .op-symbol.small-op{font-family:KaTeX_Size1}.katex .op-symbol.large-op{font-family:KaTeX_Size2}.katex .accent>.vlist-t,.katex .op-limits>.vlist-t{text-align:center}.katex .accent .accent-body{position:relative}.katex .accent .accent-body:not(.accent-full){width:0}.katex .overlay{display:block}.katex .mtable .vertical-separator{display:inline-block;min-width:1px}.katex .mtable .arraycolsep{display:inline-block}.katex .mtable .col-align-c>.vlist-t{text-align:center}.katex .mtable .col-align-l>.vlist-t{text-align:left}.katex .mtable .col-align-r>.vlist-t{text-align:right}.katex .svg-align{text-align:left}.katex svg{fill:currentColor;stroke:currentColor;fill-rule:nonzero;fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;display:block;height:inherit;position:absolute;width:100%}.katex svg path{stroke:none}.katex img{border-style:none;max-height:none;max-width:none;min-height:0;min-width:0}.katex .stretchy{display:block;overflow:hidden;position:relative;width:100%}.katex .stretchy:after,.katex .stretchy:before{content:""}.katex .hide-tail{overflow:hidden;position:relative;width:100%}.katex .halfarrow-left{left:0;overflow:hidden;position:absolute;width:50.2%}.katex .halfarrow-right{overflow:hidden;position:absolute;right:0;width:50.2%}.katex .brace-left{left:0;overflow:hidden;position:absolute;width:25.1%}.katex .brace-center{left:25%;overflow:hidden;position:absolute;width:50%}.katex .brace-right{overflow:hidden;position:absolute;right:0;width:25.1%}.katex .x-arrow-pad{padding:0 .5em}.katex .cd-arrow-pad{padding:0 .55556em 0 .27778em}.katex .mover,.katex .munder,.katex .x-arrow{text-align:center}.katex .boxpad{padding:0 .3em}.katex .fbox,.katex .fcolorbox{border:.04em solid;box-sizing:border-box}.katex .cancel-pad{padding:0 .2em}.katex .cancel-lap{margin-left:-.2em;margin-right:-.2em}.katex .sout{border-bottom-style:solid;border-bottom-width:.08em}.katex .angl{border-right:.049em solid;border-top:.049em solid;box-sizing:border-box;margin-right:.03889em}.katex .anglpad{padding:0 .03889em}.katex .eqn-num:before{content:"(" counter(katexEqnNo) ")";counter-increment:katexEqnNo}.katex .mml-eqn-num:before{content:"(" counter(mmlEqnNo) ")";counter-increment:mmlEqnNo}.katex .mtr-glue{width:50%}.katex .cd-vert-arrow{display:inline-block;position:relative}.katex .cd-label-left{display:inline-block;position:absolute;right:calc(50% + .3em);text-align:left}.katex .cd-label-right{display:inline-block;left:calc(50% + .3em);position:absolute;text-align:right}.katex-display{display:block;margin:1em 0;text-align:center}.katex-display>.katex{display:block;text-align:center;white-space:nowrap}.katex-display>.katex>.katex-html{display:block;position:relative}.katex-display>.katex>.katex-html>.tag{position:absolute;right:0}.katex-display.leqno>.katex>.katex-html>.tag{left:0;right:auto}.katex-display.fleqn>.katex{padding-left:2em;text-align:left}body{counter-reset:katexEqnNo mmlEqnNo}

