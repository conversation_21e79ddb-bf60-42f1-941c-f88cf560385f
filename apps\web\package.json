{"name": "@opencanvas/web", "author": "<PERSON><PERSON>", "homepage": "https://opencanvas.langchain.com", "repository": "https://github.com/langchain-ai/open-canvas", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --config .prettierrc --write \"src\"", "format:check": "prettier --config .prettierrc --check \"src\"", "eval": "vitest run --config ls.vitest.config.ts", "eval:highlights": "yarn tsx evals/highlights.ts"}, "dependencies": {"@assistant-ui/react": "^0.7.68", "@assistant-ui/react-markdown": "^0.7.2", "@assistant-ui/react-syntax-highlighter": "^0.7.2", "@blocknote/core": "^0.17.1", "@blocknote/mantine": "^0.17.1", "@blocknote/react": "^0.17.1", "@blocknote/shadcn": "^0.17.1", "@codemirror/lang-cpp": "^6.0.2", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-java": "^6.0.1", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-php": "^6.0.1", "@codemirror/lang-python": "^6.1.6", "@codemirror/lang-rust": "^6.0.1", "@codemirror/lang-sql": "^6.8.0", "@codemirror/lang-xml": "^6.1.0", "@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@langchain/community": "^0.3.28", "@langchain/core": "^0.3.71", "@langchain/langgraph-sdk": "^0.0.107", "@mendable/firecrawl-js": "1.10.1", "@nextjournal/lang-clojure": "^1.0.0", "@opencanvas/shared": "*", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.7", "@replit/codemirror-lang-csharp": "^6.2.0", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.45.5", "@types/react-syntax-highlighter": "^15.5.13", "@uiw/react-codemirror": "^4.23.5", "@uiw/react-md-editor": "^4.0.4", "@vercel/kv": "^2.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "eslint-plugin-unused-imports": "^4.1.4", "framer-motion": "^11.11.9", "groq-sdk": "^0.13.0", "js-cookie": "^3.0.5", "langsmith": "^0.3.60", "lodash": "^4.17.21", "lucide-react": "^0.474.0", "next": "14.2.25", "nuqs": "^2.4.1", "pdf-parse": "^1.1.1", "react": "^18", "react-colorful": "^5.6.1", "react-dom": "^18", "react-icons": "^5.3.0", "react-json-view": "^1.21.3", "react-markdown": "^9.0.1", "react-resizable-panels": "^2.1.7", "react-syntax-highlighter": "^15.5.0", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "tailwind-merge": "^2.5.2", "tailwind-scrollbar-hide": "^1.1.7", "tailwindcss-animate": "^1.0.7", "uuid": "^10.0.0", "zod": "^3.23.8", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.12.0", "@types/eslint__js": "^8.42.3", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.12", "@types/node": "^20", "@types/pdf-parse": "^1.1.4", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.12.2", "@typescript-eslint/parser": "^8.8.1", "eslint": "^8", "eslint-config-next": "14.2.10", "postcss": "^8", "prettier": "^3.3.3", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.1", "tsx": "^4.19.1", "turbo": "latest", "typescript": "^5", "typescript-eslint": "^8.8.1", "vitest": "^3.0.4"}}