import React from "react";
import { WordRenderer } from "./WordRenderer";

export interface WordArtifactRendererProps {
  chatCollapsed: boolean;
  setChatCollapsed: (c: boolean) => void;
}

export const WordArtifactRenderer: React.FC<WordArtifactRendererProps> = ({
  chatCollapsed,
  setChatCollapsed,
}) => {
  return (
    <div className="relative w-full h-full max-h-screen overflow-auto">
      <div className="flex justify-center h-full">
        <div className="relative min-h-full min-w-full">
          <WordRenderer 
            chatCollapsed={chatCollapsed}
            setChatCollapsed={setChatCollapsed}
          />
        </div>
      </div>
    </div>
  );
};