import { ProgrammingLanguageOptions } from "@opencanvas/shared/types";
import { ThreadPrimitive, useThreadRuntime } from "@assistant-ui/react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { FC, useMemo } from "react";
import { TighterText } from "../ui/header";
import { NotebookPen, FileText } from "lucide-react";
import { ProgrammingLanguagesDropdown } from "../ui/programming-lang-dropdown";
import { Button } from "../ui/button";
import { useLanguage } from "@/contexts/LanguageContext";

// const QUICK_START_PROMPTS_SEARCH = [
//   "Write a market analysis of AI chip manufacturers in 2025",
//   "Create a blog post about the latest climate change policies and their impact",
//   "Draft an investor update on renewable energy trends this quarter",
//   "Write a report on current cybersecurity threats in cloud computing",
//   "Analyze the latest developments in quantum computing for a tech newsletter",
//   "Create a summary of emerging medical breakthroughs in cancer treatment",
//   "Write about the impact of current interest rates on the housing market",
//   "Draft an article about breakthroughs in battery technology this year",
//   "Analyze current supply chain disruptions in semiconductor manufacturing",
//   "Write about how recent AI regulations affect business innovation",
// ];

// const QUICK_START_PROMPTS = [
//   "Write a bedtime story about a brave little robot",
//   "Create a function to calculate Fibonacci numbers in TypeScript",
//   "Draft a resignation letter for a position I've had for 2 years",
//   "Build a simple weather dashboard using React and Tailwind",
//   "Write a poem about artificial intelligence",
//   "Create a basic Express.js REST API with two endpoints",
//   "Draft a congratulatory speech for my sister's graduation",
//   "Build a command-line calculator in Python",
//   "Write instructions for making perfect scrambled eggs",
//   "Create a simple snake game using HTML canvas",
//   "Write me a TODO app in React",
//   "Explain why the sky is blue in a short essay",
//   "Help me draft an email to my professor Craig",
//   "Write a web scraping program in Python",
// ];

function getRandomPrompts(prompts: string[], count: number = 4): string[] {
  return [...prompts].sort(() => Math.random() - 0.5).slice(0, count);
}

interface QuickStartButtonsProps {
  handleQuickStart: (
    type: "text" | "code" | "word",
    language?: ProgrammingLanguageOptions
  ) => void;
  composer: React.ReactNode;
  searchEnabled: boolean;
}

interface QuickStartPromptsProps {
  searchEnabled: boolean;
}

const QuickStartPrompts = ({ searchEnabled }: QuickStartPromptsProps) => {
  const { t } = useLanguage();
  const threadRuntime = useThreadRuntime();

  const handleClick = (text: string) => {
    threadRuntime.append({
      role: "user",
      content: [{ type: "text", text }],
    });
  };

  const getTranslatedPrompts = () => {
    const translatedPrompts = [
      t("prompt.bedtimeStory"),
      t("prompt.fibonacci"),
      t("prompt.resignationLetter"),
      t("prompt.weatherDashboard"),
      t("prompt.aiPoem"),
      t("prompt.expressApi"),
      t("prompt.graduationSpeech"),
      t("prompt.pythonCalculator"),
      t("prompt.scrambledEggs"),
      t("prompt.snakeGame"),
      t("prompt.todoApp"),
      t("prompt.whySkyBlue"),
      t("prompt.emailProfessor"),
      t("prompt.webScraping"),
    ];

    const translatedSearchPrompts = [
      t("prompt.aiChipAnalysis"),
      t("prompt.climatePolicy"),
      t("prompt.renewableEnergy"),
      t("prompt.cybersecurity"),
      t("prompt.quantumComputing"),
      t("prompt.medicalBreakthroughs"),
      t("prompt.housingMarket"),
      t("prompt.batteryTech"),
      t("prompt.supplyChain"),
      t("prompt.aiRegulations"),
    ];

    return searchEnabled ? translatedSearchPrompts : translatedPrompts;
  };

  const selectedPrompts = useMemo(
    () => getRandomPrompts(getTranslatedPrompts()),
    [searchEnabled, t]
  );

  return (
    <div className="flex flex-col w-full gap-2">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 w-full">
        {selectedPrompts.map((prompt, index) => (
          <Button
            key={`quick-start-prompt-${index}`}
            onClick={() => handleClick(prompt)}
            variant="outline"
            className="min-h-[60px] w-full flex items-center justify-center p-6 whitespace-normal text-gray-500 hover:text-gray-700 transition-colors ease-in rounded-2xl"
          >
            <p className="text-center break-words text-sm font-normal">
              {prompt}
            </p>
          </Button>
        ))}
      </div>
    </div>
  );
};

const QuickStartButtons = (props: QuickStartButtonsProps) => {
  const { t } = useLanguage();
  const handleLanguageSubmit = (language: ProgrammingLanguageOptions) => {
    props.handleQuickStart("code", language);
  };

  return (
    <div className="flex flex-col gap-8 items-center justify-center w-full">
      <div className="flex flex-col gap-6">
        <p className="text-gray-600 text-sm">{t("welcome.startBlankCanvas")}</p>
        <div className="flex flex-row gap-1 items-center justify-center w-full">
          <Button
            variant="outline"
            className="text-gray-500 hover:text-gray-700 transition-colors ease-in rounded-2xl flex items-center justify-center gap-2 w-[250px] h-[64px]"
            onClick={() => props.handleQuickStart("text")}
          >
            {t("welcome.newMarkdown")}
            <NotebookPen />
          </Button>
          <ProgrammingLanguagesDropdown handleSubmit={handleLanguageSubmit} />
          <Button
            variant="outline"
            className="text-gray-500 hover:text-gray-700 transition-colors ease-in rounded-2xl flex items-center justify-center gap-2 w-[250px] h-[64px]"
            onClick={() => props.handleQuickStart("word")}
          >
            {t("welcome.newWord")}
            <FileText />
          </Button>
        </div>
      </div>
      <div className="flex flex-col gap-6 mt-2 w-full">
        <p className="text-gray-600 text-sm">{t("welcome.orWithMessage")}</p>
        {props.composer}
        <QuickStartPrompts searchEnabled={props.searchEnabled} />
      </div>
    </div>
  );
};

interface ThreadWelcomeProps {
  handleQuickStart: (
    type: "text" | "code" | "word",
    language?: ProgrammingLanguageOptions
  ) => void;
  composer: React.ReactNode;
  searchEnabled: boolean;
}

export const ThreadWelcome: FC<ThreadWelcomeProps> = (
  props: ThreadWelcomeProps
) => {
  return (
    <ThreadPrimitive.Empty>
      <div className="flex items-center justify-center mt-16 w-full">
        <div className="text-center max-w-3xl w-full">
          <Avatar className="mx-auto">
            <AvatarImage src="/lc_logo.jpg" alt="LangChain Logo" />
            <AvatarFallback>LC</AvatarFallback>
          </Avatar>
          <TighterText className="mt-4 text-lg font-medium">
            Hello
          </TighterText>
          <div className="mt-8 w-full">
            <QuickStartButtons
              composer={props.composer}
              handleQuickStart={props.handleQuickStart}
              searchEnabled={props.searchEnabled}
            />
          </div>
        </div>
      </div>
    </ThreadPrimitive.Empty>
  );
};
