// import packageJson from '../../../../../../../apps/web/package.json'
const version = '0.0.1'
import { Draw } from '../draw/Draw'
import { ICatalog } from '../../interface/Catalog'
import { IEditorResult } from '../../interface/Editor'
import { IGetValueOption } from '../../interface/Draw'
import { deepClone } from '../../utils'
import { processWordCount } from './works/wordCount'
import { processCatalog } from './works/catalog'
import { processGroupIds } from './works/group'
import { processValue } from './works/value'

export class WorkerManager {
  private draw: Draw

  constructor(draw: Draw) {
    this.draw = draw
  }

  public async getWordCount(): Promise<number> {
    const elementList = this.draw.getOriginalMainElementList()
    return processWordCount(elementList)
  }

  public async getCatalog(): Promise<ICatalog | null> {
    const elementList = this.draw.getOriginalMainElementList()
    const positionList = this.draw.getPosition().getOriginalMainPositionList()
    return processCatalog({
      elementList,
      positionList
    })
  }

  public async getGroupIds(): Promise<string[]> {
    const elementList = this.draw.getOriginalMainElementList()
    return processGroupIds(elementList)
  }

  public async getValue(options?: IGetValueOption): Promise<IEditorResult> {
    const data = await processValue({
      data: this.draw.getOriginValue(options),
      options
    })
    return {
      version,
      data,
      options: deepClone(this.draw.getOptions())
    }
  }
}