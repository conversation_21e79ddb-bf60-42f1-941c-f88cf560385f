import { IGetValueOption } from '../../../interface/Draw'
import { IEditorData } from '../../../interface/Editor'
import { zipElementList } from '../../../utils/element'

interface IGetValueWorkerOption {
  data: Required<IEditorData>
  options: IGetValueOption
}

// 导出函数供直接调用
export function processValue(payload: IGetValueWorkerOption): IEditorData {
  const { options, data } = payload
  const { extraPickAttrs = [] } = options || {}

  return {
    header: zipElementList(data.header, {
      extraPickAttrs,
      isClone: false
    }),
    main: zipElementList(data.main, {
      extraPickAttrs,
      isClassifyArea: true,
      isClone: false
    }),
    footer: zipElementList(data.footer, {
      extraPickAttrs,
      isClone: false
    })
  }
}
