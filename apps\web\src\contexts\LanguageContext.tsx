"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { getCookie, setCookie } from "@/lib/cookies";

export type Language = "zh" | "en";

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

const LANGUAGE_COOKIE_KEY = "oc_language";

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguageState] = useState<Language>("zh"); // 默认中文

  useEffect(() => {
    // 从cookie中读取语言设置
    const savedLanguage = getCookie(LANGUAGE_COOKIE_KEY) as Language;
    if (savedLanguage && (savedLanguage === "zh" || savedLanguage === "en")) {
      setLanguageState(savedLanguage);
    }
  }, []);

  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
    setCookie(LANGUAGE_COOKIE_KEY, lang);
  };

  const t = (key: string): string => {
    return getTranslation(key, language);
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
}

// 翻译映射
const translations: Record<string, Record<Language, string>> = {
  // 模型选择器
  "model.openai": {
    zh: "OpenAI",
    en: "OpenAI",
  },
  "model.glm": {
    zh: "GLM",
    en: "GLM",
  },
  "model.anthropic": {
    zh: "Anthropic",
    en: "Anthropic",
  },
  "model.gemini": {
    zh: "Gemini",
    en: "Gemini",
  },
  "model.fireworks": {
    zh: "Fireworks",
    en: "Fireworks",
  },
  "model.groq": {
    zh: "Groq",
    en: "Groq",
  },
  "model.ollama": {
    zh: "Ollama",
    en: "Ollama",
  },
  "model.azure": {
    zh: "Azure OpenAI",
    en: "Azure OpenAI",
  },

  // 欢迎页面
  "welcome.startBlankCanvas": {
    zh: "从空白画布开始",
    en: "Start with a blank canvas",
  },
  "welcome.newMarkdown": {
    zh: "新建 Markdown",
    en: "New Markdown",
  },
  "welcome.newCodeFile": {
    zh: "新建代码文件",
    en: "New Code File",
  },
  "welcome.orWithMessage": {
    zh: "或者发送消息",
    en: "or with a message",
  },
  "welcome.languages": {
    zh: "编程语言",
    en: "Languages",
  },

  // 工具栏
  "toolbar.addComments": {
    zh: "添加注释",
    en: "Add comments",
  },
  "toolbar.addLogs": {
    zh: "添加日志",
    en: "Add logs",
  },
  "toolbar.portLanguage": {
    zh: "转换语言",
    en: "Port language",
  },
  "toolbar.fixBugs": {
    zh: "修复错误",
    en: "Fix bugs",
  },
  "toolbar.translate": {
    zh: "翻译",
    en: "Translate",
  },

  // 语言切换
  "language.switch": {
    zh: "切换语言",
    en: "Switch Language",
  },
  "language.chinese": {
    zh: "中文",
    en: "Chinese",
  },
  "language.english": {
    zh: "English",
    en: "English",
  },

  // 编程语言
  "lang.php": {
    zh: "PHP",
    en: "PHP",
  },
  "lang.typescript": {
    zh: "TypeScript",
    en: "TypeScript",
  },
  "lang.javascript": {
    zh: "JavaScript",
    en: "JavaScript",
  },
  "lang.cpp": {
    zh: "C++",
    en: "C++",
  },
  "lang.java": {
    zh: "Java",
    en: "Java",
  },
  "lang.python": {
    zh: "Python",
    en: "Python",
  },
  "lang.html": {
    zh: "HTML",
    en: "HTML",
  },
  "lang.sql": {
    zh: "SQL",
    en: "SQL",
  },

  // 错误信息
  "error.languageNotSelected": {
    zh: "未选择语言",
    en: "Language not selected",
  },
  "error.selectLanguageToContinue": {
    zh: "请选择一种语言以继续",
    en: "Please select a language to continue",
  },
  "error.portLanguageError": {
    zh: "转换语言错误",
    en: "Port language error",
  },
  "error.codeAlreadyIn": {
    zh: "代码已经是",
    en: "The code is already in",
  },

  // 模型配置
  "config.temperature": {
    zh: "温度",
    en: "Temperature",
  },
  "config.temperatureDesc": {
    zh: "控制创造性 - 较低值产生更专注的输出，较高值产生更多样化和想象力的输出。",
    en: "Controls creativity - lower for focused outputs, higher for more variety and imagination.",
  },
  "config.maxTokens": {
    zh: "最大令牌数",
    en: "Max tokens",
  },
  "config.maxTokensDesc": {
    zh: "设置AI响应的长度 - 更多令牌意味着更长、更详细的响应。",
    en: "Set how long the AI's response can be - more tokens mean longer, more detailed responses.",
  },
  "config.resetToDefaults": {
    zh: "重置为默认值",
    en: "Reset to Defaults",
  },

  // 翻译选项
  "translate.english": {
    zh: "英语",
    en: "English",
  },
  "translate.mandarin": {
    zh: "中文",
    en: "Mandarin",
  },
  "translate.hindi": {
    zh: "印地语",
    en: "Hindi",
  },
  "translate.spanish": {
    zh: "西班牙语",
    en: "Spanish",
  },
  "translate.french": {
    zh: "法语",
    en: "French",
  },

  // 通用UI文本
  "common.loading": {
    zh: "加载中...",
    en: "Loading...",
  },
  "common.save": {
    zh: "保存",
    en: "Save",
  },
  "common.cancel": {
    zh: "取消",
    en: "Cancel",
  },
  "common.confirm": {
    zh: "确认",
    en: "Confirm",
  },
  "common.delete": {
    zh: "删除",
    en: "Delete",
  },
  "common.edit": {
    zh: "编辑",
    en: "Edit",
  },
  "common.copy": {
    zh: "复制",
    en: "Copy",
  },
  "common.paste": {
    zh: "粘贴",
    en: "Paste",
  },
  "common.search": {
    zh: "搜索",
    en: "Search",
  },
  "common.settings": {
    zh: "设置",
    en: "Settings",
  },
  "common.help": {
    zh: "帮助",
    en: "Help",
  },
  "common.close": {
    zh: "关闭",
    en: "Close",
  },

  // 应用标题和描述
  "app.title": {
    zh: "Open Canvas",
    en: "Open Canvas",
  },
  "app.description": {
    zh: "LangChain 开放画布聊天界面",
    en: "Open Canvas Chat UX by LangChain",
  },

  // 聊天界面
  "chat.placeholder": {
    zh: "输入消息...",
    en: "Type a message...",
  },
  "chat.send": {
    zh: "发送",
    en: "Send",
  },
  "chat.newChat": {
    zh: "新建对话",
    en: "New Chat",
  },
  "chat.clearHistory": {
    zh: "清除历史",
    en: "Clear History",
  },
  "chat.history": {
    zh: "聊天历史",
    en: "Chat History",
  },
  "chat.noHistory": {
    zh: "历史记录中没有找到项目。",
    en: "No items found in history.",
  },

  // 欢迎页面消息
  "welcome.whatToWrite": {
    zh: "您今天想写什么？",
    en: "What would you like to write today?",
  },

  // 输入占位符
  "placeholder.shareIdea": {
    zh: "分享您的想法，让我们一起写出精彩内容",
    en: "Share your big idea and let's write something amazing",
  },
  "placeholder.typeVision": {
    zh: "输入您对下一个优秀内容的构想",
    en: "Type your vision for the next great piece of content",
  },
  "placeholder.masterpiece": {
    zh: "您的杰作从这个提示开始",
    en: "Your masterpiece begins with this prompt",
  },
  "placeholder.whatToWrite": {
    zh: "您今天想让我们写什么？",
    en: "What would you like us to write about today?",
  },
  "placeholder.dropIdea": {
    zh: "在这里输入您的内容想法，让我们一起创作",
    en: "Drop your content idea here and let's create",
  },
  "placeholder.nextGreat": {
    zh: "您的下一个杰作从这个提示开始",
    en: "Your next great piece starts with this prompt",
  },
  "placeholder.shareStory": {
    zh: "分享您的故事想法，看它如何展开",
    en: "Share your story idea and watch it unfold",
  },
  "placeholder.writeIncredible": {
    zh: "让我们写出令人惊叹的内容 - 从这里开始",
    en: "Let's write something incredible - start here",
  },
  "placeholder.writingJourney": {
    zh: "您的写作之旅从这个提示开始",
    en: "Your writing journey begins with this prompt",
  },
  "placeholder.contentMagic": {
    zh: "将您的想法变成内容魔法 - 从这里开始",
    en: "Turn your idea into content magic - start here",
  },

  // 搜索占位符
  "placeholder.shareTopicData": {
    zh: "分享您的主题 - 我会添加实时数据",
    en: "Share your topic - I'll add live data",
  },
  "placeholder.writeAnything": {
    zh: "写任何内容 - 我会找到相关资源",
    en: "Write about anything - I'll find sources",
  },
  "placeholder.ideaResearch": {
    zh: "您的想法 + 新鲜研究 = 优秀内容",
    en: "Your idea + fresh research = great content",
  },
  "placeholder.startFacts": {
    zh: "从这里开始，获取实时事实",
    en: "Start here with real-time facts",
  },
  "placeholder.topicData": {
    zh: "在此输入主题，获取数据丰富的内容",
    en: "Topic here for data-rich content",
  },
  "placeholder.currentInsights": {
    zh: "使用当前洞察创作",
    en: "Create with current insights",
  },
  "placeholder.writeLive": {
    zh: "使用实时资源立即写作",
    en: "Write now with live sources",
  },
  "placeholder.storyData": {
    zh: "您的故事 + 新鲜数据",
    en: "Your story + fresh data",
  },
  "placeholder.ideasReady": {
    zh: "欢迎想法 - 研究就绪",
    en: "Ideas welcome - research ready",
  },
  "placeholder.startFresh": {
    zh: "从实时事实开始新创作",
    en: "Start fresh with live facts",
  },

  // 工具提示
  "tooltip.send": {
    zh: "发送",
    en: "Send",
  },
  "tooltip.history": {
    zh: "历史记录",
    en: "History",
  },
  "tooltip.webSearch": {
    zh: "网络搜索",
    en: "Web search",
  },

  // 文件操作
  "file.upload": {
    zh: "上传文件",
    en: "Upload File",
  },
  "file.download": {
    zh: "下载文件",
    en: "Download File",
  },
  "file.export": {
    zh: "导出",
    en: "Export",
  },
  "file.import": {
    zh: "导入",
    en: "Import",
  },

  // 快速开始提示
  "prompt.bedtimeStory": {
    zh: "写一个关于勇敢小机器人的睡前故事",
    en: "Write a bedtime story about a brave little robot",
  },
  "prompt.fibonacci": {
    zh: "创建一个用TypeScript计算斐波那契数列的函数",
    en: "Create a function to calculate Fibonacci numbers in TypeScript",
  },
  "prompt.resignationLetter": {
    zh: "为我工作了2年的职位起草一份辞职信",
    en: "Draft a resignation letter for a position I've had for 2 years",
  },
  "prompt.weatherDashboard": {
    zh: "使用React和Tailwind构建一个简单的天气仪表板",
    en: "Build a simple weather dashboard using React and Tailwind",
  },
  "prompt.aiPoem": {
    zh: "写一首关于人工智能的诗",
    en: "Write a poem about artificial intelligence",
  },
  "prompt.expressApi": {
    zh: "创建一个带有两个端点的基本Express.js REST API",
    en: "Create a basic Express.js REST API with two endpoints",
  },
  "prompt.graduationSpeech": {
    zh: "为我姐姐的毕业典礼起草一份祝贺演讲",
    en: "Draft a congratulatory speech for my sister's graduation",
  },
  "prompt.pythonCalculator": {
    zh: "用Python构建一个命令行计算器",
    en: "Build a command-line calculator in Python",
  },
  "prompt.scrambledEggs": {
    zh: "写制作完美炒蛋的说明",
    en: "Write instructions for making perfect scrambled eggs",
  },
  "prompt.snakeGame": {
    zh: "使用HTML canvas创建一个简单的贪吃蛇游戏",
    en: "Create a simple snake game using HTML canvas",
  },
  "prompt.todoApp": {
    zh: "用React写一个TODO应用",
    en: "Write me a TODO app in React",
  },
  "prompt.whySkyBlue": {
    zh: "在一篇短文中解释为什么天空是蓝色的",
    en: "Explain why the sky is blue in a short essay",
  },
  "prompt.emailProfessor": {
    zh: "帮我给我的教授Craig起草一封邮件",
    en: "Help me draft an email to my professor Craig",
  },
  "prompt.webScraping": {
    zh: "用Python写一个网页抓取程序",
    en: "Write a web scraping program in Python",
  },

  // 搜索提示
  "prompt.aiChipAnalysis": {
    zh: "写一份2025年AI芯片制造商的市场分析",
    en: "Write a market analysis of AI chip manufacturers in 2025",
  },
  "prompt.climatePolicy": {
    zh: "创建一篇关于最新气候变化政策及其影响的博客文章",
    en: "Create a blog post about the latest climate change policies and their impact",
  },
  "prompt.renewableEnergy": {
    zh: "起草一份关于本季度可再生能源趋势的投资者更新",
    en: "Draft an investor update on renewable energy trends this quarter",
  },
  "prompt.cybersecurity": {
    zh: "写一份关于云计算中当前网络安全威胁的报告",
    en: "Write a report on current cybersecurity threats in cloud computing",
  },
  "prompt.quantumComputing": {
    zh: "为技术通讯分析量子计算的最新发展",
    en: "Analyze the latest developments in quantum computing for a tech newsletter",
  },
  "prompt.medicalBreakthroughs": {
    zh: "创建癌症治疗新兴医学突破的摘要",
    en: "Create a summary of emerging medical breakthroughs in cancer treatment",
  },
  "prompt.housingMarket": {
    zh: "写关于当前利率对房地产市场影响的文章",
    en: "Write about the impact of current interest rates on the housing market",
  },
  "prompt.batteryTech": {
    zh: "起草一篇关于今年电池技术突破的文章",
    en: "Draft an article about breakthroughs in battery technology this year",
  },
  "prompt.supplyChain": {
    zh: "分析半导体制造业当前的供应链中断",
    en: "Analyze current supply chain disruptions in semiconductor manufacturing",
  },
  "prompt.aiRegulations": {
    zh: "写关于最近的AI法规如何影响商业创新",
    en: "Write about how recent AI regulations affect business innovation",
  },
};

function getTranslation(key: string, language: Language): string {
  const translation = translations[key];
  if (!translation) {
    console.warn(`Translation key "${key}" not found`);
    return key;
  }
  return translation[language] || key;
}
